#include <iostream>
#include <filesystem>
#include <vector>
#include <string>
#include <windows.h>
#include <shlobj.h>
#include "sqlite3.h"

namespace fs = std::filesystem;

class FirefoxDiagnostic {
public:
    void RunDiagnostic() {
        std::wcout << L"=== Firefox 诊断工具 ===" << std::endl;
        
        // 1. 检查Firefox配置文件路径
        CheckFirefoxProfiles();
        
        // 2. 检查Cookie数据库
        CheckCookieDatabase();
        
        // 3. 检查书签数据库
        CheckBookmarkDatabase();
    }

private:
    std::wstring m_profilePath;
    
    void CheckFirefoxProfiles() {
        std::wcout << L"\n1. 检查Firefox配置文件..." << std::endl;
        
        WCHAR localAppData[MAX_PATH];
        if (FAILED(SHGetFolderPathW(NULL, CSIDL_LOCAL_APPDATA, NULL, 0, localAppData))) {
            std::wcout << L"无法获取LocalAppData路径" << std::endl;
            return;
        }
        
        std::wcout << L"LocalAppData路径: " << localAppData << std::endl;
        
        std::vector<std::wstring> firefox_versions = {
            L"Mozilla\\Firefox",
            L"Mozilla Firefox", 
            L"Firefox"
        };
        
        for (const auto& version : firefox_versions) {
            fs::path profilesPath = fs::path(localAppData) / version / L"Profiles";
            std::wcout << L"检查路径: " << profilesPath.wstring() << std::endl;
            
            if (!fs::exists(profilesPath)) {
                std::wcout << L"  路径不存在" << std::endl;
                continue;
            }
            
            std::wcout << L"  找到Profiles文件夹!" << std::endl;
            
            try {
                for (const auto& entry : fs::directory_iterator(profilesPath)) {
                    if (entry.is_directory()) {
                        std::wstring profileName = entry.path().filename().wstring();
                        std::wcout << L"  配置文件: " << profileName << std::endl;
                        
                        // 检查关键文件
                        fs::path cookiesPath = entry.path() / L"cookies.sqlite";
                        fs::path placesPath = entry.path() / L"places.sqlite";
                        fs::path loginsPath = entry.path() / L"logins.json";
                        
                        std::wcout << L"    cookies.sqlite: " << (fs::exists(cookiesPath) ? L"存在" : L"不存在") << std::endl;
                        std::wcout << L"    places.sqlite: " << (fs::exists(placesPath) ? L"存在" : L"不存在") << std::endl;
                        std::wcout << L"    logins.json: " << (fs::exists(loginsPath) ? L"存在" : L"不存在") << std::endl;
                        
                        if (fs::exists(cookiesPath) || fs::exists(placesPath)) {
                            m_profilePath = entry.path().wstring();
                            std::wcout << L"  使用配置文件: " << m_profilePath << std::endl;
                            return;
                        }
                    }
                }
            }
            catch (const std::exception& e) {
                std::wcout << L"  遍历配置文件时出错: " << e.what() << std::endl;
            }
        }
        
        if (m_profilePath.empty()) {
            std::wcout << L"未找到有效的Firefox配置文件" << std::endl;
        }
    }
    
    void CheckCookieDatabase() {
        std::wcout << L"\n2. 检查Cookie数据库..." << std::endl;
        
        if (m_profilePath.empty()) {
            std::wcout << L"没有配置文件路径，跳过Cookie检查" << std::endl;
            return;
        }
        
        fs::path cookiesPath = fs::path(m_profilePath) / L"cookies.sqlite";
        if (!fs::exists(cookiesPath)) {
            std::wcout << L"cookies.sqlite不存在: " << cookiesPath.wstring() << std::endl;
            return;
        }
        
        std::wcout << L"找到cookies.sqlite: " << cookiesPath.wstring() << std::endl;
        
        // 检查数据库结构
        sqlite3* db = nullptr;
        int rc = sqlite3_open16(cookiesPath.c_str(), &db);
        if (rc != SQLITE_OK) {
            std::wcout << L"无法打开cookies.sqlite: " << sqlite3_errmsg(db) << std::endl;
            if (db) sqlite3_close(db);
            return;
        }
        
        // 检查表结构
        const char* sql = "PRAGMA table_info(moz_cookies);";
        sqlite3_stmt* stmt = nullptr;
        rc = sqlite3_prepare_v2(db, sql, -1, &stmt, nullptr);
        if (rc == SQLITE_OK) {
            std::wcout << L"moz_cookies表结构:" << std::endl;
            while (sqlite3_step(stmt) == SQLITE_ROW) {
                const char* name = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 1));
                const char* type = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 2));
                std::wcout << L"  " << name << L" (" << type << L")" << std::endl;
            }
            sqlite3_finalize(stmt);
        }
        
        // 检查Cookie数量
        const char* countSql = "SELECT COUNT(*) FROM moz_cookies;";
        rc = sqlite3_prepare_v2(db, countSql, -1, &stmt, nullptr);
        if (rc == SQLITE_OK) {
            if (sqlite3_step(stmt) == SQLITE_ROW) {
                int count = sqlite3_column_int(stmt, 0);
                std::wcout << L"Cookie总数: " << count << std::endl;
            }
            sqlite3_finalize(stmt);
        }
        
        sqlite3_close(db);
    }
    
    void CheckBookmarkDatabase() {
        std::wcout << L"\n3. 检查书签数据库..." << std::endl;
        
        if (m_profilePath.empty()) {
            std::wcout << L"没有配置文件路径，跳过书签检查" << std::endl;
            return;
        }
        
        fs::path placesPath = fs::path(m_profilePath) / L"places.sqlite";
        if (!fs::exists(placesPath)) {
            std::wcout << L"places.sqlite不存在: " << placesPath.wstring() << std::endl;
            return;
        }
        
        std::wcout << L"找到places.sqlite: " << placesPath.wstring() << std::endl;
        
        // 检查数据库结构
        sqlite3* db = nullptr;
        int rc = sqlite3_open16(placesPath.c_str(), &db);
        if (rc != SQLITE_OK) {
            std::wcout << L"无法打开places.sqlite: " << sqlite3_errmsg(db) << std::endl;
            if (db) sqlite3_close(db);
            return;
        }
        
        // 检查书签数量
        const char* countSql = "SELECT COUNT(*) FROM moz_bookmarks WHERE type = 1;";
        sqlite3_stmt* stmt = nullptr;
        rc = sqlite3_prepare_v2(db, countSql, -1, &stmt, nullptr);
        if (rc == SQLITE_OK) {
            if (sqlite3_step(stmt) == SQLITE_ROW) {
                int count = sqlite3_column_int(stmt, 0);
                std::wcout << L"书签总数: " << count << std::endl;
            }
            sqlite3_finalize(stmt);
        }
        
        sqlite3_close(db);
    }
};

int main() {
    SetConsoleOutputCP(65001);
    
    FirefoxDiagnostic diagnostic;
    diagnostic.RunDiagnostic();
    
    std::wcout << L"\n按任意键退出..." << std::endl;
    std::cin.get();
    
    return 0;
}
