﻿#pragma once
#include "WiFiData.h"
#include <vector>
#include <string>
#include <functional>
#include <map>
#include <windows.h>
#include <nlohmann/json.hpp>

// 条件包含不同的WiFi API头文件
#ifndef WINDOWS_XP_SUPPORT
    #include <wlanapi.h>
#endif

// Windows XP WZC API 结构定义（避免直接包含wzcsapi.h以提高兼容性）
#ifdef WINDOWS_XP_SUPPORT
    // WZC API 常量和结构定义
    #define INTF_ALL                    0xFFFFFFFF
    #define INTF_ALL_FLAGS              0x0001FFFF
    #define INTF_ENABLED                0x00000001
    #define INTF_CONNECTED              0x00000002

    typedef struct _INTF_ENTRY {
        GUID    guidIntf;
        WCHAR   wszDescr[256];
        ULONG   ulMediaState;
        ULONG   ulMediaType;
        ULONG   ulPhysicalMediaType;
        int     nInfraMode;
        int     nAuthMode;
        int     nWepStatus;
        DWORD   dwCtlFlags;
        DWORD   dwCapabilities;
        // 简化结构，只包含必要字段
    } INTF_ENTRY, *PINTF_ENTRY;

    typedef struct _INTFS_KEY_TABLE {
        DWORD       dwNumIntfs;
        PINTF_ENTRY pIntfs;
    } INTFS_KEY_TABLE, *PINTFS_KEY_TABLE;
#endif



class WiFiManager {
public:
    WiFiManager();
    ~WiFiManager();

    // 初始化WiFi管理器
    bool Initialize();

    // 清理资源
    void Cleanup();

    // 获取所有WiFi配置文件
    std::vector<WiFiData> GetAllWiFiProfiles();

    // 获取完整的WiFi信息并返回JSON格式
    nlohmann::json GetWiFiInfoAsJson();

 
    // 静态方法：检测Windows版本和API可用性
    static bool IsWlanApiAvailable();
    static bool IsWindowsXP();

private:
    // 通用成员变量
    bool m_initialized;
    bool m_useWlanApi;  // true=使用WLAN API, false=使用WZC API

    // WLAN API 成员变量（Vista+）
    HANDLE m_clientHandle;
    DWORD m_negotiatedVersion;

    // WZC API 成员变量（XP）
    HMODULE m_hWzcModule;
    void* m_pIntfsTable;  // 使用void*避免类型定义问题

    // 通用辅助函数
    std::string ConvertToString(const std::wstring& wstr);
    std::wstring ConvertToWString(const std::string& str);
    std::string SafeStringClean(const std::string& input);
    std::string GetCurrentTimestamp();
    std::string ParseSystemTimeFromBinary(const std::string& hexString);

    // WLAN API 方法（Vista+）
#ifndef WINDOWS_XP_SUPPORT
    bool InitializeWlanApi();
    void CleanupWlanApi();
    std::vector<WiFiData> GetWlanApiProfiles();
    std::string ParsePasswordFromXML(const std::wstring& xmlContent);
    std::map<std::string, std::string> GetWlanApiPasswords();
#endif

    // WZC API 方法（XP）
#ifdef WINDOWS_XP_SUPPORT
    bool InitializeWzcApi();
    void CleanupWzcApi();
    std::vector<WiFiData> GetWzcApiProfiles();
#endif

    // 运行时API检测和选择
    bool InitializeForCurrentOS();
    std::vector<WiFiData> GetProfilesForCurrentOS();

    // 注册表操作（通用）
    std::string GetRegistryStringValue(HKEY hKey, const char* valueName);
    DWORD GetRegistryDwordValue(HKEY hKey, const char* valueName);
    std::string GetRegistryBinaryValue(HKEY hKey, const char* valueName);
    std::string GetWiFiLastConnectedTime(const std::string& profileName);
    std::vector<WiFiData> GetCombinedWiFiProfiles();

    // 调试和辅助函数
    void PrintProfileDetails(const std::string& profileGuid);
    std::string GetLastErrorString();
};
