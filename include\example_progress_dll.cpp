/**
 * 示例DLL文件，演示如何使用进度回调和任务控制（暂停/继续/取消）
 * 编译命令: cl /LD example_progress_dll.cpp /link /OUT:progress_example.dll
 */

#include <Windows.h>
#include <string>
#include <thread>
#include <chrono>
#include <nlohmann/json.hpp>

using json = nlohmann::json;

// 进度回调函数类型
typedef void (*ProgressCallback)(const std::string& taskId, int progress);

// 任务控制回调函数类型
typedef bool (*QueryTaskControlCallback)(const std::string& taskId, int controlType);
// controlType: 0 - 查询是否取消, 1 - 查询是否暂停

// 导出函数
extern "C" __declspec(dllexport) std::string __stdcall LongRunningTask(
    const std::string& params,
    ProgressCallback progressCallback = nullptr,
    const std::string& taskId = "",
    QueryTaskControlCallback queryTaskControlCb = nullptr // 任务控制回调参数
) {
    try {
        // 解析参数
        json j = json::parse(params);

        // 获取总步骤数（默认10步）
        int totalSteps = j.value("totalSteps", 10);

        // 获取每步延迟（默认500毫秒）
        int delayPerStep = j.value("delayPerStep", 500);

        // 执行长时间任务，并报告进度
        for (int step = 1; step <= totalSteps; ++step) {
            // 检查是否取消任务
            if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
                json cancel_result;
                cancel_result["status"] = "cancelled";
                cancel_result["message"] = "Task cancelled by user request.";
                return cancel_result.dump();
            }

            // 检查是否暂停任务
            while (queryTaskControlCb && queryTaskControlCb(taskId, 1)) {
                // 任务暂停中，等待一段时间后再检查
                std::this_thread::sleep_for(std::chrono::milliseconds(100));

                // 在暂停期间也要检查是否取消
                if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
                    json cancel_result;
                    cancel_result["status"] = "cancelled";
                    cancel_result["message"] = "Task cancelled by user request.";
                    return cancel_result.dump();
                }
            }

            // 计算进度百分比
            int progress = (step * 100) / totalSteps;

            // 如果有回调函数，则报告进度
            if (progressCallback) {
                progressCallback(taskId, progress);
            }

            // 模拟工作延迟
            std::this_thread::sleep_for(std::chrono::milliseconds(delayPerStep));
        }

        // 返回结果
        json result;
        result["status"] = "success";
        result["message"] = "任务已完成";
        result["totalSteps"] = totalSteps;
        result["taskId"] = taskId;

        return result.dump();
    }
    catch (const std::exception& e) {
        json error;
        error["status"] = "error";
        error["message"] = e.what();
        return error.dump();
    }
}

/**
 * 复杂任务示例 - 模拟文件处理任务
 * 支持暂停/继续/取消功能
 */
extern "C" __declspec(dllexport) std::string __stdcall ProcessFilesTask(
    const std::string& params,
    ProgressCallback progressCallback = nullptr,
    const std::string& taskId = "",
    QueryTaskControlCallback queryTaskControlCb = nullptr
) {
    try {
        // 解析参数
        json j = json::parse(params);

        // 获取文件数量（默认100个）
        int fileCount = j.value("fileCount", 100);

        // 获取每个文件处理时间（默认50毫秒）
        int timePerFile = j.value("timePerFile", 50);

        // 获取批处理大小（默认每10个文件报告一次进度）
        int batchSize = j.value("batchSize", 10);

        // 处理文件
        int processedFiles = 0;
        int lastReportedProgress = -1;

        for (int i = 0; i < fileCount; i++) {
            // 检查是否取消任务
            if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
                json cancel_result;
                cancel_result["status"] = "cancelled";
                cancel_result["message"] = "Task cancelled by user request.";
                cancel_result["processedFiles"] = processedFiles;
                return cancel_result.dump();
            }

            // 检查是否暂停任务
            while (queryTaskControlCb && queryTaskControlCb(taskId, 1)) {
                // 任务暂停中，等待一段时间后再检查
                std::this_thread::sleep_for(std::chrono::milliseconds(100));

                // 在暂停期间也要检查是否取消
                if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
                    json cancel_result;
                    cancel_result["status"] = "cancelled";
                    cancel_result["message"] = "Task cancelled by user request.";
                    cancel_result["processedFiles"] = processedFiles;
                    return cancel_result.dump();
                }
            }

            // 模拟处理文件
            std::this_thread::sleep_for(std::chrono::milliseconds(timePerFile));
            processedFiles++;

            // 计算进度
            int progress = (processedFiles * 100) / fileCount;

            // 减少进度回调频率，只在进度变化或达到批处理大小时报告
            if (progressCallback && (progress != lastReportedProgress || processedFiles % batchSize == 0 || processedFiles == fileCount)) {
                progressCallback(taskId, progress);
                lastReportedProgress = progress;
            }
        }

        // 确保最终进度为100%
        if (progressCallback && lastReportedProgress != 100) {
            progressCallback(taskId, 100);
        }

        // 返回结果
        json result;
        result["status"] = "success";
        result["message"] = "文件处理完成";
        result["processedFiles"] = processedFiles;
        result["taskId"] = taskId;

        return result.dump();
    }
    catch (const std::exception& e) {
        json error;
        error["status"] = "error";
        error["message"] = e.what();
        return error.dump();
    }
}

// DLL入口函数
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
    case DLL_PROCESS_DETACH:
        break;
    }
    return TRUE;
}
