#include "pch.h"
#include "include/ProcessInfoManager.h"
#include <iostream>
#include <string>

int main() {
    std::cout << "测试 Init_ProcessInfoMsg 返回数据中的 create_time 字段..." << std::endl;
    
    try {
        // 创建进程信息管理器实例
        ProcessInfoManager processManager;
        
        // 获取所有进程的详细信息
        nlohmann::json processInfo = processManager.GetAllProcessesDetailed();
        
        std::cout << "获取到的进程数量: " << processInfo.size() << std::endl;
        
        // 显示前5个进程的信息，验证create_time字段
        int count = 0;
        for (const auto& process : processInfo) {
            if (count >= 5) break;
            
            std::cout << "\n进程 " << (count + 1) << ":" << std::endl;
            std::cout << "  PID: " << process["pid"] << std::endl;
            std::cout << "  名称: " << process["name"] << std::endl;
            std::cout << "  路径: " << process["path"] << std::endl;
            std::cout << "  创建时间: " << process["create_time"] << std::endl;
            std::cout << "  本地地址: " << process["localAddress"] << std::endl;
            std::cout << "  本地端口: " << process["localPort"] << std::endl;
            
            count++;
        }
        
        // 测试JSON序列化
        std::string jsonStr = processInfo.dump(2);
        std::cout << "\nJSON数据示例（前500字符）:" << std::endl;
        std::cout << jsonStr.substr(0, 500) << "..." << std::endl;
        
        std::cout << "\n测试完成！create_time字段已成功添加到返回数据中。" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
