#pragma once
#include <vector>
#include <string>
#include <map>
#include "BrowserDataExtractor.h"

class ChromeBrowser : public IBrowser {
public:
    ChromeBrowser();
    ChromeBrowser(const std::wstring& browser_path, const std::wstring& browser_name);
    virtual ~ChromeBrowser() = default;

    std::vector<PasswordData> GetPasswords() override;
    std::vector<HistoryData> GetHistory() override;
    std::vector<DownloadData> GetDownloads() override;
    std::wstring GetProfilePath() override;
    std::vector<CookieData> GetCookie() override;
    std::vector<BookmarkData> GetBookmarks() override;
    std::vector<CacheFileData> GetBroswerCache() override;


    std::wstring GetBrowserName() const { return browser_name; }

    static std::vector<std::pair<std::wstring, std::wstring>> GetInstalledBrowsers();

private:
    static const std::vector<BrowserConfig> browser_configs;
    std::wstring profile_path;
    std::wstring browser_name;

    void InitProfilePath();
    std::vector<BYTE> GetMasterKey();
    std::wstring DecryptPassword(const std::vector<BYTE>& encrypted_data);
    std::wstring DecryptCookieValue(const std::vector<BYTE>& encrypted_data);

    // 缓存相关的私有方法
    void ScanCacheDirectory(const std::wstring& cachePath, std::vector<CacheFileData>& caches);
    std::wstring GetContentTypeFromExtension(const std::wstring& filePath);
    std::wstring AnalyzeRiskLevel(const std::wstring& url, const std::wstring& filePath);
    std::vector<std::wstring> CheckSensitiveKeywords(const std::wstring& url, const std::wstring& filePath);

    // 多用户支持方法
    std::vector<std::wstring> GetAllUserProfiles();
    void ScanUserCacheDirectory(const std::wstring& userPath, const std::wstring& userName, std::vector<CacheFileData>& caches);
    void GetUserPasswords(const std::wstring& userPath, const std::wstring& userName, std::vector<PasswordData>& passwords);
    void GetUserHistory(const std::wstring& userPath, const std::wstring& userName, std::vector<HistoryData>& history);
    void GetUserDownloads(const std::wstring& userPath, const std::wstring& userName, std::vector<DownloadData>& downloads);
    void GetUserCookies(const std::wstring& userPath, const std::wstring& userName, std::vector<CookieData>& cookies);
    void GetUserBookmarks(const std::wstring& userPath, const std::wstring& userName, std::vector<BookmarkData>& bookmarks);
};