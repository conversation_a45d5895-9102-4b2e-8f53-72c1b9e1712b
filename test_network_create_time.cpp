#include "pch.h"
#include "include/NetworkConnectionManager.h"
#include <iostream>
#include <string>
#include <nlohmann/json.hpp>

using json = nlohmann::json;

int main() {
    std::cout << u8"=== 测试网络连接创建时间功能 ===" << std::endl;
    
    try {
        // 创建网络连接管理器实例
        NetworkConnectionManager networkManager;
        
        std::cout << u8"\n步骤1: 测试统一接口的create_time字段..." << std::endl;
        
        // 测试统一接口
        std::string unifiedJsonString = networkManager.GetNetworkConnectionDataAsJsonString();
        std::cout << u8"统一接口JSON数据大小: " << unifiedJsonString.size() << u8" 字节" << std::endl;
        
        if (!unifiedJsonString.empty() && unifiedJsonString != "[]") {
            json unifiedData = json::parse(unifiedJsonString);
            if (unifiedData.is_array() && !unifiedData.empty()) {
                std::cout << u8"找到 " << unifiedData.size() << u8" 个网络连接记录" << std::endl;
                
                // 显示前3个记录的时间信息
                int count = 0;
                for (const auto& record : unifiedData) {
                    if (count >= 3) break;
                    
                    std::cout << u8"\n--- 连接记录 " << (count + 1) << u8" ---" << std::endl;
                    
                    if (record.contains("protocol")) {
                        std::cout << u8"协议: " << record["protocol"].get<std::string>() << std::endl;
                    }
                    
                    if (record.contains("local_address") && record.contains("local_port")) {
                        std::cout << u8"本地地址: " << record["local_address"].get<std::string>() 
                                  << u8":" << record["local_port"].get<int>() << std::endl;
                    }
                    
                    if (record.contains("process_name")) {
                        std::cout << u8"进程名: " << record["process_name"].get<std::string>() << std::endl;
                    }
                    
                    if (record.contains("create_time")) {
                        std::string createTime = record["create_time"].get<std::string>();
                        std::cout << u8"连接创建时间: " << createTime << std::endl;
                        
                        if (createTime != "Unknown" && !createTime.empty()) {
                            std::cout << u8"✓ 成功获取连接创建时间！" << std::endl;
                        } else {
                            std::cout << u8"⚠ 连接创建时间为空或未知" << std::endl;
                        }
                    } else {
                        std::cout << u8"✗ 缺少create_time字段" << std::endl;
                    }
                    
                    count++;
                }
            }
        }
        
        std::cout << u8"\n步骤2: 测试传统接口的连接时间字段..." << std::endl;
        
        // 测试传统接口
        std::string traditionalJsonString = networkManager.GetAllNetworkConnectionsAsJsonString();
        std::cout << u8"传统接口JSON数据大小: " << traditionalJsonString.size() << u8" 字节" << std::endl;
        
        if (!traditionalJsonString.empty() && traditionalJsonString != "[]") {
            json traditionalData = json::parse(traditionalJsonString);
            if (traditionalData.is_array() && !traditionalData.empty()) {
                const auto& firstRecord = traditionalData[0];
                
                std::cout << u8"\n--- 传统接口第一条记录 ---" << std::endl;
                
                if (firstRecord.contains("processCreateTime")) {
                    std::cout << u8"进程创建时间: " << firstRecord["processCreateTime"].get<std::string>() << std::endl;
                }
                
                if (firstRecord.contains("connectionCreateTime")) {
                    std::string connCreateTime = firstRecord["connectionCreateTime"].get<std::string>();
                    std::cout << u8"连接创建时间: " << connCreateTime << std::endl;
                    
                    if (connCreateTime != "Unknown" && !connCreateTime.empty()) {
                        std::cout << u8"✓ 传统接口也包含连接创建时间！" << std::endl;
                    } else {
                        std::cout << u8"⚠ 传统接口连接创建时间为空或未知" << std::endl;
                    }
                } else {
                    std::cout << u8"✗ 传统接口缺少connectionCreateTime字段" << std::endl;
                }
            }
        }
        
        std::cout << u8"\n步骤3: 测试网络连接数据结构..." << std::endl;
        
        // 直接获取网络连接数据
        std::vector<NetworkConnectionData> connections = networkManager.GetNetworkConnectionData();
        std::cout << u8"直接获取到 " << connections.size() << u8" 个网络连接" << std::endl;
        
        if (!connections.empty()) {
            const auto& firstConn = connections[0];
            std::cout << u8"\n--- 第一个连接的详细信息 ---" << std::endl;
            std::cout << u8"协议: " << firstConn.protocol << std::endl;
            std::cout << u8"本地地址: " << firstConn.local_address << u8":" << firstConn.local_port << std::endl;
            std::cout << u8"进程名: " << firstConn.process_name << std::endl;
            std::cout << u8"连接创建时间: " << firstConn.create_time << std::endl;
            
            if (!firstConn.create_time.empty() && firstConn.create_time != "Unknown") {
                std::cout << u8"✓ NetworkConnectionData结构体包含有效的create_time字段！" << std::endl;
            } else {
                std::cout << u8"⚠ NetworkConnectionData的create_time字段为空或未知" << std::endl;
            }
        }
        
        std::cout << u8"\n=== 测试完成 ===" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << u8"测试过程中发生错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
