@echo off
chcp 65001 > nul
echo === 编译 NetworkConnectionManager 图标获取测试 ===

REM 设置编译器路径
set "VS_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community"
set "VCVARS_PATH=%VS_PATH%\VC\Auxiliary\Build\vcvars32.bat"

REM 检查Visual Studio是否存在
if not exist "%VCVARS_PATH%" (
    echo 错误: 找不到 Visual Studio 2022 Community
    echo 请确保已安装 Visual Studio 2022 Community 并且路径正确
    pause
    exit /b 1
)

REM 初始化编译环境
echo 初始化 Visual Studio 编译环境...
call "%VCVARS_PATH%"

REM 创建输出目录
if not exist "test_output" mkdir test_output

REM 编译参数
set COMPILE_FLAGS=/std:c++14 /EHsc /W3 /MD /O2
set INCLUDE_DIRS=/I"include" /I"vcpkg_installed\x86-windows\include"
set LIB_DIRS=/LIBPATH:"vcpkg_installed\x86-windows\lib"
set LIBS=kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib psapi.lib iphlpapi.lib ws2_32.lib crypt32.lib bcrypt.lib

echo.
echo 编译源文件...

REM 编译所有必要的源文件
cl %COMPILE_FLAGS% %INCLUDE_DIRS% /c ^
    src\Utils.cpp ^
    src\NetworkConnectionManager.cpp ^
    test_network_icon_utils.cpp ^
    /Fo"test_output\\"

if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo.
echo 链接可执行文件...

REM 链接生成可执行文件
link /OUT:"test_output\NetworkIconTest.exe" ^
    %LIB_DIRS% ^
    "test_output\Utils.obj" ^
    "test_output\NetworkConnectionManager.obj" ^
    "test_output\test_network_icon_utils.obj" ^
    %LIBS%

if %ERRORLEVEL% neq 0 (
    echo 链接失败！
    pause
    exit /b 1
)

echo.
echo ✓ 编译成功！
echo 可执行文件: test_output\NetworkIconTest.exe
echo.
echo 运行测试程序...
echo.

REM 运行测试程序
"test_output\NetworkIconTest.exe"

echo.
echo 测试完成！
pause
