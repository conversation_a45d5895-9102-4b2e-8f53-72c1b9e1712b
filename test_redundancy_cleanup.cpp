// 测试冗余代码清理后的功能
// 验证Utils类中的统一方法是否正常工作

#include <iostream>
#include <string>
#include <vector>
#include <windows.h>
#include "Utils.h"
#include "ProcessManager.h"
#include "ProcessInfoManager.h"

void TestBase64Encoding() {
    std::cout << "=== 测试Base64编码功能 ===" << std::endl;
    
    // 测试数据
    std::vector<BYTE> testData = {0x48, 0x65, 0x6C, 0x6C, 0x6F}; // "Hello"
    
    // 使用Utils中的统一方法
    std::string base64Result = Utils::Base64Encode(testData);
    std::cout << "Base64编码结果: " << base64Result << std::endl;
    
    // 验证结果不为空
    if (!base64Result.empty()) {
        std::cout << "✅ Base64编码测试通过" << std::endl;
    } else {
        std::cout << "❌ Base64编码测试失败" << std::endl;
    }
}

void TestIconExtraction() {
    std::cout << "\n=== 测试图标提取功能 ===" << std::endl;
    
    // 获取系统图标进行测试
    HICON hIcon = LoadIcon(NULL, IDI_APPLICATION);
    if (hIcon) {
        // 使用Utils中的统一方法
        std::vector<BYTE> bmpData = Utils::ExtractIconToBMP(hIcon, 32);
        
        if (!bmpData.empty()) {
            std::cout << "✅ 图标转BMP测试通过，数据大小: " << bmpData.size() << " 字节" << std::endl;
            
            // 测试转换为Base64
            std::string base64Icon = Utils::Base64Encode(bmpData);
            if (!base64Icon.empty()) {
                std::cout << "✅ 图标Base64转换测试通过，长度: " << base64Icon.length() << std::endl;
            } else {
                std::cout << "❌ 图标Base64转换测试失败" << std::endl;
            }
        } else {
            std::cout << "❌ 图标转BMP测试失败" << std::endl;
        }
        
        DestroyIcon(hIcon);
    } else {
        std::cout << "❌ 无法加载测试图标" << std::endl;
    }
}

void TestStringConversion() {
    std::cout << "\n=== 测试字符串转换功能 ===" << std::endl;
    
    // 测试UTF-8到宽字符转换
    std::string utf8Str = "测试字符串";
    std::wstring wideStr = Utils::UTF8ToWString(utf8Str);
    
    if (!wideStr.empty()) {
        std::cout << "✅ UTF8到宽字符转换测试通过" << std::endl;
        
        // 测试宽字符到UTF-8转换
        std::string convertedBack = Utils::WStringToUTF8(wideStr);
        if (convertedBack == utf8Str) {
            std::cout << "✅ 宽字符到UTF8转换测试通过" << std::endl;
        } else {
            std::cout << "❌ 宽字符到UTF8转换测试失败" << std::endl;
        }
    } else {
        std::cout << "❌ UTF8到宽字符转换测试失败" << std::endl;
    }
}

void TestProcessManagerIntegration() {
    std::cout << "\n=== 测试ProcessManager集成 ===" << std::endl;
    
    try {
        ProcessManager pm;
        
        // 测试获取进程图标（应该使用Utils中的方法）
        std::string iconBase64 = pm.ExtractProcessIconAsBase64("C:\\Windows\\System32\\notepad.exe", 32);
        
        if (!iconBase64.empty()) {
            std::cout << "✅ ProcessManager图标提取测试通过" << std::endl;
        } else {
            std::cout << "⚠️ ProcessManager图标提取返回空结果（可能是正常的）" << std::endl;
        }
    } catch (const std::exception& e) {
        std::cout << "❌ ProcessManager测试异常: " << e.what() << std::endl;
    }
}

void TestProcessInfoManagerIntegration() {
    std::cout << "\n=== 测试ProcessInfoManager集成 ===" << std::endl;
    
    try {
        ProcessInfoManager pim;
        
        // 测试获取进程图标（应该使用Utils中的方法）
        std::string iconBase64 = pim.GetExeIconAsBase64("C:\\Windows\\System32\\notepad.exe");
        
        if (!iconBase64.empty()) {
            std::cout << "✅ ProcessInfoManager图标提取测试通过" << std::endl;
        } else {
            std::cout << "⚠️ ProcessInfoManager图标提取返回空结果（可能是正常的）" << std::endl;
        }
    } catch (const std::exception& e) {
        std::cout << "❌ ProcessInfoManager测试异常: " << e.what() << std::endl;
    }
}

int main() {
    std::cout << "开始测试冗余代码清理后的功能..." << std::endl;
    
    TestBase64Encoding();
    TestIconExtraction();
    TestStringConversion();
    TestProcessManagerIntegration();
    TestProcessInfoManagerIntegration();
    
    std::cout << "\n测试完成！" << std::endl;
    std::cout << "如果所有测试都通过，说明冗余代码清理成功。" << std::endl;
    
    return 0;
}
