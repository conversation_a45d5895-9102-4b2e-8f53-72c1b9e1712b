﻿#pragma once
#include "ServiceData.h"
#include <vector>
#include <string>
#include <functional>
#include <windows.h>
#include <nlohmann/json.hpp>



class ServiceManager {
public:
    ServiceManager();
    ~ServiceManager();

    // 初始化服务管理器
    bool Initialize();

    // 清理资源
    void Cleanup();

    // 获取所有Windows服务
    std::vector<ServiceData> GetAllServices();

    // 获取完整的服务信息并返回JSON格式
    nlohmann::json GetServicesInfoAsJson();

    // 保存服务信息到JSON文件
    bool SaveServicesInfoToFile(const std::string& filename);

 

private:
    SC_HANDLE m_scManager;
    bool m_initialized;

    // 注意：字符串转换函数已移至Utils类中
    // 使用 Utils::WStringToUTF8() 替代 ConvertToString()
    // 使用 Utils::UTF8ToWString() 替代 ConvertToWString()

    // 获取服务状态字符串
    std::string GetServiceStatusString(DWORD status);

    // 获取启动类型字符串
    std::string GetStartupTypeString(DWORD startType);

    // 获取服务类型字符串
    std::string GetServiceTypeString(DWORD serviceType);

    // 获取服务详细信息
    ServiceData GetServiceDetails(const std::string& serviceName);

    // 获取服务描述
    std::string GetServiceDescription(SC_HANDLE serviceHandle);

    // 获取服务配置信息
    bool GetServiceConfig(SC_HANDLE serviceHandle, ServiceData& serviceData);
};
