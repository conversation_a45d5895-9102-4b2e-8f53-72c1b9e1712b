#include "pch.h"
#include "include/NetworkConnectionManager.h"
#include <iostream>
#include <string>
#include <nlohmann/json.hpp>

using json = nlohmann::json;

int main() {
    std::cout << "=== Simple Create Time Test ===" << std::endl;
    
    try {
        NetworkConnectionManager networkManager;
        
        std::cout << "\nTesting unified interface..." << std::endl;
        
        // Test unified interface
        std::string jsonString = networkManager.GetNetworkConnectionDataAsJsonString();
        std::cout << "JSON data size: " << jsonString.size() << " bytes" << std::endl;
        
        if (jsonString.empty()) {
            std::cout << "Warning: Empty JSON data" << std::endl;
            return 1;
        }
        
        if (jsonString == "[]") {
            std::cout << "Warning: Empty JSON array" << std::endl;
            return 1;
        }
        
        try {
            // Try to parse JSON
            json data = json::parse(jsonString);
            std::cout << "SUCCESS: JSON parsing successful!" << std::endl;
            std::cout << "Found " << data.size() << " network connection records" << std::endl;
            
            if (!data.empty()) {
                const auto& firstRecord = data[0];
                std::cout << "\n--- First Record Field Check ---" << std::endl;
                
                if (firstRecord.contains("create_time")) {
                    std::cout << "SUCCESS: create_time field exists" << std::endl;
                    std::cout << "create_time value: " << firstRecord["create_time"] << std::endl;
                } else {
                    std::cout << "ERROR: create_time field missing" << std::endl;
                }
                
                if (firstRecord.contains("process_name")) {
                    std::cout << "process_name: " << firstRecord["process_name"] << std::endl;
                }
                
                if (firstRecord.contains("process_path")) {
                    std::cout << "process_path: " << firstRecord["process_path"] << std::endl;
                }
            }
            
        } catch (const json::parse_error& e) {
            std::cout << "ERROR: JSON parsing failed: " << e.what() << std::endl;
            std::cout << "JSON first 200 characters: " << jsonString.substr(0, 200) << std::endl;
            return 1;
        }
        
        std::cout << "\n=== Test completed successfully ===" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Test error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
