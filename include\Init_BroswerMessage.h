#pragma once

#include "BrowserDataExtractor.h"
#include "ChromeBrowser.h"
#include "FirefoxBrowser.h"
#include "IEBrowser.h"
#include "Utils.h"
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#include <filesystem>
#include <iostream>
#include <nlohmann/json.hpp>
#include "ProcessInfoManager.h"

// 添加任务控制回调函数类型定义
typedef bool (*QueryTaskControlCallback)(const std::string& taskId, int controlType);
// controlType: 0 - 查询是否取消, 1 - 查询是否暂停

extern "C" __declspec(dllexport) std::string Init_BroswerMsg(const std::string& params, void(*progressCallback)(const std::string&, int), const std::string& taskId, QueryTaskControlCallback queryTaskControlCb = nullptr);
//extern "C" __declspec(dllexport) std::string Init_ProcessInfoMsg(const std::string& params, void (*progressCallback)(const std::string&, int), const std::string& taskId, QueryTaskControlCallback queryTaskControlCb = nullptr);
extern "C" __declspec(dllexport) std::string Init_WifiInfoMsg(const std::string& params, void(*progressCallback)(const std::string&, int), const std::string& taskId, QueryTaskControlCallback queryTaskControlCb);
extern "C" __declspec(dllexport) std::string Init_ServiceInfoMsg(const std::string& params, void(*progressCallback)(const std::string&, int), const std::string& taskId, QueryTaskControlCallback queryTaskControlCb);
extern "C" __declspec(dllexport) std::string Init_ProcessallInfoMsg(const std::string& params, void(*progressCallback)(const std::string&, int), const std::string& taskId, QueryTaskControlCallback queryTaskControlCb);
extern "C" __declspec(dllexport) std::string Init_ShareInfoMsg(const std::string& params, void(*progressCallback)(const std::string&, int), const std::string& taskId, QueryTaskControlCallback queryTaskControlCb);

extern "C" __declspec(dllexport) std::string Init_DriverInfoMsg(const std::string& params, void(*progressCallback)(const std::string&, int), const std::string& taskId, QueryTaskControlCallback queryTaskControlCb);
extern "C" __declspec(dllexport) std::string Init_FirewallInfoMsg(const std::string& params, void(*progressCallback)(const std::string&, int), const std::string& taskId, QueryTaskControlCallback queryTaskControlCb);
extern "C" __declspec(dllexport) std::string Init_ScreensaverInfoMsg(const std::string& params, void(*progressCallback)(const std::string&, int), const std::string& taskId, QueryTaskControlCallback queryTaskControlCb);
extern "C" __declspec(dllexport) std::string Init_PasswordPolicyInfoMsg(const std::string& params, void(*progressCallback)(const std::string&, int), const std::string& taskId, QueryTaskControlCallback queryTaskControlCb);
extern "C" __declspec(dllexport) std::string Init_UserAccountInfoMsg(const std::string& params, void(*progressCallback)(const std::string&, int), const std::string& taskId, QueryTaskControlCallback queryTaskControlCb);
extern "C" __declspec(dllexport) std::string Init_AccountLockoutPolicyInfoMsg(const std::string& params, void(*progressCallback)(const std::string&, int), const std::string& taskId, QueryTaskControlCallback queryTaskControlCb);
extern "C" __declspec(dllexport) std::string Init_StartupInfoMsg(const std::string& params, void(*progressCallback)(const std::string&, int), const std::string& taskId, QueryTaskControlCallback queryTaskControlCb);
extern "C" __declspec(dllexport) std::string Init_NetworkConnectionInfoMsg(const std::string& params, void(*progressCallback)(const std::string&, int), const std::string& taskId, QueryTaskControlCallback queryTaskControlCb);
extern "C" __declspec(dllexport) std::string Init_ExifExtractorMsg(const std::string& params, void(*progressCallback)(const std::string&, int), const std::string& taskId, QueryTaskControlCallback queryTaskControlCb);

class Init_BroswerMessage
{
public:
	Init_BroswerMessage();
	~Init_BroswerMessage();

    std::string GetAllDataAsJson(const std::vector<std::pair<std::wstring, std::wstring>>& chromeBrowsers,
        const std::vector<PasswordData>& firefoxPasswords,
        const std::vector<HistoryData>& firefoxHistory,
        const std::vector<DownloadData>& firefoxDownloads,
        const std::vector<CookieData>& firefoxCookies,
        const std::vector<BookmarkData> firefoxBookmarks,
        const std::vector<CacheFileData>& firefoxCaches,
        const std::vector<PasswordData>& iePasswords,
        const std::vector<HistoryData>& ieHistory,
        const std::vector<DownloadData>& ieDownloads,
        const std::vector<CookieData>& ieCookies,
        const std::vector<BookmarkData> ieBookmarks,
        const std::vector<CacheFileData>& ieCaches,
        const std::string& action = "all");

    // 增加支持Chrome浏览器数据的函数
    std::string GetAllDataAsJsonWithChrome(
        const std::vector<std::pair<std::wstring, std::wstring>>& chromeBrowsers,
        const std::vector<std::vector<PasswordData>>& chromePasswords,
        const std::vector<std::vector<HistoryData>>& chromeHistory,
        const std::vector<std::vector<DownloadData>>& chromeDownloads,
        const std::vector<std::vector<CookieData>>& chromeCookies,
        const std::vector<std::vector<BookmarkData>>& chromeBookmarks,
        const std::vector<PasswordData>& firefoxPasswords,
        const std::vector<HistoryData>& firefoxHistory,
        const std::vector<DownloadData>& firefoxDownloads,
        const std::vector<CookieData>& firefoxCookies,
        const std::vector<BookmarkData> firefoxBookmarks,
        const std::vector<PasswordData>& iePasswords,
        const std::vector<HistoryData>& ieHistory,
        const std::vector<DownloadData>& ieDownloads,
        const std::vector<CookieData>& ieCookies,
        const std::vector<BookmarkData> ieBookmarks,
        const std::string& action = "all");

    std::string GetProcessInfoAsJson();
};

