# 浏览器数据提取器 - 扁平化JSON格式使用指南

## 概述

浏览器数据提取器已升级为扁平化JSON输出格式，提供更简洁、高效的数据结构。新格式按数据类型分组，而不是按浏览器分组，同时保留完整的数据归属信息。

## 格式对比

### 旧格式（已弃用）
```json
{
  "status": "success",
  "data": {
    "chrome_browsers": [
      {
        "name": "Chrome",
        "passwords": [...],
        "history": [...]
      }
    ],
    "firefox": {
      "name": "Firefox",
      "passwords": [...],
      "history": [...]
    },
    "ie": {
      "name": "Internet Explorer", 
      "passwords": [...],
      "history": [...]
    }
  }
}
```

### 新格式（当前）
```json
{
  "status": "success",
  "data": {
    "passwords": [
      {
        "url": "https://example.com",
        "username": "user1",
        "password": "***",
        "user_name": "<PERSON>",
        "browser_type": "Chrome"
      },
      {
        "url": "https://test.com",
        "username": "user2", 
        "password": "***",
        "user_name": "<PERSON>",
        "browser_type": "Firefox"
      }
    ],
    "history": [...],
    "downloads": [...],
    "cookies": [...],
    "bookmarks": [...],
    "caches": [...]
  }
}
```

## API调用方式

### 获取所有数据
```cpp
std::string params = R"({"action": "all"})";
std::string result = Init_BroswerMsg(params, nullptr, "task_id", nullptr);
```

### 获取特定类型数据
```cpp
// 仅获取密码
std::string params = R"({"action": "password"})";

// 仅获取历史记录
std::string params = R"({"action": "history"})";

// 仅获取下载记录
std::string params = R"({"action": "downloads"})";

// 仅获取Cookie
std::string params = R"({"action": "cookies"})";

// 仅获取书签
std::string params = R"({"action": "bookmarks"})";

// 仅获取缓存
std::string params = R"({"action": "cache"})";
```

## 数据结构说明

### 通用字段
所有数据记录都包含以下归属信息字段：
- `user_name`: 数据所属的Windows用户账户名
- `browser_type`: 数据来源的浏览器类型（Chrome、Firefox、Internet Explorer等）

### 密码数据 (passwords)
```json
{
  "url": "https://example.com",
  "username": "user123",
  "password": "encrypted_password",
  "create_time": "2024-12-01 10:30:00",
  "user_name": "Alice",
  "browser_type": "Chrome"
}
```

### 历史记录 (history)
```json
{
  "url": "https://example.com",
  "title": "Example Site",
  "visit_time": "2024-12-01 15:45:30",
  "visit_count": 5,
  "user_name": "Bob",
  "browser_type": "Firefox"
}
```

### 下载记录 (downloads)
```json
{
  "url": "https://example.com/file.zip",
  "file_path": "C:\\Users\\<USER>\\Downloads\\file.zip",
  "file_size": 1048576,
  "start_time": "2024-12-01 14:20:00",
  "end_time": "2024-12-01 14:21:30",
  "user_name": "Alice",
  "browser_type": "Chrome"
}
```

### Cookie数据 (cookies)
```json
{
  "Host": "example.com",
  "keyname": "session_id",
  "Cookie": "abc123def456",
  "path": "/",
  "createdata": "2024-12-01",
  "user_name": "Charlie",
  "browser_type": "Internet Explorer"
}
```

### 书签数据 (bookmarks)
```json
{
  "title": "Example Site",
  "url": "https://example.com",
  "folder_path": "Bookmarks/Work",
  "date_added": "2024-12-01 09:15:00",
  "user_name": "David",
  "browser_type": "Firefox"
}
```

### 缓存数据 (caches)
```json
{
  "file_path": "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Cache\\f_000001",
  "file_size": 2048,
  "last_modified": "2024-12-01 16:30:00",
  "url": "https://example.com/image.jpg",
  "user_name": "Alice",
  "browser_type": "Chrome"
}
```

## 使用示例

### C++ 解析示例
```cpp
#include "nlohmann/json.hpp"
using json = nlohmann::json;

// 获取数据
std::string result = Init_BroswerMsg(R"({"action": "all"})", nullptr, "task", nullptr);
json data = json::parse(result);

// 检查状态
if (data["status"] == "success") {
    // 处理密码数据
    if (data["data"].contains("passwords")) {
        for (const auto& password : data["data"]["passwords"]) {
            std::string url = password["url"];
            std::string user = password["user_name"];
            std::string browser = password["browser_type"];
            
            std::cout << "Password from " << browser << " (user: " << user << "): " << url << std::endl;
        }
    }
    
    // 统计不同浏览器的数据分布
    std::map<std::string, int> browserCounts;
    for (const auto& password : data["data"]["passwords"]) {
        browserCounts[password["browser_type"]]++;
    }
    
    for (const auto& pair : browserCounts) {
        std::cout << pair.first << ": " << pair.second << " passwords" << std::endl;
    }
}
```

### JavaScript 解析示例
```javascript
// 假设通过某种方式获取了JSON字符串
const result = JSON.parse(jsonString);

if (result.status === "success") {
    // 获取所有密码
    const passwords = result.data.passwords || [];
    
    // 按浏览器分组
    const passwordsByBrowser = passwords.reduce((acc, password) => {
        const browser = password.browser_type;
        if (!acc[browser]) acc[browser] = [];
        acc[browser].push(password);
        return acc;
    }, {});
    
    // 显示统计信息
    Object.keys(passwordsByBrowser).forEach(browser => {
        console.log(`${browser}: ${passwordsByBrowser[browser].length} passwords`);
    });
    
    // 按用户分组
    const passwordsByUser = passwords.reduce((acc, password) => {
        const user = password.user_name;
        if (!acc[user]) acc[user] = [];
        acc[user].push(password);
        return acc;
    }, {});
}
```

## 新格式的优势

### 1. 简化数据访问
- **旧格式**: `data.chrome_browsers[0].passwords[0]`
- **新格式**: `data.passwords[0]`

### 2. 便于跨浏览器分析
```cpp
// 统计所有浏览器的密码总数
int totalPasswords = data["data"]["passwords"].size();

// 按浏览器类型分组统计
std::map<std::string, int> browserStats;
for (const auto& item : data["data"]["passwords"]) {
    browserStats[item["browser_type"]]++;
}
```

### 3. 高效的数据过滤
```cpp
// 筛选特定用户的数据
std::vector<json> userPasswords;
for (const auto& password : data["data"]["passwords"]) {
    if (password["user_name"] == "Alice") {
        userPasswords.push_back(password);
    }
}

// 筛选特定浏览器的数据
std::vector<json> chromePasswords;
for (const auto& password : data["data"]["passwords"]) {
    if (password["browser_type"] == "Chrome") {
        chromePasswords.push_back(password);
    }
}
```

## 迁移指南

### 从旧格式迁移
如果您的代码使用旧格式，需要进行以下调整：

**旧代码**:
```cpp
// 访问Chrome密码
for (const auto& browser : data["data"]["chrome_browsers"]) {
    for (const auto& password : browser["passwords"]) {
        // 处理密码
    }
}

// 访问Firefox密码
for (const auto& password : data["data"]["firefox"]["passwords"]) {
    // 处理密码
}
```

**新代码**:
```cpp
// 访问所有密码（包括Chrome、Firefox、IE）
for (const auto& password : data["data"]["passwords"]) {
    std::string browser = password["browser_type"];
    // 根据browser_type区分浏览器类型
    if (browser == "Chrome") {
        // 处理Chrome密码
    } else if (browser == "Firefox") {
        // 处理Firefox密码
    }
}
```

## 注意事项

1. **向下兼容性**: 所有现有的action参数继续有效
2. **数据完整性**: 所有原有数据字段都得到保留
3. **性能提升**: 新格式解析速度更快，内存使用更少
4. **数据归属**: 通过user_name和browser_type字段可以准确识别数据来源

## 测试程序

项目包含以下测试程序来验证新格式：
- `test_flattened_json.cpp`: 全面测试新JSON格式
- `json_format_example.cpp`: 展示格式使用示例
- `test_multiuser.cpp`: 测试多用户支持功能

运行这些程序可以验证新格式的正确性和完整性。
