﻿#pragma once

#define WIN32_LEAN_AND_MEAN             // 从 Windows 头文件中排除极少使用的内容

// 解决 Windows SDK 7.1A 中的 HLOG 类型冲突问题
// 必须在包含任何 Windows 头文件之前定义这些宏
#ifndef _LMERRLOG_H_
#define _LMERRLOG_H_
#endif

#ifndef _LMCONS_H_
#define _LMCONS_H_
#endif

#ifndef _LMSERVER_H_
#define _LMSERVER_H_
#endif

// 防止包含 PDH 相关头文件，避免 PDH_HLOG 冲突
#ifndef _PDH_H_
#define _PDH_H_
#endif

// Windows 头文件
#include <windows.h>

// COM 相关头文件 - 提供 CoInitializeEx, CoUninitialize 等函数
#include <objbase.h>

// WLAN API 头文件（Vista+）
#ifndef WINDOWS_XP_SUPPORT
#include <wlanapi.h>
#endif

// 注册表操作
#include <winreg.h>
