<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Base64图标测试工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007acc;
            padding-bottom: 10px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .section-title::before {
            content: "🔧";
            margin-right: 8px;
        }
        .input-area {
            margin-bottom: 15px;
        }
        .input-label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #555;
        }
        .base64-input {
            width: 100%;
            height: 120px;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            resize: vertical;
            box-sizing: border-box;
        }
        .base64-input:focus {
            border-color: #007acc;
            outline: none;
        }
        .button-group {
            margin-top: 10px;
        }
        .btn {
            background-color: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            font-size: 14px;
        }
        .btn:hover {
            background-color: #005a9e;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .icon-display-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }
        .icon-test-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
            text-align: center;
        }
        .icon-test-card h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 14px;
        }
        .icon-container {
            width: 64px;
            height: 64px;
            margin: 10px auto;
            border: 2px solid #ddd;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
        }
        .icon-container img {
            max-width: 32px;
            max-height: 32px;
        }
        .status-indicator {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .analysis-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }
        .analysis-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
        }
        .analysis-card h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .info-label {
            font-weight: bold;
            color: #555;
        }
        .info-value {
            color: #333;
            font-family: 'Consolas', 'Monaco', monospace;
        }
        .hex-display {
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 11px;
            background: #f8f9fa;
            padding: 10px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ Base64图标测试工具</h1>

        <div class="section">
            <div class="section-title">输入Base64数据</div>
            <div id="inputSection">
                <div class="input-area">
                    <div class="input-label">粘贴您的Base64图标数据：</div>
                    <textarea id="base64Input" class="base64-input"
                              placeholder="请粘贴Base64数据，例如：Qk02EAAAAAAAADYAAAAoAAAAIAAAAOD..."></textarea>
                </div>
                <div class="button-group">
                    <button class="btn" onclick="testBase64()">🔍 测试图标</button>
                    <button class="btn btn-secondary" onclick="clearAll()">🗑️ 清空</button>
                    <button class="btn btn-secondary" onclick="loadSample()">📋 加载示例</button>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">图标显示测试</div>
            <div id="displaySection">
                <div id="displayResult">
                    <p style="color: #666; text-align: center; margin: 20px 0;">
                        请在上方输入Base64数据并点击"测试图标"按钮
                    </p>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">数据分析</div>
            <div id="analysisSection">
                <div id="analysisResult">
                    <p style="color: #666; text-align: center; margin: 20px 0;">
                        数据分析结果将在测试后显示
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 示例Base64数据
        const sampleBase64 = "Qk02EAAAAAAAADYAAAAoAAAAIAAAAOD///8BACAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAA6+np/+vp6f/s6en/7Onp/+vp6f/s6en/6unp/+rp6f/q6ur/6urp/+np6f/o6en/7Onq/+rp6f/o6en/6Orq/+zq6v/p6ur/6urq/+rp6f/s6ur/6urq/+rp6f/q6ur/6urq/+vo6P/t6er/7Onp/+np6f/r6en/6enp/+rq6v/o6en/6ujo/+rp6f/p6en/6+np/+zq6v/r6ur/7Onp/+zp6f/p6en/6unp/+np6f/s6en/6Onp/+3p6v/q6en/7enq/+zp6f/q6en/6+np/+np6f/s6en/6+rq/+np6f/n6en/6+np/+rp6f/q6en/6unp/+np6f/r6en/7Orq/+vp6f/o6en/6+rp/+rp6f/r6en/7Onp/+np6f/s6ur/6+rq/+zp6f/p6en/7Onp/+vp6f/p6en/6ujo/+jo5v/p6Of/6unp/+zp6v/t6er/6enp/+zp6f/q6en/6ujp/+rp6f/t6en/6Onp/+vp6f/o6en/6+np/+rp6f/r6en/6unp/+rp6v/p6en/6enp/+nq6v/r6en/6enp/+np6f/o6en/6enp/+jp6f/r6ef/6c+u/9yLIv/djin/3I8q/9uPKv/bjin/3Ioj/+XOrv/s6Oj/6unp/+rp6f/p6en/6Onp/+3p6f/q6en/6enp/+rp6f/p6en/6+np/+vp6f/q6ur/7Ojp/+vq6v/q6en/6enp/+3p6v/p6en/6unp/+np6f/o6en/3ZAt/9yPKf/bjin/244p/9qOKv/bjir/3I4p/9qOKP/bjSn/3I8p/92PKv/bjy3/7Orq/+rp6f/r6en/6enp/+rp6f/q6en/6erq/+zo6f/q6ur/7Orq/+jp6f/p6en/6enp/+rp6f/q6en/7Onp/+zp6v/r6en/6+np/9iIIP/cjyn/3I8p/9yOKf/ajyn/3I4q/9yPKv/ejyr/3I0o/9uOKP/bjyn/3I4q/92OKf/biR7/7Onp/+rq6v/r6en/6enp/+zo6f/r6ur/6erq/+np6f/s6ur/6enp/+jp6f/o6en/6erq/+jq6v/q6en/7vD0/9yOKv/bjin/3I4n/9uPKf/bjin/2o4o/9mOKP/bjin/+vv+/9uOKf/bjyn/244q/9uMKP/bjin/3I0o/92OKv/djin/3Y4p/+3v8//r6en/7unq/+np6f/p6ur/6+np/+np6f/s6On/6Orp/+np6f/p6en/6+rq/+np6f/ajSn/3I4q/9yPKf/cjin/244p/9yPKv/cjin/244q/9yPKv/29vb/9Pb1/9yPKf/ajyj/248p/92PKv/ajij/244p/9uNKP/ajyn/3o8r/+jq6f/p6en/6+np/+np6f/p6en/6enp/+vp6f/r6en/6+rq/+vp6f/r6er/7Onp/9mOKP/djir/244q/9eNKf/bjin/244q/92QK//ajSn/4JY3//Lp2v/39/f/9ff2/9qOKv/ajij/240o/9qNKf/bjin/2o4o/9uNKP/bjSj/7enp/+no6f/q6ur/6+nq/+np6f/q6ur/6Orq/+np6f/q6ur/7Onp/+np6f/YiCH/2o0o/9uOKf/cjin/2o4o/9qNKf/Zjij/5K5m//X29P/49vf/9vf3//n39//49/f/5Ll+/9uOKv/cjin/3I4p/9uOKP/cjin/2o4q/9yOKf/ZiR7/6+np/+rp6f/p6en/6enp/+np6f/r6ur/6Onp/+np6f/p6un/2pAp/9yNKf/cjSr/3o0q/9uNKf/djin/9vb2//b39//39/f/5bp7/9yOKv/09PP/9ff3//X5/v/bjin/3I0o/96dSv/39vb/2o0q/9yNKP/ajSn/3I0o/9uOKP/ekCz/6erq/+vp6f/q6ur/6enp/+vq6v/o6en/6unp/+no6P/cjin/3JAq/9qOKv/bjin/3I8p/9uNKf/29/f/9fb2/92QK//cjin/2own//X19f/YjC7/2o8o/9uOKf/bjyj/9/b2//X39//cjir/244p/9uNKP/ajSj/2o4p/9qOKf/r6Oj/6Orq/+rq6v/q6ur/6unq/+rp6f/q6en/6te+/9uNKP/ejin/2I4o/9uNKf/bjSj/+v3///X29f/ajyr/3Y8p/9qNKf/ajyn/2owo/9yNKf/Zjin/244q/9qOKf/bjij/9/X1//r9///bjin/3I4p/9qOKf/bjSj/3I0o/+jWxP/q6en/6urq/+rq6v/o6en/6+np/+np6f/ZiCD/3I4p/9yPKv/bjin/3I4q/92QLP/29vb/14kg/9uOKf/cjSn/240p/9uOKf/ajij/3Y0p/9yOKv/djin/240p/9mOJ//biB//9vf3/92QLP/ajSj/240p/9uNKf/ajCn/2Ikg/+np6f/r6en/6enp/+vp6f/o6en/6enp/9mOKf/ajSn/3I4o/9yOKP/djif/9/n7//X39//bjSn/3I4p/9uNKP/ajij/240o//f2+P/08+7/244o//bw5v/39fb/248q/9yOKP/39/f/+Pj7/9qNKf/cjin/244o/92MKf/ajSf/6+np/+np6f/p6en/6unp/+jq6f/q6Of/3Y4p/9mJIP/Zjiv/3I8q/9qNKf/29/f/9Pb2/9uOKf/cjin/9vT0/+W8hf/biiH/9fTw//T08//bjyr/+Pv//9yOKP/ZolL/2o4o//j29//09/b/2o0m/9yPKf/bjyr/24wl/92PKv/q6Oj/7Onp/+np6f/r6er/6+nq/+vo5//cjij/3I8r/9SFHv/bjyv/9PTy//b29f/09vb/3I4p//n6/f/cjij/9fj7//P08//bjij/3Ikj//Xx7P/YjSn/2o4o//X19P/ajin/9vf3//T19P/19fX/3I4p//b7///ejSj/3I0p/+3o6P/q6ur/6urq/+jp6f/p6en/6erp/9yOKv/bjSj/2o0q//X4/P/YiB7/9/f5//b39//y6tn/6NCr/9yOKP/dkC//5rqD/92OKf/djSj/8vTy/9yMKP/bjij/46pi/9aSN//29/f/9ff4/9yJHv/4/v//2o4q/92NKP/ajin/6enp/+np6f/p6en/6urq/+jp6f/p6ur/2Yge/9yOKf/bjin/240o/9uMJ//cjy3/9vb2/9yPKP/cjin/244q/9qNKP/ajSj/2o0p/9uOKf/cjin/240o/9uNJ//ajSn/344p//f29v/ZkCn/3I0p/9yNKP/cjin/3Y4o/9qLIf/p6ur/6enp/+rq6v/q6en/6Onp/+np6f/r1cH/3I4q/9uOKv/bjSj/2o0n/9uOKP/09/z/9/Pz/9yOKf/cjSj/248q/9uNKP/cjin/244p/9yOKP/bjin/244p/9yPK//z9PL/9vj7/9yOKP/ajSj/3I0o/9uPKP/ajir/6NXA/+jp6f/q6ur/6enp/+np6f/r6en/6unp/+zp6f/cjin/3Y4o/9qPKP/bjin/240o/9qNKv/19/f/9PTy/9qOKP/bjSj/2o0o/9uIH//19vX/3I8s/9mMKP/ajin/9fTz//T39//ajSn/2o0o/9uNKP/bjSj/244o/9yOKf/r6On/6unp/+np6f/p6en/6Orp/+np6f/p6en/6enp/96PLP/cjir/240o/9yNKP/cjSj/2own//b29v/u4Mr/3Y0p/9qOKP/6/f//9vb2//f18//cjSj/24ke//X09P/19/f/9fb2/9uOKP/bjCj/244q/9uNKf/bjir/3Y8r/+jp6f/p6en/6+np/+rq6v/o6en/6Onp/+np6f/p6ur/6Orq/9eHGf/bjSj/3Y0o/9qMJ//bjSj/2own/9uMJ//bjSj/9fPy//f39//19/f/9vf3//T29//09vb/+Pz//9yOKf/bjCf/240o/92OJ//cjij/24wo/9eIHv/r6en/6unp/+jp6f/p6en/6enp/+rq6v/o6en/6urq/+np6f/o6en/6+np/9yPKP/bjCj/2o4p/9uNKP/Zjij/240o/9yNKP/ajij/9/b3//f39//3+v7/9/r8/9qPKv/ajSj/240o/9mNJ//bjSj/2o0n/9mMKP/bjSj/6Onp/+np6f/r6ur/6Onp/+np6f/p6en/6enp/+vp6f/q6en/6unp/+np6f/q6en/2Y0p/9yNKP/cjin/2I0n/9uNKP/ajSf/2o0n/9qNJ//YjCn/9fb2//X08v/ajij/2o4o/9mNJ//bjSj/2Y0n/9qMJ//ajSf/240p/9uNKP/q6en/6enp/+rq6v/r6er/6enp/+rq6v/n6en/6enp/+np6f/q6ur/6enp/+np6f/s8PL/3I0p/92NKP/ajSj/2Ywn/9qNKP/djSj/2o0o/9yNKP/ekC3/9vb2/9yOKv/cjif/2own/9uOKf/bjSj/2Y0o/9qMJ//bjSf/7vD0/+nq6v/r6en/6Onp/+nq6v/p6en/6unp/+nq6f/o6en/6erq/+np6f/o6en/6unp/+nq6v/s6er/6+nq/9eIHf/ajCf/24wn/9uNKP/bjin/240o/9mNJ//bjSj/240o/9qMKP/cjCf/240o/9uMJ//aiB3/7Onp/+rp6f/r6en/6Ono/+vp6f/p6ur/6erq/+rq6v/p6en/6unq/+jp6f/q6en/6urq/+nq6v/q6ur/6enp/+np6f/p6un/6+np/9uQK//cjij/2o0o/9qNKP/bjSj/240o/9uOKf/bjif/244p/9uNKf/cjSn/248r/+vp6v/p6en/6erq/+zq6v/n6en/6+nq/+rp6f/o6en/6urq/+rq6v/p6en/6+np/+rp6f/q6en/6enp/+zq6//o6ur/6unp/+rq6v/o6ur/6enp/+jp6P/q1rr/2Ygd/9qOKf/bjin/3I4o/9mOKP/biyH/6NS9/+vq6f/o6un/6urq/+jp6f/q6ur/6urq/+vp6f/q6en/6enp/+vp6f/q6ur/6urq/+jq6v/p6en/6enp/+rq6v/p6en/6unp/+np6f/r6en/6+np/+np6f/q6ur/6Orq/+vp6f/o6en/6+np/+rp6P/s6Oj/6urp/+zq6v/q6ur/6erp/+rp6f/p6un/6urq/+jq6v/p6en/6enp/+vp6f/o6ur/6enp/+np6f/p6en/6enp/+jp6f/q6ur/6erq/+jq6v/q6er/6urq/+vp6f/q6ur/6urq/+nq6v/q6ur/6urq/+jp6f/p6en/5+np/+rq6v/q6ur/6urq/+vp6f/q6ur/6unp/+rp6v/r6en/6unq/+vp6v/p6en/6+jp/+jp6f/o6en/6+vr/+rq6v/r6ur/6Orp/+rq6v/p6en/6erq/+rp6f/q6ur/6enp/+jq6f/q6ur/6erq/+rp6f/q6en/6enp/+rq6v/q6ur/6erq/+np6f/p6+r/6enp/+rp6f/p6ur/6+nq/+rq6v/p6un/6+nq/+jq6v/q6ur/6+rq/+jq6v/m5ub/6enp/w==";

        // 清空所有内容
        function clearAll() {
            document.getElementById('base64Input').value = '';
            document.getElementById('displayResult').innerHTML = '<p style="color: #666; text-align: center; margin: 20px 0;">请在上方输入Base64数据并点击"测试图标"按钮</p>';
            document.getElementById('analysisResult').innerHTML = '<p style="color: #666; text-align: center; margin: 20px 0;">数据分析结果将在测试后显示</p>';
        }

        // 加载示例数据
        function loadSample() {
            document.getElementById('base64Input').value = sampleBase64;
        }

        // 主要测试函数
        function testBase64() {
            const base64Data = document.getElementById('base64Input').value.trim();

            if (!base64Data) {
                alert('请输入Base64数据');
                return;
            }

            try {
                // 显示图标测试结果
                displayIcons(base64Data);

                // 显示数据分析结果
                analyzeData(base64Data);

            } catch (error) {
                document.getElementById('displayResult').innerHTML =
                    `<div style="color: red; text-align: center; padding: 20px;">
                        ❌ 测试失败: ${error.message}
                    </div>`;
            }
        }

        // 显示图标测试
        function displayIcons(base64Data) {
            const displaySection = document.getElementById('displayResult');

            // 测试不同的显示方式
            const testMethods = [
                {
                    name: 'BMP格式 (推荐)',
                    dataUri: `data:image/bmp;base64,${base64Data}`,
                    description: '标准BMP格式显示'
                },
                {
                    name: 'PNG格式 (兼容测试)',
                    dataUri: `data:image/png;base64,${base64Data}`,
                    description: '尝试PNG格式解析'
                },
                {
                    name: 'JPEG格式 (兼容测试)',
                    dataUri: `data:image/jpeg;base64,${base64Data}`,
                    description: '尝试JPEG格式解析'
                },
                {
                    name: 'GIF格式 (兼容测试)',
                    dataUri: `data:image/gif;base64,${base64Data}`,
                    description: '尝试GIF格式解析'
                }
            ];

            let html = '<div class="icon-display-grid">';

            testMethods.forEach((method, index) => {
                html += `
                    <div class="icon-test-card">
                        <h4>${method.name}</h4>
                        <div class="icon-container">
                            <img id="icon-${index}"
                                 src="${method.dataUri}"
                                 alt="图标测试"
                                 onload="updateStatus(${index}, true)"
                                 onerror="updateStatus(${index}, false)">
                        </div>
                        <div id="status-${index}" class="status-indicator">
                            加载中...
                        </div>
                        <div style="font-size: 12px; color: #666; margin-top: 5px;">
                            ${method.description}
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            displaySection.innerHTML = html;
        }

        // 更新加载状态
        function updateStatus(index, success) {
            const statusElement = document.getElementById(`status-${index}`);
            const imgElement = document.getElementById(`icon-${index}`);

            if (success) {
                statusElement.innerHTML = `✅ 加载成功 (${imgElement.naturalWidth}x${imgElement.naturalHeight})`;
                statusElement.className = 'status-indicator status-success';
            } else {
                statusElement.innerHTML = '❌ 加载失败';
                statusElement.className = 'status-indicator status-error';
                imgElement.style.display = 'none';
            }
        }

        // 数据分析功能
        function analyzeData(base64Data) {
            const analysisSection = document.getElementById('analysisResult');

            try {
                // 基本信息分析
                const basicInfo = analyzeBasicInfo(base64Data);

                // 文件头分析
                const headerInfo = analyzeFileHeader(base64Data);

                // 生成分析结果HTML
                const html = `
                    <div class="analysis-grid">
                        <div class="analysis-card">
                            <h4>📊 基本信息</h4>
                            ${generateInfoRows(basicInfo)}
                        </div>
                        <div class="analysis-card">
                            <h4>🔍 文件头分析</h4>
                            ${generateInfoRows(headerInfo)}
                        </div>
                    </div>
                    <div class="analysis-card" style="margin-top: 20px;">
                        <h4>📋 十六进制数据 (前256字节)</h4>
                        <div class="hex-display">${generateHexDisplay(base64Data)}</div>
                    </div>
                `;

                analysisSection.innerHTML = html;

            } catch (error) {
                analysisSection.innerHTML = `
                    <div style="color: red; text-align: center; padding: 20px;">
                        ❌ 分析失败: ${error.message}
                    </div>
                `;
            }
        }

        // 基本信息分析
        function analyzeBasicInfo(base64Data) {
            const length = base64Data.length;
            const estimatedSize = Math.floor(length * 3 / 4);

            // 检查Base64填充字符
            let paddingCount = 0;
            if (length >= 2) {
                if (base64Data[length - 1] === '=') paddingCount++;
                if (base64Data[length - 2] === '=') paddingCount++;
            }

            // 验证Base64字符
            const validBase64Chars = /^[A-Za-z0-9+/=]*$/;
            const isValidBase64 = validBase64Chars.test(base64Data);

            return {
                'Base64长度': `${length} 字符`,
                '估计文件大小': `${estimatedSize} 字节`,
                'Base64开头': base64Data.substring(0, 20) + '...',
                'Base64结尾': '...' + base64Data.substring(length - 20),
                '填充字符数量': `${paddingCount} 个等号`,
                '长度校验': length % 4 === 0 ? '✅ 长度正确' : '❌ 长度不是4的倍数',
                '字符校验': isValidBase64 ? '✅ 字符有效' : '❌ 包含无效字符',
                '数据完整性': (length % 4 === 0 && isValidBase64) ? '✅ 格式正确' : '⚠️ 格式可能有问题'
            };
        }

        // 文件头分析
        function analyzeFileHeader(base64Data) {
            try {
                const binaryString = atob(base64Data.substring(0, Math.min(base64Data.length, 200)));
                const bytes = [];
                for (let i = 0; i < Math.min(binaryString.length, 54); i++) {
                    bytes.push(binaryString.charCodeAt(i));
                }

                const info = {};

                if (bytes.length >= 2) {
                    const signature = String.fromCharCode(bytes[0], bytes[1]);
                    info['文件签名'] = `"${signature}" (0x${bytes[0].toString(16).padStart(2, '0')} 0x${bytes[1].toString(16).padStart(2, '0')})`;

                    if (signature === 'BM') {
                        info['格式识别'] = '✅ BMP格式';

                        if (bytes.length >= 14) {
                            const fileSize = bytes[2] + (bytes[3] << 8) + (bytes[4] << 16) + (bytes[5] << 24);
                            const dataOffset = bytes[10] + (bytes[11] << 8) + (bytes[12] << 16) + (bytes[13] << 24);
                            info['文件大小'] = `${fileSize} 字节`;
                            info['数据偏移'] = `${dataOffset} 字节`;
                        }

                        if (bytes.length >= 54) {
                            const width = bytes[18] + (bytes[19] << 8) + (bytes[20] << 16) + (bytes[21] << 24);
                            const height = bytes[22] + (bytes[23] << 8) + (bytes[24] << 16) + (bytes[25] << 24);
                            const bitCount = bytes[28] + (bytes[29] << 8);

                            info['图像宽度'] = `${width} 像素`;
                            info['图像高度'] = `${Math.abs(height)} 像素`;
                            info['颜色深度'] = `${bitCount} 位`;
                            info['图像方向'] = height < 0 ? '自上而下' : '自下而上';
                        }
                    } else {
                        info['格式识别'] = '❌ 非BMP格式';
                    }
                }

                return info;
            } catch (error) {
                return {'解析错误': error.message};
            }
        }

        // 生成信息行HTML
        function generateInfoRows(infoObj) {
            let html = '';
            for (const [label, value] of Object.entries(infoObj)) {
                html += `
                    <div class="info-row">
                        <span class="info-label">${label}:</span>
                        <span class="info-value">${value}</span>
                    </div>
                `;
            }
            return html;
        }

        // 生成十六进制显示
        function generateHexDisplay(base64Data) {
            try {
                const binaryString = atob(base64Data.substring(0, Math.min(base64Data.length, 400)));
                let hexDisplay = '';
                const maxBytes = Math.min(binaryString.length, 256);

                for (let i = 0; i < maxBytes; i += 16) {
                    // 地址
                    hexDisplay += i.toString(16).padStart(8, '0').toUpperCase() + ': ';

                    // 十六进制
                    for (let j = 0; j < 16 && i + j < maxBytes; j++) {
                        hexDisplay += binaryString.charCodeAt(i + j).toString(16).padStart(2, '0').toUpperCase() + ' ';
                    }

                    // 填充空格
                    for (let j = maxBytes - i; j < 16; j++) {
                        hexDisplay += '   ';
                    }

                    // ASCII
                    hexDisplay += ' | ';
                    for (let j = 0; j < 16 && i + j < maxBytes; j++) {
                        const char = binaryString.charCodeAt(i + j);
                        hexDisplay += (char >= 32 && char <= 126) ? String.fromCharCode(char) : '.';
                    }

                    hexDisplay += '\n';
                }

                if (binaryString.length > 256) {
                    hexDisplay += `\n... (显示前256字节，总共约${binaryString.length}字节)`;
                }

                return hexDisplay;
            } catch (error) {
                return `解析错误: ${error.message}`;
            }
        }
    </script>
</body>
</html>