@echo off
echo Building simple create time test...

cl /EHsc /std:c++14 /I. /I"C:\vcpkg\installed\x86-windows\include" ^
   test_create_time_simple.cpp ^
   src\NetworkConnectionManager.cpp ^
   src\Utils.cpp ^
   /link /LIBPATH:"C:\vcpkg\installed\x86-windows\lib" ^
   /LIBPATH:"Release" ^
   BrowserDataExtractor.lib ^
   iphlpapi.lib ws2_32.lib psapi.lib shell32.lib gdi32.lib user32.lib advapi32.lib ^
   /out:test_create_time_simple.exe

if %ERRORLEVEL% EQU 0 (
    echo Build successful! Running test...
    echo.
    test_create_time_simple.exe
) else (
    echo Build failed!
)

pause
