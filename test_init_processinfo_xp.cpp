#include "pch.h"
#include "include/Init_BroswerMessage.h"
#include <iostream>
#include <string>
#include <windows.h>

// 进度回调函数
void ProgressCallback(const std::string& message, int progress) {
    std::cout << u8"进度 [" << progress << "%]: " << message << std::endl;
}

// 任务控制回调函数（用于测试取消/暂停功能）
bool QueryTaskControlCallback(const std::string& taskId, int checkType) {
    // 返回false表示继续执行，true表示取消
    return false;
}

int main() {
    std::cout << u8"=== Init_ProcessInfoMsg Windows XP 兼容性测试 ===" << std::endl;
    std::cout << u8"开始测试进程信息获取接口..." << std::endl;

    try {
        // 测试参数
        std::string params = "{}";  // 空参数
        std::string taskId = "test_task_001";

        std::cout << u8"\n步骤1: 调用 Init_ProcessInfoMsg..." << std::endl;
        
        // 调用接口
        std::string result = Init_ProcessInfoMsg(
            params,
            ProgressCallback,
            taskId,
            QueryTaskControlCallback
        );

        std::cout << u8"\n步骤2: 接口调用完成" << std::endl;
        std::cout << u8"返回数据大小: " << result.size() << u8" 字节" << std::endl;

        // 显示结果的前500个字符
        if (result.size() > 500) {
            std::cout << u8"\n返回数据预览 (前500字符):" << std::endl;
            std::cout << result.substr(0, 500) << "..." << std::endl;
        } else {
            std::cout << u8"\n完整返回数据:" << std::endl;
            std::cout << result << std::endl;
        }

        std::cout << u8"\n✓ 测试成功完成！" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << u8"\n✗ 测试失败 - 标准异常: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << u8"\n✗ 测试失败 - 未知异常" << std::endl;
        return 1;
    }

    std::cout << u8"\n按任意键退出..." << std::endl;
    system("pause");
    return 0;
}
