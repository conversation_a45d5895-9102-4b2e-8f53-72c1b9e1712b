#include "pch.h"
#include "Utils.h"
#include <time.h>
#include <sstream>
#include <iomanip>
#include <windows.h>
#include <algorithm>
#include <bcrypt.h>
#include <fstream>
#include <iostream>
#include <shellapi.h>
#include <map>
#include <cctype>
#pragma comment(lib, "bcrypt.lib")
#pragma comment(lib, "crypt32.lib")
#pragma comment(lib, "shell32.lib")

// ����״̬��
#ifndef STATUS_SUCCESS
#define STATUS_SUCCESS ((NTSTATUS)0x00000000L)
#endif

#ifndef STATUS_UNSUCCESSFUL
#define STATUS_UNSUCCESSFUL ((NTSTATUS)0xC0000001L)
#endif

#ifndef BCRYPT_SUCCESS
#define BCRYPT_SUCCESS(Status) (((NTSTATUS)(Status)) >= 0)
#endif

std::wstring Utils::ConvertChromeTimestamp(__int64 timestamp) {
    // Chromeʱ����Ǵ�1601��1��1�տ�ʼ��΢����
    timestamp /= 1000000; // ת��Ϊ��
    time_t unix_time = timestamp - 11644473600LL; // ����ΪUnixʱ���

    tm timeinfo;
    if (localtime_s(&timeinfo, &unix_time) != 0) {
        return L"Invalid timestamp";
    }

    wchar_t buffer[32];
    wcsftime(buffer, sizeof(buffer) / sizeof(wchar_t), L"%Y-%m-%d %H:%M:%S", &timeinfo);
    return std::wstring(buffer);
}

std::wstring Utils::ConvertFirefoxTimestamp(__int64 timestamp) {
    // Firefoxʱ����Ǵ�1970��1��1�տ�ʼ�ĺ�����
    timestamp /= 1000; // ת��Ϊ��

    tm timeinfo;
    if (localtime_s(&timeinfo, &timestamp) != 0) {
        return L"Invalid timestamp";
    }

    wchar_t buffer[32];
    wcsftime(buffer, sizeof(buffer) / sizeof(wchar_t), L"%Y-%m-%d %H:%M:%S", &timeinfo);
    return std::wstring(buffer);
}

std::vector<BYTE> Utils::Base64Decode(const std::string& input) {
    std::vector<BYTE> decoded;
    if (input.empty()) {
        printf("Base64����: ����Ϊ��\n");
        return decoded;
    }

    printf("Base64����: ���볤�� %zu �ֽ�\n", input.length());

    // ��������Ĵ�С
    DWORD decodedSize = 0;
    if (!CryptStringToBinaryA(input.c_str(), input.length(),
        CRYPT_STRING_BASE64, NULL, &decodedSize, NULL, NULL)) {
        // �����޸�Base64�ַ���
        std::string fixedInput = input;
        // �滻URL��ȫ���ַ�
        std::replace(fixedInput.begin(), fixedInput.end(), '-', '+');
        std::replace(fixedInput.begin(), fixedInput.end(), '_', '/');
        // �������
        switch (fixedInput.length() % 4) {
        case 2: fixedInput += "=="; break;
        case 3: fixedInput += "="; break;
        }

        if (!CryptStringToBinaryA(fixedInput.c_str(), fixedInput.length(),
            CRYPT_STRING_BASE64, NULL, &decodedSize, NULL, NULL)) {
            DWORD error = GetLastError();
            printf("Base64����: �����Сʧ�ܣ������� %d (0x%08X)\n", error, error);
            return decoded;
        }

        // �����ڴ沢����
        decoded.resize(decodedSize);
        if (!CryptStringToBinaryA(fixedInput.c_str(), fixedInput.length(),
            CRYPT_STRING_BASE64, decoded.data(), &decodedSize, NULL, NULL)) {
            DWORD error = GetLastError();
            printf("Base64����: ����ʧ�ܣ������� %d (0x%08X)\n", error, error);
            decoded.clear();
            return decoded;
        }
    }
    else {
        // �����ڴ沢����
        decoded.resize(decodedSize);
        if (!CryptStringToBinaryA(input.c_str(), input.length(),
            CRYPT_STRING_BASE64, decoded.data(), &decodedSize, NULL, NULL)) {
            DWORD error = GetLastError();
            printf("Base64����: ����ʧ�ܣ������� %d (0x%08X)\n", error, error);
            decoded.clear();
            return decoded;
        }
    }

    printf("Base64����: �ɹ���������С %lu �ֽ�\n", decodedSize);
    decoded.resize(decodedSize);
    return decoded;
}

std::wstring Utils::UTF8ToWString(const std::string& str) {
    if (str.empty()) return std::wstring();

    int size_needed = MultiByteToWideChar(CP_UTF8, 0, str.c_str(),
        (int)str.length(), NULL, 0);

    std::wstring wstr(size_needed, 0);
    MultiByteToWideChar(CP_UTF8, 0, str.c_str(), (int)str.length(),
        &wstr[0], size_needed);

    return wstr;
}

std::string Utils::WStringToUTF8(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();

    int size_needed = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(),
        (int)wstr.length(), NULL, 0, NULL, NULL);

    std::string str(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), (int)wstr.length(),
        &str[0], size_needed, NULL, NULL);

    return str;
}

std::vector<BYTE> Utils::AesGcmDecrypt(
    const std::vector<BYTE>& key,
    const std::vector<BYTE>& iv,
    const std::vector<BYTE>& cipher_text,
    const std::vector<BYTE>& auth_tag)
{
    std::vector<BYTE> plain_text;
    BCRYPT_ALG_HANDLE hAlg = NULL;
    BCRYPT_KEY_HANDLE hKey = NULL;
    NTSTATUS status = STATUS_UNSUCCESSFUL;
    DWORD cbData = 0, cbKeyObject = 0, cbResult = 0;
    PBYTE pbKeyObject = NULL;

    try {
        // ���㷨�ṩ����
        status = BCryptOpenAlgorithmProvider(
            &hAlg,
            BCRYPT_AES_ALGORITHM,
            NULL,
            0);
        if (!BCRYPT_SUCCESS(status)) {
            printf("BCryptOpenAlgorithmProviderʧ��: 0x%x\n", status);
            return plain_text;
        }

        // ������ģʽΪGCM
        status = BCryptSetProperty(
            hAlg,
            BCRYPT_CHAINING_MODE,
            (PBYTE)BCRYPT_CHAIN_MODE_GCM,
            sizeof(BCRYPT_CHAIN_MODE_GCM),
            0);
        if (!BCRYPT_SUCCESS(status)) {
            printf("BCryptSetPropertyʧ��: 0x%x\n", status);
            return plain_text;
        }

        // ��ȡ��Կ�����С
        status = BCryptGetProperty(
            hAlg,
            BCRYPT_OBJECT_LENGTH,
            (PBYTE)&cbKeyObject,
            sizeof(DWORD),
            &cbResult,
            0);
        if (!BCRYPT_SUCCESS(status)) {
            printf("BCryptGetPropertyʧ��: 0x%x\n", status);
            return plain_text;
        }

        // ������Կ�����ڴ�
        pbKeyObject = (PBYTE)HeapAlloc(GetProcessHeap(), 0, cbKeyObject);
        if (pbKeyObject == NULL) {
            printf("�ڴ����ʧ��\n");
            return plain_text;
        }

        // ������Կ���
        status = BCryptGenerateSymmetricKey(
            hAlg,
            &hKey,
            pbKeyObject,
            cbKeyObject,
            (PBYTE)key.data(),
            key.size(),
            0);
        if (!BCRYPT_SUCCESS(status)) {
            printf("BCryptGenerateSymmetricKeyʧ��: 0x%x\n", status);
            return plain_text;
        }

        // ׼����֤��Ϣ
        BCRYPT_AUTHENTICATED_CIPHER_MODE_INFO authInfo;
        BCRYPT_INIT_AUTH_MODE_INFO(authInfo);
        authInfo.pbNonce = (PBYTE)iv.data();
        authInfo.cbNonce = iv.size();
        authInfo.pbAuthData = NULL;
        authInfo.cbAuthData = 0;
        authInfo.pbTag = (PBYTE)auth_tag.data();
        authInfo.cbTag = auth_tag.size();

        // ��ȡ���ܺ�����ݴ�С
        status = BCryptDecrypt(
            hKey,
            (PBYTE)cipher_text.data(),
            cipher_text.size(),
            &authInfo,
            NULL,
            0,
            NULL,
            0,
            &cbData,
            0);
        if (!BCRYPT_SUCCESS(status)) {
            printf("BCryptDecrypt (��ȡ��С)ʧ��: 0x%x\n", status);
            return plain_text;
        }

        // ����������ݵ��ڴ�
        plain_text.resize(cbData);

        // ִ�н���
        status = BCryptDecrypt(
            hKey,
            (PBYTE)cipher_text.data(),
            cipher_text.size(),
            &authInfo,
            NULL,
            0,
            plain_text.data(),
            cbData,
            &cbResult,
            0);
        if (!BCRYPT_SUCCESS(status)) {
            printf("BCryptDecryptʧ��: 0x%x", status);
            switch (status) {
            case 0xC000A002:
                printf(" (STATUS_INVALID_SIGNATURE - 认证标签验证失败)\n");
                break;
            case 0xC0000001:
                printf(" (STATUS_UNSUCCESSFUL - 操作失败)\n");
                break;
            case 0xC000000D:
                printf(" (STATUS_INVALID_PARAMETER - 参数无效)\n");
                break;
            case 0xC0000008:
                printf(" (STATUS_INVALID_HANDLE - 句柄无效)\n");
                break;
            default:
                printf(" (未知错误)\n");
                break;
            }
            plain_text.clear();
        }
        else {
            printf("AES-GCM���ܳɹ�,���ܺ��С: %lu �ֽ�\n", cbResult);
            plain_text.resize(cbResult);
        }
    }
    catch (...) {
        printf("AES-GCM���ܹ��̳����쳣\n");
        plain_text.clear();
    }

    // ������Դ
    if (hKey) BCryptDestroyKey(hKey);
    if (hAlg) BCryptCloseAlgorithmProvider(hAlg, 0);
    if (pbKeyObject) HeapFree(GetProcessHeap(), 0, pbKeyObject);

    return plain_text;
}

// AES-GCM解密函数（支持关联数据）
std::vector<BYTE> Utils::AesGcmDecryptWithAAD(
    const std::vector<BYTE>& key,
    const std::vector<BYTE>& iv,
    const std::vector<BYTE>& cipher_text,
    const std::vector<BYTE>& auth_tag,
    const std::vector<BYTE>& aad) {

    std::vector<BYTE> plain_text;

    BCRYPT_ALG_HANDLE hAlg = NULL;
    BCRYPT_KEY_HANDLE hKey = NULL;
    NTSTATUS status = STATUS_UNSUCCESSFUL;

    do {
        // 打开AES算法提供程序
        status = BCryptOpenAlgorithmProvider(&hAlg, BCRYPT_AES_ALGORITHM, NULL, 0);
        if (!BCRYPT_SUCCESS(status)) {
            printf("BCryptOpenAlgorithmProvider失败: 0x%x\n", status);
            break;
        }

        // 设置链模式为GCM
        status = BCryptSetProperty(hAlg, BCRYPT_CHAINING_MODE, (PBYTE)BCRYPT_CHAIN_MODE_GCM, sizeof(BCRYPT_CHAIN_MODE_GCM), 0);
        if (!BCRYPT_SUCCESS(status)) {
            printf("BCryptSetProperty失败: 0x%x\n", status);
            break;
        }

        // 生成密钥对象
        status = BCryptGenerateSymmetricKey(hAlg, &hKey, NULL, 0, (PBYTE)key.data(), (ULONG)key.size(), 0);
        if (!BCRYPT_SUCCESS(status)) {
            printf("BCryptGenerateSymmetricKey失败: 0x%x\n", status);
            break;
        }

        // 设置认证信息
        BCRYPT_AUTHENTICATED_CIPHER_MODE_INFO authInfo;
        BCRYPT_INIT_AUTH_MODE_INFO(authInfo);
        authInfo.pbNonce = (PBYTE)iv.data();
        authInfo.cbNonce = (ULONG)iv.size();
        authInfo.pbTag = (PBYTE)auth_tag.data();
        authInfo.cbTag = (ULONG)auth_tag.size();

        // 设置关联数据
        if (!aad.empty()) {
            authInfo.pbAuthData = (PBYTE)aad.data();
            authInfo.cbAuthData = (ULONG)aad.size();
        }

        // 获取解密后数据的大小
        ULONG cbResult = 0;
        status = BCryptDecrypt(hKey, (PBYTE)cipher_text.data(), (ULONG)cipher_text.size(), &authInfo, NULL, 0, NULL, 0, &cbResult, 0);
        if (!BCRYPT_SUCCESS(status)) {
            printf("BCryptDecrypt获取大小失败: 0x%x", status);
            switch (status) {
            case 0xC000A002:
                printf(" (STATUS_INVALID_SIGNATURE - 认证标签验证失败)\n");
                break;
            case 0xC0000001:
                printf(" (STATUS_UNSUCCESSFUL - 操作失败)\n");
                break;
            case 0xC000000D:
                printf(" (STATUS_INVALID_PARAMETER - 参数无效)\n");
                break;
            case 0xC0000008:
                printf(" (STATUS_INVALID_HANDLE - 句柄无效)\n");
                break;
            default:
                printf(" (未知错误)\n");
                break;
            }
            break;
        }

        // 分配缓冲区并解密
        plain_text.resize(cbResult);
        status = BCryptDecrypt(hKey, (PBYTE)cipher_text.data(), (ULONG)cipher_text.size(), &authInfo, NULL, 0, plain_text.data(), cbResult, &cbResult, 0);
        if (!BCRYPT_SUCCESS(status)) {
            printf("BCryptDecrypt失败: 0x%x", status);
            switch (status) {
            case 0xC000A002:
                printf(" (STATUS_INVALID_SIGNATURE - 认证标签验证失败)\n");
                break;
            case 0xC0000001:
                printf(" (STATUS_UNSUCCESSFUL - 操作失败)\n");
                break;
            case 0xC000000D:
                printf(" (STATUS_INVALID_PARAMETER - 参数无效)\n");
                break;
            case 0xC0000008:
                printf(" (STATUS_INVALID_HANDLE - 句柄无效)\n");
                break;
            default:
                printf(" (未知错误)\n");
                break;
            }
            plain_text.clear();
        }

    } while (false);

    // 清理资源
    if (hKey) {
        BCryptDestroyKey(hKey);
    }
    if (hAlg) {
        BCryptCloseAlgorithmProvider(hAlg, 0);
    }

    return plain_text;
}

void Utils::WriteUTF8BOM(std::ofstream& file) {
    unsigned char bom[] = { 0xEF, 0xBB, 0xBF };
    file.write(reinterpret_cast<char*>(bom), sizeof(bom));
}

std::string Utils::EscapeCSV(const std::wstring& str) {
    std::string utf8Str = WStringToUTF8(str);
    std::string result;
    bool needQuotes = false;

    // ����Ƿ���Ҫ����
    for (char c : utf8Str) {
        if (c == ',' || c == '"' || c == '\n' || c == '\r') {
            needQuotes = true;
            break;
        }
    }

    if (!needQuotes) {
        return utf8Str;
    }

    // �������Ų�ת���Ѵ��ڵ�����
    result.push_back('"');
    for (char c : utf8Str) {
        if (c == '"') {
            result.append("\"\"");
        }
        else {
            result.push_back(c);
        }
    }
    result.push_back('"');

    return result;
}

bool Utils::ExportPasswordsToCSV(const std::vector<PasswordData>& passwords, const std::wstring& filename) {
    try {
        std::ofstream file(filename, std::ios::out | std::ios::binary);
        if (!file.is_open()) {
            printf("�޷��������뵼���ļ�\n");
            return false;
        }

        // д��UTF-8 BOM
        WriteUTF8BOM(file);

        // д��CSV��ͷ
        file << "URL,Username,Password,Create Time\n";

        // д������
        for (const auto& pwd : passwords) {
            file << EscapeCSV(pwd.url) << ","
                << EscapeCSV(pwd.username) << ","
                << EscapeCSV(pwd.password) << ","
                << EscapeCSV(pwd.create_time) << "\n";
        }

        file.close();
        printf("�ɹ����� %zu �������¼��: %ls\n", passwords.size(), filename.c_str());
        return true;
    }
    catch (const std::exception& e) {
        printf("������������ʱ����: %s\n", e.what());
        return false;
    }
}

bool Utils::ExportHistoryToCSV(const std::vector<HistoryData>& history, const std::wstring& filename) {
    try {
        std::ofstream file(filename, std::ios::out | std::ios::binary);
        if (!file.is_open()) {
            printf("�޷�������ʷ��¼�����ļ�\n");
            return false;
        }

        // д��UTF-8 BOM
        WriteUTF8BOM(file);

        // д��CSV��ͷ
        file << "URL,Title,Visit Time,Visit Count\n";

        // д������
        for (const auto& hist : history) {
            file << EscapeCSV(hist.url) << ","
                << EscapeCSV(hist.title) << ","
                << EscapeCSV(hist.visit_time) << ","
                << hist.visit_count << "\n";
        }

        file.close();
        printf("�ɹ����� %zu ����ʷ��¼��: %ls\n", history.size(), filename.c_str());
        return true;
    }
    catch (const std::exception& e) {
        printf("������ʷ��¼ʱ����: %s\n", e.what());
        return false;
    }
}

bool Utils::ExportDownloadsToCSV(const std::vector<DownloadData>& downloads, const std::wstring& filename) {
    try {
        std::ofstream file(filename, std::ios::out | std::ios::binary);
        if (!file.is_open()) {
            printf("�޷��������ؼ�¼�����ļ�\n");
            return false;
        }

        // д��UTF-8 BOM
        WriteUTF8BOM(file);

        // д��CSV��ͷ
        file << "File Path,Download URL,File Size(Bytes),Start Time,End Time\n";

        // д������
        for (const auto& dl : downloads) {
            file << EscapeCSV(dl.file_path) << ","
                << EscapeCSV(dl.url) << ","
                << dl.file_size << ","
                << EscapeCSV(dl.start_time) << ","
                << EscapeCSV(dl.end_time) << "\n";
        }

        file.close();
        printf("�ɹ����� %zu �����ؼ�¼��: %ls\n", downloads.size(), filename.c_str());
        return true;
    }
    catch (const std::exception& e) {
        printf("�������ؼ�¼ʱ����: %s\n", e.what());
        return false;
    }
}

bool Utils::ExportCookieToCSV(const std::vector<CookieData>& passwords, const std::wstring& filename) {
    try {
        std::ofstream file(filename, std::ios::out | std::ios::binary);
        if (!file.is_open()) {
            printf("�޷��������뵼���ļ�\n");
            return false;
        }

        // д��UTF-8 BOM
        WriteUTF8BOM(file);

        // д��CSV��ͷ
        file << "Host,path,cookie\n";

        // д������
        for (const auto& pwd : passwords) {
            file << EscapeCSV(pwd.Host) << ","
                << EscapeCSV(pwd.path) << ","
                << EscapeCSV(pwd.Cookie) << "\n";
        }

        file.close();
        printf("�ɹ����� %zu �������¼��: %ls\n", passwords.size(), filename.c_str());
        return true;
    }
    catch (const std::exception& e) {
        printf("������������ʱ����: %s\n", e.what());
        return false;
    }
}

// Base64编码函数
std::string Utils::Base64Encode(const std::vector<BYTE>& data) {
    if (data.empty()) {
        return "";
    }

    DWORD encodedSize = 0;
    if (!CryptBinaryToStringA(data.data(), data.size(), CRYPT_STRING_BASE64 | CRYPT_STRING_NOCRLF, NULL, &encodedSize)) {
        printf("Base64编码: 获取大小失败，错误码: %d\n", GetLastError());
        return "";
    }

    std::string encoded(encodedSize, '\0');
    if (!CryptBinaryToStringA(data.data(), data.size(), CRYPT_STRING_BASE64 | CRYPT_STRING_NOCRLF, &encoded[0], &encodedSize)) {
        printf("Base64编码: 编码失败，错误码: %d\n", GetLastError());
        return "";
    }

    // 正确处理字符串长度：CryptBinaryToStringA返回的长度包含null终止符
    // 我们需要移除null终止符，但保留Base64填充字符（等号）
    if (encodedSize > 0 && encoded[encodedSize - 1] == '\0') {
        encoded.resize(encodedSize - 1);
    } else {
        encoded.resize(encodedSize);
    }

    return encoded;
}

// 将图标转换为标准BMP格式的二进制数据
// 技术要求：生成标准BMP文件格式（包含完整文件头），32x32像素，支持透明度
std::vector<BYTE> Utils::ExtractIconToBMP(HICON hIcon, int size) {
    std::vector<BYTE> bmpData;

    // 输入验证
    if (!hIcon) {
        return bmpData;
    }

    // 获取设备上下文
    HDC hdc = GetDC(NULL);
    if (!hdc) {
        return bmpData;
    }

    // 创建兼容的内存设备上下文
    HDC hdcMem = CreateCompatibleDC(hdc);
    if (!hdcMem) {
        ReleaseDC(NULL, hdc);
        return bmpData;
    }

    // 创建32位ARGB位图信息（支持透明度）
    BITMAPINFO bmi = {};
    bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
    bmi.bmiHeader.biWidth = size;
    bmi.bmiHeader.biHeight = -size; // 负值表示自上而下的位图
    bmi.bmiHeader.biPlanes = 1;
    bmi.bmiHeader.biBitCount = 32; // 32位支持透明度
    bmi.bmiHeader.biCompression = BI_RGB;

    // 创建DIB位图
    void* pBits = NULL;
    HBITMAP hBitmap = CreateDIBSection(hdcMem, &bmi, DIB_RGB_COLORS, &pBits, NULL, 0);
    if (!hBitmap || !pBits) {
        DeleteDC(hdcMem);
        ReleaseDC(NULL, hdc);
        return bmpData;
    }

    HBITMAP hOldBitmap = (HBITMAP)SelectObject(hdcMem, hBitmap);

    // 填充透明背景（白色，便于图标显示）
    RECT rect = {0, 0, size, size};
    HBRUSH hBrush = CreateSolidBrush(RGB(255, 255, 255));
    FillRect(hdcMem, &rect, hBrush);
    DeleteObject(hBrush);

    // 使用Windows Shell API绘制图标
    BOOL drawResult = DrawIconEx(hdcMem, 0, 0, hIcon, size, size, 0, NULL, DI_NORMAL);

    if (drawResult) {
        // 生成标准BMP文件格式（包含完整文件头）
        DWORD imageSize = size * size * 4; // 32位ARGB
        DWORD fileSize = sizeof(BITMAPFILEHEADER) + sizeof(BITMAPINFOHEADER) + imageSize;

        bmpData.resize(fileSize);

        // 构建BMP文件头
        BITMAPFILEHEADER* pFileHeader = (BITMAPFILEHEADER*)bmpData.data();
        pFileHeader->bfType = 0x4D42; // "BM" - BMP文件标识
        pFileHeader->bfSize = fileSize;
        pFileHeader->bfReserved1 = 0;
        pFileHeader->bfReserved2 = 0;
        pFileHeader->bfOffBits = sizeof(BITMAPFILEHEADER) + sizeof(BITMAPINFOHEADER);

        // 构建BMP信息头
        BITMAPINFOHEADER* pInfoHeader = (BITMAPINFOHEADER*)(bmpData.data() + sizeof(BITMAPFILEHEADER));
        *pInfoHeader = bmi.bmiHeader;
        pInfoHeader->biHeight = size; // 正值表示自下而上的位图（BMP标准）
        pInfoHeader->biSizeImage = imageSize;

        // 复制并转换图像数据（BMP格式要求自下而上存储）
        BYTE* pImageData = bmpData.data() + sizeof(BITMAPFILEHEADER) + sizeof(BITMAPINFOHEADER);
        BYTE* pSourceData = (BYTE*)pBits;

        // 逐行复制并垂直翻转以符合BMP标准
        for (int y = 0; y < size; y++) {
            BYTE* pDestRow = pImageData + (size - 1 - y) * size * 4;
            BYTE* pSourceRow = pSourceData + y * size * 4;
            memcpy(pDestRow, pSourceRow, size * 4);
        }
    }

    // 确保内存资源正确释放
    SelectObject(hdcMem, hOldBitmap);
    DeleteObject(hBitmap);
    DeleteDC(hdcMem);
    ReleaseDC(NULL, hdc);

    return bmpData;
}

// 检查文件是否存在
bool Utils::FileExists(const std::wstring& filePath) {
    DWORD fileAttribs = GetFileAttributesW(filePath.c_str());
    return (fileAttribs != INVALID_FILE_ATTRIBUTES &&
            !(fileAttribs & FILE_ATTRIBUTE_DIRECTORY));
}

// 获取文件图标并转换为Base64编码
// 功能：为浏览器下载历史记录中的文件获取对应的系统图标
// 输入：文件路径（file_path字段）
// 输出：纯Base64编码字符串（32x32像素BMP格式，支持透明度）
std::wstring Utils::GetFileIconAsBase64(const std::wstring& filePath, int iconSize) {
    // 静态缓存，基于文件扩展名避免重复获取相同类型文件的图标
    static std::map<std::wstring, std::wstring> iconCache;

    try {
        // 输入验证
        if (filePath.empty()) {
            return L""; // 返回空字符串，不影响其他数据
        }

        // 提取并标准化文件扩展名
        std::wstring extension;
        size_t dotPos = filePath.find_last_of(L'.');
        if (dotPos != std::wstring::npos && dotPos < filePath.length() - 1) {
            extension = filePath.substr(dotPos);
            std::transform(extension.begin(), extension.end(), extension.begin(), ::towlower);
        }

        // 性能优化：只对不存在的文件使用缓存（避免缓存文件特定的图标）
        if (!FileExists(filePath)) {
            auto cacheIt = iconCache.find(extension);
            if (cacheIt != iconCache.end()) {
                printf("图标获取: 从缓存获取扩展名图标\n");
                return cacheIt->second;
            }
        }

        HICON hIcon = NULL;
        std::wstring iconBase64;
        bool isFileSpecificIcon = false; // 标记是否获取到文件特定的图标

        // 处理逻辑1：如果文件存在，优先获取文件的真实图标
        if (FileExists(filePath)) {
            SHFILEINFOW sfi = {};
            DWORD_PTR result = 0;

            // 首先尝试获取文件的实际图标（不使用缓存的扩展名图标）
            // 使用SHGFI_ICON标志获取文件的真实图标
            result = SHGetFileInfoW(filePath.c_str(), 0, &sfi, sizeof(sfi),
                                   SHGFI_ICON | SHGFI_LARGEICON);

            if (result != 0 && sfi.hIcon) {
                hIcon = sfi.hIcon;
                isFileSpecificIcon = true;
                printf("图标获取: 成功获取文件特定的大图标\n");
            } else {
                // 如果大图标失败，尝试小图标
                result = SHGetFileInfoW(filePath.c_str(), 0, &sfi, sizeof(sfi),
                                       SHGFI_ICON | SHGFI_SMALLICON);
                if (result != 0 && sfi.hIcon) {
                    hIcon = sfi.hIcon;
                    isFileSpecificIcon = true;
                    printf("图标获取: 成功获取文件特定的小图标\n");
                }
            }

            // 对于可执行文件，尝试使用ExtractIcon获取内嵌图标
            if (hIcon == NULL || hIcon == (HICON)1) {
                std::string filePathA = WStringToUTF8(filePath);

                // 检查是否为可执行文件
                std::wstring lowerExt = extension;
                if (lowerExt == L".exe" || lowerExt == L".dll" || lowerExt == L".ico") {
                    HICON extractedIcon = ExtractIconA(GetModuleHandle(NULL), filePathA.c_str(), 0);
                    if (extractedIcon != NULL && extractedIcon != (HICON)1) {
                        if (hIcon != NULL) {
                            DestroyIcon(hIcon); // 释放之前的图标
                        }
                        hIcon = extractedIcon;
                        isFileSpecificIcon = true;
                        printf("图标获取: 成功提取可执行文件内嵌图标\n");
                    }
                }
            }
        }

        // 处理逻辑2：如果文件不存在或无法获取文件特定图标，使用扩展名默认图标
        if (hIcon == NULL || hIcon == (HICON)1) {
            SHFILEINFOW sfi = {};
            DWORD_PTR result = 0;

            if (!extension.empty()) {
                // 根据扩展名获取默认图标
                result = SHGetFileInfoW(extension.c_str(), FILE_ATTRIBUTE_NORMAL, &sfi, sizeof(sfi),
                                       SHGFI_ICON | SHGFI_LARGEICON | SHGFI_USEFILEATTRIBUTES);
                if (result == 0) {
                    result = SHGetFileInfoW(extension.c_str(), FILE_ATTRIBUTE_NORMAL, &sfi, sizeof(sfi),
                                           SHGFI_ICON | SHGFI_SMALLICON | SHGFI_USEFILEATTRIBUTES);
                }

                if (result != 0 && sfi.hIcon) {
                    hIcon = sfi.hIcon;
                    printf("图标获取: 使用扩展名默认图标\n");
                }
            }

            // 最后的回退：使用通用文件图标
            if (hIcon == NULL || hIcon == (HICON)1) {
                result = SHGetFileInfoW(L"*.*", FILE_ATTRIBUTE_NORMAL, &sfi, sizeof(sfi),
                                       SHGFI_ICON | SHGFI_SMALLICON | SHGFI_USEFILEATTRIBUTES);
                if (result != 0 && sfi.hIcon) {
                    hIcon = sfi.hIcon;
                    printf("图标获取: 使用通用文件图标\n");
                }
            }
        }

        // 图标处理和转换
        if (hIcon != NULL && hIcon != (HICON)1) {
            // 将图标转换为标准BMP格式的二进制数据
            std::vector<BYTE> bmpData = ExtractIconToBMP(hIcon, iconSize);

            if (!bmpData.empty()) {
                // 转换为Base64编码
                std::string base64 = Base64Encode(bmpData);
                if (!base64.empty()) {
                    // 返回纯Base64编码字符串（不包含data URI前缀）
                    iconBase64 = UTF8ToWString(base64);

                    // 只缓存扩展名默认图标，不缓存文件特定的图标
                    if (!extension.empty() && !isFileSpecificIcon) {
                        iconCache[extension] = iconBase64;
                        printf("图标获取: 已缓存扩展名图标\n");
                    }
                }
            }

            // 确保内存资源正确释放
            DestroyIcon(hIcon);
        }

        return iconBase64;

    } catch (...) {
        // 异常处理：确保在任何异常情况下都返回空字符串
        return L"";
    }
}

// ==================== 通用工具函数 ====================
// 从ExifManager.cpp移动到此处以避免重复定义

// Windows XP兼容的数字转字符串函数
std::string Utils::NumberToString(int value) {
    std::ostringstream oss;
    oss << value;
    return oss.str();
}

// Windows XP兼容的字符串转整数函数
int Utils::StringToInt(const std::string& str) {
    try {
        return atoi(str.c_str());
    } catch (...) {
        return 0;
    }
}

// 改进的UTF-8字符串清理函数 - 保留有效的UTF-8字符
std::string Utils::CleanUtf8String(const std::string& input) {
    if (input.empty()) {
        return input;
    }

    std::string result;
    result.reserve(input.length() * 2); // 预留更多空间用于转义字符

    for (size_t i = 0; i < input.length(); ++i) {
        unsigned char c = static_cast<unsigned char>(input[i]);

        // 处理ASCII字符
        if (c < 128) {
            // 处理控制字符
            if (c < 32) {
                if (c == '\n') {
                    result += "\\n";
                } else if (c == '\r') {
                    result += "\\r";
                } else if (c == '\t') {
                    result += "\\t";
                } else if (c == '\b') {
                    result += "\\b";
                } else if (c == '\f') {
                    result += "\\f";
                } else if (c == '\0') {
                    // 跳过null字符
                    continue;
                } else {
                    // 其他控制字符用Unicode转义 - Windows XP兼容版本
                    char buffer[8];
#ifdef _MSC_VER
                    sprintf_s(buffer, sizeof(buffer), "\\u%04x", c);
#else
                    sprintf(buffer, "\\u%04x", c);
#endif
                    result += buffer;
                }
            }
            // 处理JSON特殊字符
            else if (c == '"') {
                result += "\\\"";
            } else if (c == '\\') {
                result += "\\\\";
            } else {
                result += c;
            }
        }
        // 处理UTF-8多字节字符
        else {
            // 2字节UTF-8序列
            if ((c & 0xE0) == 0xC0) {
                if (i + 1 < input.length()) {
                    unsigned char c2 = static_cast<unsigned char>(input[i + 1]);
                    if ((c2 & 0xC0) == 0x80) {
                        result += c;
                        result += c2;
                        i += 1;
                        continue;
                    }
                }
            }
            // 3字节UTF-8序列
            else if ((c & 0xF0) == 0xE0) {
                if (i + 2 < input.length()) {
                    unsigned char c2 = static_cast<unsigned char>(input[i + 1]);
                    unsigned char c3 = static_cast<unsigned char>(input[i + 2]);
                    if ((c2 & 0xC0) == 0x80 && (c3 & 0xC0) == 0x80) {
                        result += c;
                        result += c2;
                        result += c3;
                        i += 2;
                        continue;
                    }
                }
            }
            // 4字节UTF-8序列
            else if ((c & 0xF8) == 0xF0) {
                if (i + 3 < input.length()) {
                    unsigned char c2 = static_cast<unsigned char>(input[i + 1]);
                    unsigned char c3 = static_cast<unsigned char>(input[i + 2]);
                    unsigned char c4 = static_cast<unsigned char>(input[i + 3]);
                    if ((c2 & 0xC0) == 0x80 && (c3 & 0xC0) == 0x80 && (c4 & 0xC0) == 0x80) {
                        result += c;
                        result += c2;
                        result += c3;
                        result += c4;
                        i += 3;
                        continue;
                    }
                }
            }

            // 无效的UTF-8字节，替换为?
            result += '?';
        }
    }

    return result;
}

// 专门用于文件路径的清理函数（保留中文字符，不进行JSON转义）
std::string Utils::CleanFilePathString(const std::string& input) {
    if (input.empty()) {
        return input;
    }

    std::string result;
    result.reserve(input.length());

    for (size_t i = 0; i < input.length(); ++i) {
        unsigned char c = static_cast<unsigned char>(input[i]);

        // 处理ASCII字符
        if (c < 128) {
            // 跳过控制字符（除了常见的空白字符）
            if (c < 32 && c != '\t') {
                continue;
            }
            result += c;
        }
        // 处理UTF-8多字节字符（保留中文等字符）
        else {
            // 2字节UTF-8序列
            if ((c & 0xE0) == 0xC0) {
                if (i + 1 < input.length()) {
                    unsigned char c2 = static_cast<unsigned char>(input[i + 1]);
                    if ((c2 & 0xC0) == 0x80) {
                        result += c;
                        result += c2;
                        i += 1;
                        continue;
                    }
                }
            }
            // 3字节UTF-8序列
            else if ((c & 0xF0) == 0xE0) {
                if (i + 2 < input.length()) {
                    unsigned char c2 = static_cast<unsigned char>(input[i + 1]);
                    unsigned char c3 = static_cast<unsigned char>(input[i + 2]);
                    if ((c2 & 0xC0) == 0x80 && (c3 & 0xC0) == 0x80) {
                        result += c;
                        result += c2;
                        result += c3;
                        i += 2;
                        continue;
                    }
                }
            }
            // 4字节UTF-8序列
            else if ((c & 0xF8) == 0xF0) {
                if (i + 3 < input.length()) {
                    unsigned char c2 = static_cast<unsigned char>(input[i + 1]);
                    unsigned char c3 = static_cast<unsigned char>(input[i + 2]);
                    unsigned char c4 = static_cast<unsigned char>(input[i + 3]);
                    if ((c2 & 0xC0) == 0x80 && (c3 & 0xC0) == 0x80 && (c4 & 0xC0) == 0x80) {
                        result += c;
                        result += c2;
                        result += c3;
                        result += c4;
                        i += 3;
                        continue;
                    }
                }
            }

            // 无效的UTF-8字节，跳过
        }
    }

    return result;
}

// 生成带时间戳的JSON文件名 - Windows XP兼容版本
std::string Utils::GenerateJsonFileName() {
    time_t now = time(0);
    struct tm* timeinfo;
    struct tm timeinfo_buf;
#ifdef _MSC_VER
    if (localtime_s(&timeinfo_buf, &now) == 0) {
        timeinfo = &timeinfo_buf;
    } else {
        // 如果localtime_s失败，使用默认时间戳
        timeinfo = nullptr;
    }
#else
    timeinfo = localtime(&now);  // Windows XP兼容
    if (timeinfo) {
        timeinfo_buf = *timeinfo;
        timeinfo = &timeinfo_buf;
    }
#endif

    char timestamp[32];
    if (timeinfo) {
        strftime(timestamp, sizeof(timestamp), "%Y%m%d_%H%M%S", timeinfo);
    } else {
        // 使用安全的字符串复制
#ifdef _MSC_VER
        strncpy_s(timestamp, sizeof(timestamp), "unknown_time", _TRUNCATE);
#else
        strncpy(timestamp, "unknown_time", sizeof(timestamp) - 1);
        timestamp[sizeof(timestamp) - 1] = '\0';
#endif
    }

    return std::string("EXIF_Analysis_") + timestamp + ".json";
}

// 保存JSON结果到文件
bool Utils::SaveJsonToFile(const std::string& jsonContent, const std::string& fileName) {
    try {
        std::ofstream file(fileName, std::ios::out | std::ios::trunc);
        if (!file.is_open()) {
            return false;
        }

        file << jsonContent;
        file.close();
        return true;
    } catch (const std::exception& e) {
        return false;
    }
}

// 创建只包含ASCII字符的安全JSON
nlohmann::json CreateSafeJson(const nlohmann::json& originalJson) {
    nlohmann::json safeJson;

    try {
        // 递归清理JSON中的所有字符串
        for (auto it = originalJson.begin(); it != originalJson.end(); ++it) {
            const std::string& key = it.key();
            std::string safeKey = Utils::CleanUtf8String(key);

            if (it.value().is_string()) {
                std::string originalStr = it.value().get<std::string>();
                safeJson[safeKey] = Utils::CleanUtf8String(originalStr);
            } else if (it.value().is_number()) {
                safeJson[safeKey] = it.value();
            } else if (it.value().is_boolean()) {
                safeJson[safeKey] = it.value();
            } else if (it.value().is_object()) {
                safeJson[safeKey] = CreateSafeJson(it.value());
            } else if (it.value().is_array()) {
                nlohmann::json safeArray = nlohmann::json::array();
                for (const auto& item : it.value()) {
                    if (item.is_string()) {
                        safeArray.push_back(Utils::CleanUtf8String(item.get<std::string>()));
                    } else if (item.is_object()) {
                        safeArray.push_back(CreateSafeJson(item));
                    } else {
                        safeArray.push_back(item);
                    }
                }
                safeJson[safeKey] = safeArray;
            } else {
                safeJson[safeKey] = it.value();
            }
        }
    } catch (...) {
        // 如果处理失败，返回空对象
        return nlohmann::json::object();
    }

    return safeJson;
}

// 安全的JSON转换函数
std::string Utils::SafeJsonDump(const nlohmann::json& json) {
    try {
        return json.dump(4);
    } catch (const nlohmann::json::exception& e) {
        // 如果JSON dump失败，创建清理后的版本
        try {
            nlohmann::json safeJson = CreateSafeJson(json);
            return safeJson.dump(4);
        } catch (...) {
            // 最后的备用方案
            nlohmann::json errorJson;
            errorJson["status"] = "error";
            errorJson["message"] = "JSON encoding error";
            errorJson["error_code"] = e.id;
            errorJson["error_message"] = "Failed to encode JSON due to invalid characters";

            try {
                return errorJson.dump(4);
            } catch (...) {
                return "{\"status\":\"error\",\"message\":\"Critical JSON encoding failure\"}";
            }
        }
    }
}