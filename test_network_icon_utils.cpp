#include "pch.h"
#include "include/NetworkConnectionManager.h"
#include "include/Utils.h"
#include <iostream>
#include <string>

int main() {
    std::cout << u8"=== 测试 NetworkConnectionManager 中的图标获取是否使用 Utils ===" << std::endl;
    
    try {
        // 创建网络连接管理器实例
        NetworkConnectionManager networkManager;
        
        // 测试1: 直接测试Utils中的图标获取方法
        std::cout << u8"\n测试1: 直接使用 Utils::GetFileIconAsBase64" << std::endl;
        std::wstring testPath = L"C:\\Windows\\System32\\notepad.exe";
        std::wstring iconBase64 = Utils::GetFileIconAsBase64(testPath, 32);
        
        if (!iconBase64.empty()) {
            std::cout << u8"✓ Utils::GetFileIconAsBase64 工作正常" << std::endl;
            std::cout << u8"  图标数据大小: " << iconBase64.length() << u8" 字符" << std::endl;
        } else {
            std::cout << u8"✗ Utils::GetFileIconAsBase64 返回空结果" << std::endl;
        }
        
        // 测试2: 测试NetworkConnectionManager中的GetProcessIconBase64方法
        std::cout << u8"\n测试2: 使用 NetworkConnectionManager::GetProcessIconBase64" << std::endl;
        std::string testPathUtf8 = "C:\\Windows\\System32\\notepad.exe";
        
        // 使用反射调用私有方法（这里我们需要创建一个测试友元或公共包装方法）
        // 由于GetProcessIconBase64是私有方法，我们通过获取网络进程信息来间接测试
        std::cout << u8"  通过获取网络进程信息来测试图标获取..." << std::endl;
        
        // 获取所有网络进程（这会间接调用GetProcessIconBase64）
        auto networkProcesses = networkManager.GetAllNetworkProcesses();
        
        std::cout << u8"  找到 " << networkProcesses.size() << u8" 个网络进程" << std::endl;
        
        // 检查是否有进程获取到了图标
        int processesWithIcons = 0;
        for (const auto& process : networkProcesses) {
            if (!process.iconBase64.empty() && process.iconBase64 != "icon_available") {
                processesWithIcons++;
                std::cout << u8"  ✓ 进程 " << process.processName 
                          << u8" 获取到图标，大小: " << process.iconBase64.length() << u8" 字符" << std::endl;
                break; // 只显示第一个成功的例子
            }
        }
        
        if (processesWithIcons > 0) {
            std::cout << u8"✓ NetworkConnectionManager 成功使用 Utils 获取图标" << std::endl;
        } else {
            std::cout << u8"⚠ 没有网络进程获取到图标数据（可能没有活跃的网络连接）" << std::endl;
        }
        
        // 测试3: 验证字符串转换方法
        std::cout << u8"\n测试3: 验证字符串转换方法" << std::endl;
        std::string utf8Test = "测试字符串";
        std::wstring wideTest = L"测试宽字符串";
        
        // 测试UTF8到宽字符转换
        std::wstring convertedWide = Utils::UTF8ToWString(utf8Test);
        std::string convertedUtf8 = Utils::WStringToUTF8(wideTest);
        
        if (!convertedWide.empty() && !convertedUtf8.empty()) {
            std::cout << u8"✓ Utils 字符串转换方法工作正常" << std::endl;
        } else {
            std::cout << u8"✗ Utils 字符串转换方法有问题" << std::endl;
        }
        
        // 测试4: 测试Base64编码
        std::cout << u8"\n测试4: 测试 Utils::Base64Encode" << std::endl;
        std::vector<BYTE> testData = {0x48, 0x65, 0x6C, 0x6C, 0x6F}; // "Hello"
        std::string base64Result = Utils::Base64Encode(testData);
        
        if (!base64Result.empty()) {
            std::cout << u8"✓ Utils::Base64Encode 工作正常" << std::endl;
            std::cout << u8"  编码结果: " << base64Result << std::endl;
        } else {
            std::cout << u8"✗ Utils::Base64Encode 返回空结果" << std::endl;
        }
        
        std::cout << u8"\n=== 测试完成 ===" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << u8"\n✗ 测试失败 - 标准异常: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << u8"\n✗ 测试失败 - 未知异常" << std::endl;
        return 1;
    }
    
    std::cout << u8"\n按任意键退出..." << std::endl;
    system("pause");
    return 0;
}
