﻿#define _CRT_SECURE_NO_WARNINGS
#include "pch.h"
#include "../include/FirewallManager.h"
#include <iostream>
#include <sstream>
#include <memory>
#include <ctime>
#include <iomanip>
#include <fstream>
#include <set>
#include <algorithm>
#include <chrono>
#include <regex>
#include <winsvc.h>
#include <windows.h>
#include <objbase.h>

// Remove WMI dependency, use netsh and registry combination method
#pragma comment(lib, "advapi32.lib")
#pragma comment(lib, "ole32.lib")

FirewallManager::FirewallManager() : m_initialized(false), m_useRegistryMethod(false) {
}

FirewallManager::~FirewallManager() {
    Cleanup();
}

bool FirewallManager::Initialize() {
    if (m_initialized) {
        return true;
    }

    m_startTime = std::chrono::system_clock::now();
    
    // Initialize COM
    HRESULT hr = CoInitializeEx(0, COINIT_MULTITHREADED);
    if (FAILED(hr)) {
        std::cout << "Failed to initialize COM: " << hr << std::endl;
        return false;
    }

    // Select appropriate initialization method based on current OS
    if (InitializeForCurrentOS()) {
        m_initialized = true;
        return true;
    }

    CoUninitialize();
    return false;
}

// Static methods: Detect Windows version and API availability
bool FirewallManager::IsWindowsXP() {
    OSVERSIONINFO osvi;
    ZeroMemory(&osvi, sizeof(OSVERSIONINFO));
    osvi.dwOSVersionInfoSize = sizeof(OSVERSIONINFO);
    
    if (GetVersionEx(&osvi)) {
        return (osvi.dwMajorVersion == 5);
    }
    return false;
}

bool FirewallManager::IsNetshAvailable() {
    char systemPath[MAX_PATH];
    if (GetSystemDirectoryA(systemPath, MAX_PATH) > 0) {
        std::string netshPath = std::string(systemPath) + "\\netsh.exe";
        DWORD dwAttrib = GetFileAttributesA(netshPath.c_str());
        return (dwAttrib != INVALID_FILE_ATTRIBUTES && !(dwAttrib & FILE_ATTRIBUTE_DIRECTORY));
    }
    return false;
}

bool FirewallManager::IsAdvancedFirewallAvailable() {
    return !IsWindowsXP();
}

bool FirewallManager::InitializeForCurrentOS() {
    if (IsWindowsXP()) {
        std::cout << "Using Registry method for Windows XP firewall" << std::endl;
        m_useRegistryMethod = true;
        return true;
    } else {
        std::cout << "Using netsh+registry method for modern Windows firewall" << std::endl;
        m_useRegistryMethod = false;
        
        if (IsNetshAvailable()) {
            std::cout << "netsh.exe available, using netsh+registry method" << std::endl;
            return true;
        } else {
            std::cout << "netsh.exe not available, using registry fallback" << std::endl;
            m_useRegistryMethod = true;
            return true;
        }
    }
}

void FirewallManager::Cleanup() {
    if (m_initialized) {
        CoUninitialize();
        m_initialized = false;
    }
}

std::vector<FirewallRuleData> FirewallManager::GetAllFirewallRules() {
    if (!m_initialized) {
        LogError("FirewallManager not initialized");
        return std::vector<FirewallRuleData>();
    }
    return GetRulesForCurrentOS();
}

std::vector<FirewallRuleData> FirewallManager::GetRulesForCurrentOS() {
    if (m_useRegistryMethod) {
        return GetFirewallRulesViaRegistryXP();
    } else {
        std::vector<FirewallRuleData> allRules;
        std::set<std::string> processedRules;

        try {
            std::vector<FirewallRuleData> netshRules = GetFirewallRulesViaNetsh();
            for (auto& rule : netshRules) {
                if (processedRules.find(rule.name) == processedRules.end()) {
                    allRules.push_back(rule);
                    processedRules.insert(rule.name);
                }
            }
            
            std::vector<FirewallRuleData> registryRules = GetFirewallRulesViaAdvancedRegistry();
            for (auto& rule : registryRules) {
                if (processedRules.find(rule.name) == processedRules.end()) {
                    allRules.push_back(rule);
                    processedRules.insert(rule.name);
                }
            }
            
        } catch (const std::exception& e) {
            LogError("Error in GetRulesForCurrentOS: " + std::string(e.what()));
        }

        return allRules;
    }
}

std::vector<FirewallProfileData> FirewallManager::GetFirewallProfiles() {
    if (!m_initialized) {
        return std::vector<FirewallProfileData>();
    }
    return GetProfilesForCurrentOS();
}

std::vector<FirewallProfileData> FirewallManager::GetProfilesForCurrentOS() {
    if (m_useRegistryMethod) {
        return GetFirewallProfilesViaRegistryXP();
    } else {
        std::vector<FirewallProfileData> profiles;
        try {
            std::vector<FirewallProfileData> netshProfiles = GetProfilesViaNetsh();
            profiles.insert(profiles.end(), netshProfiles.begin(), netshProfiles.end());
            
            std::vector<FirewallProfileData> registryProfiles = GetProfilesViaAdvancedRegistry();
            for (auto& registryProfile : registryProfiles) {
                bool found = false;
                for (auto& netshProfile : profiles) {
                    if (netshProfile.name == registryProfile.name) {
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    profiles.push_back(registryProfile);
                }
            }
        } catch (const std::exception& e) {
            LogError("Error getting firewall profiles: " + std::string(e.what()));
        }
        return profiles;
    }
}

std::vector<FirewallServiceData> FirewallManager::GetFirewallServices() {
    if (!m_initialized) {
        return std::vector<FirewallServiceData>();
    }
    
    if (m_useRegistryMethod) {
        return GetFirewallServicesViaRegistryXP();
    } else {
        return GetFirewallServicesViaNetsh();
    }
}

FirewallStatistics FirewallManager::GetFirewallStatistics() {
    if (m_useRegistryMethod) {
        return GetFirewallStatisticsViaRegistryXP();
    } else {
        return GetFirewallStatisticsViaNetsh();
    }
}

std::vector<FirewallLogEntry> FirewallManager::GetFirewallLogEntries(int maxEntries) {
    return std::vector<FirewallLogEntry>(); // 简化实现
}

nlohmann::json FirewallManager::GetFirewallInfoAsJson() {
    nlohmann::json result;
    
    try {
        std::vector<FirewallRuleData> rules = GetAllFirewallRules();
        std::vector<FirewallProfileData> profiles = GetFirewallProfiles();
        std::vector<FirewallServiceData> services = GetFirewallServices();
        FirewallStatistics stats = GetFirewallStatistics();

        result["rules"] = rules;
        result["profiles"] = profiles;
        result["services"] = services;
        result["statistics"] = stats;
        
        // 元数据
        result["metadata"]["tool_name"] = "Windows Firewall Scanner";
        result["metadata"]["version"] = "4.0";
        result["metadata"]["scan_method"] = m_useRegistryMethod ? "Registry (XP)" : "netsh + Registry (No WMI)";
        result["metadata"]["windows_xp_compatible"] = true;
        result["metadata"]["wmi_dependency"] = false;
        result["metadata"]["scan_time"] = std::time(nullptr);
        
    } catch (const std::exception& e) {
        LogError("Error creating JSON: " + std::string(e.what()));
    }
    
    return result;
}

bool FirewallManager::SaveFirewallInfoToFile(const std::string& filename) {
    try {
        nlohmann::json data = GetFirewallInfoAsJson();
        std::ofstream file(filename);
        file << data.dump(4);
        return true;
    } catch (const std::exception& e) {
        LogError("Error saving to file: " + std::string(e.what()));
        return false;
    }
}

void FirewallManager::LogError(const std::string& error) {
    m_lastError = error;
    std::cout << "FirewallManager Error: " << error << std::endl;
}

std::string FirewallManager::GetLastErrorString() {
    return m_lastError;
}


// 简化的方法实现（避免编译错误）
std::vector<FirewallRuleData> FirewallManager::GetFirewallRulesViaNetsh() {
    std::vector<FirewallRuleData> rules;

    try {
        std::string command = "netsh advfirewall firewall show rule name=all verbose";
        std::string output = ExecuteCommand(command);

        if (!output.empty()) {
            // 解析netsh输出，提取真实的防火墙规则
            std::cout << "=== NETSH OUTPUT DEBUG ===" << std::endl;
            std::cout << "Raw output length: " << output.length() << " characters" << std::endl;
            std::cout << "First 1000 characters of netsh output:" << std::endl;
            std::cout << "----------------------------------------" << std::endl;
            size_t previewLen = (output.length() > 1000) ? 1000 : output.length();
            std::string preview = output.substr(0, previewLen);
            std::cout << preview << std::endl;
            std::cout << "----------------------------------------" << std::endl;

            rules = ParseNetshFirewallOutput(output);
            std::cout << "Parsed " << rules.size() << " rules from netsh output" << std::endl;

            if (rules.empty()) {
                std::cout << "WARNING: No rules parsed! Checking for common patterns..." << std::endl;
                if (output.find("规则名称") != std::string::npos) {
                    std::cout << "Found '规则名称' in output - Chinese rules detected!" << std::endl;
                }
                if (output.find("Rule Name") != std::string::npos) {
                    std::cout << "Found 'Rule Name' in output - English rules detected!" << std::endl;
                }
                if (output.find("瑙勫垯鍚嶇О") != std::string::npos) {
                    std::cout << "Found garbled Chinese characters - encoding issue detected!" << std::endl;
                    std::cout << "Attempting to fix encoding and re-parse..." << std::endl;
                    // 尝试重新解析乱码的输出
                    rules = ParseGarbledNetshOutput(output);
                    std::cout << "Re-parsed " << rules.size() << " rules from garbled output" << std::endl;
                }
                if (output.find("No rules match") != std::string::npos || output.find("没有匹配") != std::string::npos) {
                    std::cout << "netsh reports no matching rules" << std::endl;
                }
            }
            std::cout << "=== END NETSH DEBUG ===" << std::endl;
        } else {
            std::cout << "ERROR: No netsh output received, netsh may not be available" << std::endl;
        }

    } catch (const std::exception& e) {
        LogError("Error in GetFirewallRulesViaNetsh: " + std::string(e.what()));
    }

    return rules;
}

std::vector<FirewallRuleData> FirewallManager::GetFirewallRulesViaAdvancedRegistry() {
    std::vector<FirewallRuleData> rules;

    try {
        std::cout << "Getting advanced registry firewall rules..." << std::endl;

        // Windows Vista+ 高级防火墙注册表路径
        std::string firewallPath = "SYSTEM\\CurrentControlSet\\Services\\SharedAccess\\Parameters\\FirewallPolicy";

        // 检查不同的配置文件
        std::vector<std::string> profiles = {"DomainProfile", "StandardProfile", "PublicProfile"};

        for (const auto& profile : profiles) {
            std::string profilePath = firewallPath + "\\" + profile;

            // 获取入站规则
            std::string inboundPath = profilePath + "\\FirewallRules";
            auto inboundRules = GetAdvancedRegistryRules(inboundPath, profile, "In");
            rules.insert(rules.end(), inboundRules.begin(), inboundRules.end());

            // 获取出站规则
            auto outboundRules = GetAdvancedRegistryRules(inboundPath, profile, "Out");
            rules.insert(rules.end(), outboundRules.begin(), outboundRules.end());
        }

        std::cout << "Retrieved " << rules.size() << " rules from advanced registry" << std::endl;

    } catch (const std::exception& e) {
        LogError("Error in GetFirewallRulesViaAdvancedRegistry: " + std::string(e.what()));
    }

    return rules;
}

std::vector<FirewallRuleData> FirewallManager::GetFirewallRulesViaRegistry() {
    std::vector<FirewallRuleData> rules;

    try {
        std::cout << "Getting basic registry firewall rules..." << std::endl;

        // 基础注册表防火墙规则（通用方法）
        // 这个方法主要用于获取一些基础的防火墙配置信息

        // 检查防火墙是否启用
        std::string regPath = "SYSTEM\\CurrentControlSet\\Services\\SharedAccess\\Parameters\\FirewallPolicy";

        // 尝试读取一些基础的防火墙设置
        HKEY hKey;
        if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, regPath.c_str(), 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
            // 这里可以添加读取基础防火墙配置的逻辑
            // 但由于现代Windows的防火墙规则主要通过netsh获取，这里保持简单
            RegCloseKey(hKey);
        }

        std::cout << "Retrieved " << rules.size() << " rules from basic registry" << std::endl;

    } catch (const std::exception& e) {
        LogError("Error in GetFirewallRulesViaRegistry: " + std::string(e.what()));
    }

    return rules;
}

std::vector<FirewallProfileData> FirewallManager::GetProfilesViaNetsh() {
    std::vector<FirewallProfileData> profiles;

    try {
        // 获取真实的防火墙配置文件信息
        std::vector<std::string> profileNames = {"domainprofile", "privateprofile", "publicprofile"};
        std::vector<std::string> displayNames = {"Domain", "Private", "Public"};

        for (size_t i = 0; i < profileNames.size(); ++i) {
            std::string command = "netsh advfirewall show " + profileNames[i];
            std::string output = ExecuteCommand(command);

            if (!output.empty()) {
                FirewallProfileData profile = ParseNetshProfileOutput(output, displayNames[i]);
                if (!profile.name.empty()) {
                    profiles.push_back(profile);
                }
            }
        }

        // 如果netsh失败，提供基本的配置文件
        if (profiles.empty()) {
            FirewallProfileData profile;
            profile.name = "Domain";
            profile.enabled = true;
            profile.default_inbound_action_block = true;
            profile.default_outbound_action_block = false;
            profiles.push_back(profile);
        }

    } catch (const std::exception& e) {
        LogError("Error in GetProfilesViaNetsh: " + std::string(e.what()));
    }

    return profiles;
}

std::vector<FirewallProfileData> FirewallManager::GetProfilesViaAdvancedRegistry() {
    std::vector<FirewallProfileData> profiles;

    try {
        // 简化实现
        FirewallProfileData profile;
        profile.name = "Private";
        // profile.description = "Private profile via advanced registry"; // 字段不存在
        profile.enabled = true;
        profile.default_inbound_action_block = true;
        profile.default_outbound_action_block = false;
        profiles.push_back(profile);

    } catch (const std::exception& e) {
        LogError("Error in GetProfilesViaAdvancedRegistry: " + std::string(e.what()));
    }

    return profiles;
}

std::string FirewallManager::ExecuteCommand(const std::string& command) {
    std::string result;

    try {
        std::cout << "Executing command: " << command << std::endl;

        // 设置命令使用UTF-8编码输出
        std::string utf8Command = "chcp 65001 >nul && " + command;

        FILE* pipe = _popen(utf8Command.c_str(), "r");
        if (!pipe) {
            std::cout << "Failed to open pipe for command: " << command << std::endl;
            return "";
        }

        char buffer[1024]; // 增大缓冲区
        while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
            result += buffer;
        }

        int exitCode = _pclose(pipe);
        std::cout << "Command completed with exit code: " << exitCode << std::endl;
        std::cout << "Output length: " << result.length() << " characters" << std::endl;

        if (result.empty()) {
            std::cout << "Warning: Command returned empty output" << std::endl;
        }

    } catch (const std::exception& e) {
        LogError("Error executing command: " + std::string(e.what()));
    }

    return result;
}

// Windows XP 防火墙实现
std::vector<FirewallRuleData> FirewallManager::GetFirewallRulesViaRegistryXP() {
    std::vector<FirewallRuleData> rules;

    try {
        std::cout << "Getting Windows XP firewall rules..." << std::endl;

        // 1. 首先尝试使用netsh firewall命令（XP专用）
        auto netshRules = GetXPFirewallRulesViaNetsh();
        rules.insert(rules.end(), netshRules.begin(), netshRules.end());

        // 2. 然后从注册表获取详细信息
        auto registryRules = GetXPFirewallRulesViaRegistry();
        rules.insert(rules.end(), registryRules.begin(), registryRules.end());

        std::cout << "Retrieved " << rules.size() << " XP firewall rules total" << std::endl;

    } catch (const std::exception& e) {
        LogError("Error in GetFirewallRulesViaRegistryXP: " + std::string(e.what()));
    }

    return rules;
}

std::vector<FirewallProfileData> FirewallManager::GetFirewallProfilesViaRegistryXP() {
    std::vector<FirewallProfileData> profiles;

    try {
        FirewallProfileData profile;
        profile.name = "Standard";
        // profile.description = "Windows XP Standard Firewall Profile"; // 字段不存在
        profile.enabled = IsXPFirewallEnabled();
        profile.default_inbound_action_block = profile.enabled;
        profile.default_outbound_action_block = false;
        profile.notifications_disabled = !GetXPFirewallNotifications();
        profiles.push_back(profile);

    } catch (const std::exception& e) {
        LogError("Error in GetFirewallProfilesViaRegistryXP: " + std::string(e.what()));
    }

    return profiles;
}

std::vector<FirewallServiceData> FirewallManager::GetFirewallServicesViaRegistryXP() {
    std::vector<FirewallServiceData> services;

    try {
        FirewallServiceData service;
        service.service_name = "SharedAccess";
        service.display_name = "Windows Firewall/Internet Connection Sharing (ICS)";
        service.description = "Provides network address translation, addressing, name resolution and/or intrusion prevention services for a home or small office network.";
        service.status = IsXPFirewallEnabled() ? "Running" : "Stopped";
        service.startup_type = "Automatic";
        services.push_back(service);

    } catch (const std::exception& e) {
        LogError("Error in GetFirewallServicesViaRegistryXP: " + std::string(e.what()));
    }

    return services;
}

std::vector<FirewallServiceData> FirewallManager::GetFirewallServicesViaNetsh() {
    std::vector<FirewallServiceData> services;

    try {
        // 获取真实的防火墙服务状态
        std::string command = "sc query MpsSvc";
        std::string output = ExecuteCommand(command);

        FirewallServiceData service;
        service.service_name = "MpsSvc";
        service.display_name = "Windows Defender Firewall";
        service.description = "Windows Defender Firewall helps protect your computer by preventing unauthorized users from gaining access to your computer through the Internet or a network.";
        service.startup_type = "Automatic";

        // 解析sc query输出获取真实状态
        if (output.find("RUNNING") != std::string::npos) {
            service.status = "Running";
            service.is_running = true;
        } else if (output.find("STOPPED") != std::string::npos) {
            service.status = "Stopped";
            service.is_running = false;
        } else if (output.find("PAUSED") != std::string::npos) {
            service.status = "Paused";
            service.is_running = false;
        } else {
            service.status = "Unknown";
            service.is_running = false;
        }

        services.push_back(service);

    } catch (const std::exception& e) {
        LogError("Error in GetFirewallServicesViaNetsh: " + std::string(e.what()));
    }

    return services;
}

FirewallStatistics FirewallManager::GetFirewallStatisticsViaRegistryXP() {
    FirewallStatistics stats;

    try {
        auto rules = GetFirewallRulesViaRegistryXP();
        auto profiles = GetFirewallProfilesViaRegistryXP();

        stats.total_rules = static_cast<int>(rules.size());
        stats.enabled_rules = 0;
        stats.disabled_rules = 0;
        stats.inbound_rules = 0;
        stats.outbound_rules = 0;
        stats.allow_rules = 0;
        stats.block_rules = 0;

        for (const auto& rule : rules) {
            if (rule.enabled) stats.enabled_rules++;
            else stats.disabled_rules++;

            if (rule.direction == "In") stats.inbound_rules++;
            else if (rule.direction == "Out") stats.outbound_rules++;

            if (rule.action == "Allow") stats.allow_rules++;
            else stats.block_rules++;
        }

        // 配置文件统计信息（简化）
        // stats.total_profiles = static_cast<int>(profiles.size());
        // stats.active_profiles = 0;
        // for (const auto& profile : profiles) {
        //     if (profile.enabled) stats.active_profiles++;
        // }

        stats.scan_time = std::time(nullptr);

    } catch (const std::exception& e) {
        LogError("Error in GetFirewallStatisticsViaRegistryXP: " + std::string(e.what()));
    }

    return stats;
}

FirewallStatistics FirewallManager::GetFirewallStatisticsViaNetsh() {
    FirewallStatistics stats;

    try {
        auto rules = GetRulesForCurrentOS();
        auto profiles = GetProfilesForCurrentOS();

        stats.total_rules = static_cast<int>(rules.size());
        stats.enabled_rules = 0;
        stats.disabled_rules = 0;
        stats.inbound_rules = 0;
        stats.outbound_rules = 0;
        stats.allow_rules = 0;
        stats.block_rules = 0;

        for (const auto& rule : rules) {
            if (rule.enabled) stats.enabled_rules++;
            else stats.disabled_rules++;

            if (rule.direction == "In") stats.inbound_rules++;
            else if (rule.direction == "Out") stats.outbound_rules++;

            if (rule.action == "Allow") stats.allow_rules++;
            else stats.block_rules++;
        }

        // 配置文件统计信息（简化）
        // stats.total_profiles = static_cast<int>(profiles.size());
        // stats.active_profiles = 0;
        // for (const auto& profile : profiles) {
        //     if (profile.enabled) stats.active_profiles++;
        // }

        stats.scan_time = std::time(nullptr);

    } catch (const std::exception& e) {
        LogError("Error in GetFirewallStatisticsViaNetsh: " + std::string(e.what()));
    }

    return stats;
}

// XP注册表操作实现
std::string FirewallManager::GetXPFirewallRegistryPath() {
    return "SYSTEM\\CurrentControlSet\\Services\\SharedAccess\\Parameters\\FirewallPolicy\\StandardProfile";
}

bool FirewallManager::IsXPFirewallEnabled() {
    std::string regPath = GetXPFirewallRegistryPath();
    DWORD enabled = ReadRegistryDWORD(HKEY_LOCAL_MACHINE, regPath, "EnableFirewall");
    return (enabled == 1);
}

std::vector<std::string> FirewallManager::GetXPFirewallExceptions() {
    std::vector<std::string> exceptions;

    try {
        std::string regPath = GetXPFirewallRegistryPath() + "\\AuthorizedApplications\\List";
        HKEY hKey;

        if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, regPath.c_str(), 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
            DWORD index = 0;
            char valueName[256];
            DWORD valueNameSize;

            while (true) {
                valueNameSize = sizeof(valueName);
                LONG result = RegEnumValueA(hKey, index, valueName, &valueNameSize, nullptr, nullptr, nullptr, nullptr);

                if (result == ERROR_SUCCESS) {
                    std::string appPath = ReadRegistryString(hKey, "", valueName);
                    if (!appPath.empty()) {
                        size_t colonPos = appPath.find(':');
                        if (colonPos != std::string::npos) {
                            std::string path = appPath.substr(0, colonPos);
                            exceptions.push_back(path);
                        } else {
                            exceptions.push_back(appPath);
                        }
                    }
                    index++;
                } else {
                    break;
                }
            }

            RegCloseKey(hKey);
        }

    } catch (const std::exception& e) {
        LogError("Error in GetXPFirewallExceptions: " + std::string(e.what()));
    }

    return exceptions;
}

std::vector<std::string> FirewallManager::GetXPFirewallServices() {
    std::vector<std::string> services;
    services.push_back("SharedAccess");
    return services;
}

std::vector<std::string> FirewallManager::GetXPFirewallPorts() {
    std::vector<std::string> ports;

    try {
        std::string regPath = GetXPFirewallRegistryPath() + "\\GloballyOpenPorts\\List";
        HKEY hKey;

        if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, regPath.c_str(), 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
            DWORD index = 0;
            char valueName[256];
            DWORD valueNameSize;

            while (true) {
                valueNameSize = sizeof(valueName);
                LONG result = RegEnumValueA(hKey, index, valueName, &valueNameSize, nullptr, nullptr, nullptr, nullptr);

                if (result == ERROR_SUCCESS) {
                    ports.push_back(std::string(valueName));
                    index++;
                } else {
                    break;
                }
            }

            RegCloseKey(hKey);
        }

    } catch (const std::exception& e) {
        LogError("Error in GetXPFirewallPorts: " + std::string(e.what()));
    }

    return ports;
}

std::string FirewallManager::GetXPFirewallProfile() {
    return "Standard";
}

bool FirewallManager::GetXPFirewallNotifications() {
    std::string regPath = GetXPFirewallRegistryPath();
    DWORD notifications = ReadRegistryDWORD(HKEY_LOCAL_MACHINE, regPath, "DisableNotifications");
    return (notifications == 0);
}

bool FirewallManager::GetXPFirewallUnicastResponses() {
    std::string regPath = GetXPFirewallRegistryPath();
    DWORD unicast = ReadRegistryDWORD(HKEY_LOCAL_MACHINE, regPath, "DisableUnicastResponsesToMulticastBroadcast");
    return (unicast == 0);
}

// 注册表操作辅助方法
std::string FirewallManager::ReadRegistryString(HKEY hKey, const std::string& subKey, const std::string& valueName) {
    HKEY hSubKey = hKey;
    bool needClose = false;

    if (!subKey.empty()) {
        if (RegOpenKeyExA(hKey, subKey.c_str(), 0, KEY_READ, &hSubKey) != ERROR_SUCCESS) {
            return "";
        }
        needClose = true;
    }

    char buffer[1024];
    DWORD bufferSize = sizeof(buffer);
    DWORD type;

    LONG result = RegQueryValueExA(hSubKey, valueName.c_str(), nullptr, &type, (LPBYTE)buffer, &bufferSize);

    if (needClose) {
        RegCloseKey(hSubKey);
    }

    if (result == ERROR_SUCCESS && (type == REG_SZ || type == REG_EXPAND_SZ)) {
        return std::string(buffer);
    }

    return "";
}

DWORD FirewallManager::ReadRegistryDWORD(HKEY hKey, const std::string& subKey, const std::string& valueName) {
    HKEY hSubKey = hKey;
    bool needClose = false;

    if (!subKey.empty()) {
        if (RegOpenKeyExA(hKey, subKey.c_str(), 0, KEY_READ, &hSubKey) != ERROR_SUCCESS) {
            return 0;
        }
        needClose = true;
    }

    DWORD value = 0;
    DWORD valueSize = sizeof(value);
    DWORD type;

    LONG result = RegQueryValueExA(hSubKey, valueName.c_str(), nullptr, &type, (LPBYTE)&value, &valueSize);

    if (needClose) {
        RegCloseKey(hSubKey);
    }

    if (result == ERROR_SUCCESS && type == REG_DWORD) {
        return value;
    }

    return 0;
}

bool FirewallManager::ReadRegistryBool(HKEY hKey, const std::string& subKey, const std::string& valueName) {
    return (ReadRegistryDWORD(hKey, subKey, valueName) != 0);
}

// 缺少的方法实现（简化版本）
std::vector<FirewallServiceData> FirewallManager::GetFirewallServicesInfo() {
    return GetFirewallServices();
}

FirewallServiceData FirewallManager::GetServiceDetails(const std::string& serviceName) {
    FirewallServiceData service;
    service.service_name = serviceName;
    service.display_name = serviceName;
    service.status = "Unknown";
    service.startup_type = "Unknown";
    return service;
}

std::vector<FirewallLogEntry> FirewallManager::ParseFirewallLog(const std::string& logPath, int maxEntries) {
    return std::vector<FirewallLogEntry>();
}

FirewallLogEntry FirewallManager::ParseLogLine(const std::string& line) {
    return FirewallLogEntry();
}

bool FirewallManager::IsFirewallEnabled() {
    if (IsWindowsXP()) {
        return IsXPFirewallEnabled();
    }
    return true; // 简化实现
}

std::string FirewallManager::GetFirewallStatus() {
    return IsFirewallEnabled() ? "Enabled" : "Disabled";
}

bool FirewallManager::IsWindowsFirewallServiceRunning() {
    return true; // 简化实现
}

FirewallRuleData FirewallManager::GetRuleDetails(const std::string& ruleName) {
    FirewallRuleData rule;
    rule.name = ruleName;
    rule.description = "Rule details";
    rule.enabled = true;
    return rule;
}

std::string FirewallManager::GetRuleDirection(int direction) {
    switch (direction) {
        case 1: return "In";
        case 2: return "Out";
        default: return "Both";
    }
}

std::string FirewallManager::GetRuleAction(int action) {
    switch (action) {
        case 0: return "Block";
        case 1: return "Allow";
        default: return "Unknown";
    }
}

std::string FirewallManager::GetRuleProtocol(int protocol) {
    switch (protocol) {
        case 6: return "TCP";
        case 17: return "UDP";
        case 1: return "ICMP";
        default: return "Any";
    }
}

std::string FirewallManager::GetRuleProfiles(int profiles) {
    std::string result;
    if (profiles & 1) result += "Domain,";
    if (profiles & 2) result += "Private,";
    if (profiles & 4) result += "Public,";
    if (!result.empty()) result.pop_back(); // 移除最后的逗号
    return result.empty() ? "All" : result;
}

std::vector<std::string> FirewallManager::GetConflictingRules() {
    return std::vector<std::string>();
}

nlohmann::json FirewallManager::GenerateSecurityReport() {
    nlohmann::json report;
    report["status"] = "OK";
    report["message"] = "Security report generated";
    return report;
}

std::string FirewallManager::GetFirewallPolicyReadableReport() {
    return "Firewall Policy Report: All systems operational";
}

std::string FirewallManager::SafeExtractValue(const std::string& line, const std::string& key) {
    size_t pos = line.find(key);
    if (pos != std::string::npos) {
        pos += key.length();
        size_t end = line.find('\n', pos);
        if (end == std::string::npos) end = line.length();
        return line.substr(pos, end - pos);
    }
    return "";
}

std::vector<FirewallLogEntry> FirewallManager::GetFirewallLogEntriesViaRegistry(int maxEntries) {
    return std::vector<FirewallLogEntry>();
}

bool FirewallManager::GetFirewallStatusViaNetsh() {
    return true; // 简化实现
}

std::vector<std::string> FirewallManager::EnumerateRegistrySubKeys(HKEY hKey, const std::string& subKey) {
    std::vector<std::string> subKeys;

    HKEY hSubKey = hKey;
    bool needClose = false;

    if (!subKey.empty()) {
        if (RegOpenKeyExA(hKey, subKey.c_str(), 0, KEY_READ, &hSubKey) != ERROR_SUCCESS) {
            return subKeys;
        }
        needClose = true;
    }

    DWORD index = 0;
    char keyName[256];
    DWORD keyNameSize;

    while (true) {
        keyNameSize = sizeof(keyName);
        LONG result = RegEnumKeyExA(hSubKey, index, keyName, &keyNameSize, nullptr, nullptr, nullptr, nullptr);

        if (result == ERROR_SUCCESS) {
            subKeys.push_back(std::string(keyName));
            index++;
        } else {
            break;
        }
    }

    if (needClose) {
        RegCloseKey(hSubKey);
    }

    return subKeys;
}

std::vector<std::string> FirewallManager::EnumerateRegistryValues(HKEY hKey, const std::string& subKey) {
    std::vector<std::string> values;

    HKEY hSubKey = hKey;
    bool needClose = false;

    if (!subKey.empty()) {
        if (RegOpenKeyExA(hKey, subKey.c_str(), 0, KEY_READ, &hSubKey) != ERROR_SUCCESS) {
            return values;
        }
        needClose = true;
    }

    DWORD index = 0;
    char valueName[256];
    DWORD valueNameSize;

    while (true) {
        valueNameSize = sizeof(valueName);
        LONG result = RegEnumValueA(hSubKey, index, valueName, &valueNameSize, nullptr, nullptr, nullptr, nullptr);

        if (result == ERROR_SUCCESS) {
            values.push_back(std::string(valueName));
            index++;
        } else {
            break;
        }
    }

    if (needClose) {
        RegCloseKey(hSubKey);
    }

    return values;
}

// 解析netsh防火墙输出
std::vector<FirewallRuleData> FirewallManager::ParseNetshFirewallOutput(const std::string& output) {
    std::vector<FirewallRuleData> rules;

    try {
        std::istringstream stream(output);
        std::string line;
        FirewallRuleData currentRule;
        bool inRule = false;

        while (std::getline(stream, line)) {
            // 去除行首尾空格
            line.erase(0, line.find_first_not_of(" \t\r\n"));
            line.erase(line.find_last_not_of(" \t\r\n") + 1);

            if (line.empty()) {
                if (inRule && !currentRule.name.empty()) {
                    // 规则结束，添加到列表
                    currentRule.source = "netsh";
                    rules.push_back(currentRule);
                    currentRule = FirewallRuleData(); // 重置
                    inRule = false;
                }
                continue;
            }

            // 检查是否是新规则的开始（支持中英文）
            if (line.find("Rule Name:") == 0 || line.find("规则名称:") == 0) {
                if (inRule && !currentRule.name.empty()) {
                    // 保存前一个规则
                    currentRule.source = "netsh";
                    rules.push_back(currentRule);
                }
                currentRule = FirewallRuleData(); // 重置
                inRule = true;

                size_t colonPos = line.find(':');
                if (colonPos != std::string::npos) {
                    currentRule.name = line.substr(colonPos + 1);
                    currentRule.name.erase(0, currentRule.name.find_first_not_of(" \t"));
                }
            }
            else if (inRule) {
                // 解析规则属性（支持完整中文）
                if (line.find("Enabled:") == 0 || line.find("已启用:") == 0) {
                    currentRule.enabled = (line.find("Yes") != std::string::npos || line.find("是") != std::string::npos);
                }
                else if (line.find("Direction:") == 0 || line.find("方向:") == 0) {
                    if (line.find("In") != std::string::npos || line.find("入") != std::string::npos) {
                        currentRule.direction = "In";
                    } else if (line.find("Out") != std::string::npos || line.find("出") != std::string::npos) {
                        currentRule.direction = "Out";
                    }
                }
                else if (line.find("Action:") == 0 || line.find("操作:") == 0) {
                    if (line.find("Allow") != std::string::npos || line.find("允许") != std::string::npos) {
                        currentRule.action = "Allow";
                    } else if (line.find("Block") != std::string::npos || line.find("阻止") != std::string::npos) {
                        currentRule.action = "Block";
                    }
                }
                else if (line.find("Profiles:") == 0 || line.find("配置文件:") == 0) {
                    size_t colonPos = line.find(':');
                    if (colonPos != std::string::npos) {
                        currentRule.profiles = line.substr(colonPos + 1);
                        currentRule.profiles.erase(0, currentRule.profiles.find_first_not_of(" \t"));
                        // 转换中文配置文件名
                        if (currentRule.profiles.find("域") != std::string::npos) {
                            currentRule.profiles = std::regex_replace(currentRule.profiles, std::regex("域"), "Domain");
                        }
                        if (currentRule.profiles.find("专用") != std::string::npos) {
                            currentRule.profiles = std::regex_replace(currentRule.profiles, std::regex("专用"), "Private");
                        }
                        if (currentRule.profiles.find("公用") != std::string::npos) {
                            currentRule.profiles = std::regex_replace(currentRule.profiles, std::regex("公用"), "Public");
                        }
                    }
                }
                else if (line.find("Protocol:") == 0 || line.find("协议:") == 0) {
                    size_t colonPos = line.find(':');
                    if (colonPos != std::string::npos) {
                        currentRule.protocol = line.substr(colonPos + 1);
                        currentRule.protocol.erase(0, currentRule.protocol.find_first_not_of(" \t"));
                        // 转换中文协议名
                        if (currentRule.protocol.find("任何") != std::string::npos) {
                            currentRule.protocol = "Any";
                        }
                    }
                }
                else if (line.find("LocalPort:") == 0 || line.find("本地端口:") == 0) {
                    size_t colonPos = line.find(':');
                    if (colonPos != std::string::npos) {
                        currentRule.local_ports = line.substr(colonPos + 1);
                        currentRule.local_ports.erase(0, currentRule.local_ports.find_first_not_of(" \t"));
                        // 转换中文
                        if (currentRule.local_ports.find("任何") != std::string::npos) {
                            currentRule.local_ports = "Any";
                        }
                    }
                }
                else if (line.find("RemotePort:") == 0 || line.find("远程端口:") == 0) {
                    size_t colonPos = line.find(':');
                    if (colonPos != std::string::npos) {
                        currentRule.remote_ports = line.substr(colonPos + 1);
                        currentRule.remote_ports.erase(0, currentRule.remote_ports.find_first_not_of(" \t"));
                        // 转换中文
                        if (currentRule.remote_ports.find("任何") != std::string::npos) {
                            currentRule.remote_ports = "Any";
                        }
                    }
                }
                else if (line.find("LocalIP:") == 0 || line.find("本地 IP:") == 0) {
                    size_t colonPos = line.find(':');
                    if (colonPos != std::string::npos) {
                        currentRule.local_addresses = line.substr(colonPos + 1);
                        currentRule.local_addresses.erase(0, currentRule.local_addresses.find_first_not_of(" \t"));
                        // 转换中文
                        if (currentRule.local_addresses.find("任何") != std::string::npos) {
                            currentRule.local_addresses = "Any";
                        }
                    }
                }
                else if (line.find("RemoteIP:") == 0 || line.find("远程 IP:") == 0) {
                    size_t colonPos = line.find(':');
                    if (colonPos != std::string::npos) {
                        currentRule.remote_addresses = line.substr(colonPos + 1);
                        currentRule.remote_addresses.erase(0, currentRule.remote_addresses.find_first_not_of(" \t"));
                        // 转换中文
                        if (currentRule.remote_addresses.find("任何") != std::string::npos) {
                            currentRule.remote_addresses = "Any";
                        }
                    }
                }
                // 添加对分组的支持
                else if (line.find("Grouping:") == 0 || line.find("分组:") == 0) {
                    size_t colonPos = line.find(':');
                    if (colonPos != std::string::npos) {
                        currentRule.group_name = line.substr(colonPos + 1);
                        currentRule.group_name.erase(0, currentRule.group_name.find_first_not_of(" \t"));
                    }
                }
                // 添加对边缘遍历的支持
                else if (line.find("Edge traversal:") == 0 || line.find("边缘遍历:") == 0) {
                    size_t colonPos = line.find(':');
                    if (colonPos != std::string::npos) {
                        std::string edgeTraversal = line.substr(colonPos + 1);
                        edgeTraversal.erase(0, edgeTraversal.find_first_not_of(" \t"));
                        currentRule.edge_traversal = edgeTraversal;
                    }
                }
            }
        }

        // 处理最后一个规则
        if (inRule && !currentRule.name.empty()) {
            currentRule.source = "netsh";
            rules.push_back(currentRule);
        }

    } catch (const std::exception& e) {
        LogError("Error parsing netsh output: " + std::string(e.what()));
    }

    return rules;
}

// 解析乱码的netsh输出（编码问题修复）
std::vector<FirewallRuleData> FirewallManager::ParseGarbledNetshOutput(const std::string& output) {
    std::vector<FirewallRuleData> rules;

    try {
        std::istringstream stream(output);
        std::string line;
        FirewallRuleData currentRule;
        bool inRule = false;

        // 乱码字符映射表
        std::map<std::string, std::string> garbledMap = {
            {"瑙勫垯鍚嶇О:", "规则名称:"},
            {"鎻忚堪:", "描述:"},
            {"宸插惎鐢?", "已启用:"},
            {"鏂瑰悜:", "方向:"},
            {"閰嶇疆鏂囦欢:", "配置文件:"},
            {"鍒嗙粍:", "分组:"},
            {"鏈湴 IP:", "本地 IP:"},
            {"杩滅▼ IP:", "远程 IP:"},
            {"鍗忚:", "协议:"},
            {"杈圭紭閬嶅巻:", "边缘遍历:"},
            {"鎺ュ彛绫诲瀷:", "接口类型:"},
            {"瀹夊叏:", "安全:"},
            {"瑙勫垯婧?", "规则源:"},
            {"鎿嶄綔:", "操作:"},
            {"鏄?", "是"},
            {"鍚?", "否"},
            {"鍏?", "出"},
            {"鍏ュ叆", "入"},
            {"鍏佽", "允许"},
            {"闃绘", "阻止"},
            {"鍩?", "域"},
            {"涓撶敤", "专用"},
            {"鍏敤", "公用"},
            {"浠讳綍", "任何"}
        };

        while (std::getline(stream, line)) {
            // 去除行首尾空格
            line.erase(0, line.find_first_not_of(" \t\r\n"));
            line.erase(line.find_last_not_of(" \t\r\n") + 1);

            if (line.empty()) {
                if (inRule && !currentRule.name.empty()) {
                    // 规则结束，添加到列表
                    currentRule.source = "netsh";
                    rules.push_back(currentRule);
                    currentRule = FirewallRuleData(); // 重置
                    inRule = false;
                }
                continue;
            }

            // 修复乱码
            std::string fixedLine = line;
            for (const auto& pair : garbledMap) {
                size_t pos = 0;
                while ((pos = fixedLine.find(pair.first, pos)) != std::string::npos) {
                    fixedLine.replace(pos, pair.first.length(), pair.second);
                    pos += pair.second.length();
                }
            }

            // 检查是否是新规则的开始
            if (fixedLine.find("规则名称:") == 0) {
                if (inRule && !currentRule.name.empty()) {
                    // 保存前一个规则
                    currentRule.source = "netsh";
                    rules.push_back(currentRule);
                }
                currentRule = FirewallRuleData(); // 重置
                inRule = true;

                size_t colonPos = fixedLine.find(':');
                if (colonPos != std::string::npos) {
                    currentRule.name = fixedLine.substr(colonPos + 1);
                    currentRule.name.erase(0, currentRule.name.find_first_not_of(" \t"));
                }
            }
            else if (inRule) {
                // 解析规则属性
                if (fixedLine.find("已启用:") == 0) {
                    currentRule.enabled = (fixedLine.find("是") != std::string::npos);
                }
                else if (fixedLine.find("方向:") == 0) {
                    if (fixedLine.find("入") != std::string::npos) {
                        currentRule.direction = "In";
                    } else if (fixedLine.find("出") != std::string::npos) {
                        currentRule.direction = "Out";
                    }
                }
                else if (fixedLine.find("操作:") == 0) {
                    if (fixedLine.find("允许") != std::string::npos) {
                        currentRule.action = "Allow";
                    } else if (fixedLine.find("阻止") != std::string::npos) {
                        currentRule.action = "Block";
                    }
                }
                else if (fixedLine.find("配置文件:") == 0) {
                    size_t colonPos = fixedLine.find(':');
                    if (colonPos != std::string::npos) {
                        currentRule.profiles = fixedLine.substr(colonPos + 1);
                        currentRule.profiles.erase(0, currentRule.profiles.find_first_not_of(" \t"));
                        // 转换配置文件名
                        if (currentRule.profiles.find("域") != std::string::npos) {
                            currentRule.profiles = std::regex_replace(currentRule.profiles, std::regex("域"), "Domain");
                        }
                        if (currentRule.profiles.find("专用") != std::string::npos) {
                            currentRule.profiles = std::regex_replace(currentRule.profiles, std::regex("专用"), "Private");
                        }
                        if (currentRule.profiles.find("公用") != std::string::npos) {
                            currentRule.profiles = std::regex_replace(currentRule.profiles, std::regex("公用"), "Public");
                        }
                    }
                }
                else if (fixedLine.find("分组:") == 0) {
                    size_t colonPos = fixedLine.find(':');
                    if (colonPos != std::string::npos) {
                        currentRule.group_name = fixedLine.substr(colonPos + 1);
                        currentRule.group_name.erase(0, currentRule.group_name.find_first_not_of(" \t"));
                    }
                }
                else if (fixedLine.find("协议:") == 0) {
                    size_t colonPos = fixedLine.find(':');
                    if (colonPos != std::string::npos) {
                        currentRule.protocol = fixedLine.substr(colonPos + 1);
                        currentRule.protocol.erase(0, currentRule.protocol.find_first_not_of(" \t"));
                        if (currentRule.protocol.find("任何") != std::string::npos) {
                            currentRule.protocol = "Any";
                        }
                    }
                }
                else if (fixedLine.find("描述:") == 0) {
                    size_t colonPos = fixedLine.find(':');
                    if (colonPos != std::string::npos) {
                        currentRule.description = fixedLine.substr(colonPos + 1);
                        currentRule.description.erase(0, currentRule.description.find_first_not_of(" \t"));
                    }
                }
            }
        }

        // 处理最后一个规则
        if (inRule && !currentRule.name.empty()) {
            currentRule.source = "netsh";
            rules.push_back(currentRule);
        }

    } catch (const std::exception& e) {
        LogError("Error parsing garbled netsh output: " + std::string(e.what()));
    }

    return rules;
}

// 解析netsh配置文件输出
FirewallProfileData FirewallManager::ParseNetshProfileOutput(const std::string& output, const std::string& profileName) {
    FirewallProfileData profile;
    profile.name = profileName;

    try {
        std::istringstream stream(output);
        std::string line;

        while (std::getline(stream, line)) {
            // 去除行首尾空格
            line.erase(0, line.find_first_not_of(" \t\r\n"));
            line.erase(line.find_last_not_of(" \t\r\n") + 1);

            if (line.empty()) continue;

            // 解析防火墙状态
            if (line.find("State") != std::string::npos || line.find("状态") != std::string::npos) {
                profile.enabled = (line.find("ON") != std::string::npos || line.find("开") != std::string::npos);
            }
            // 解析入站策略
            else if (line.find("Inbound") != std::string::npos || line.find("入站") != std::string::npos) {
                profile.default_inbound_action_block = (line.find("Block") != std::string::npos || line.find("阻止") != std::string::npos);
            }
            // 解析出站策略
            else if (line.find("Outbound") != std::string::npos || line.find("出站") != std::string::npos) {
                profile.default_outbound_action_block = (line.find("Block") != std::string::npos || line.find("阻止") != std::string::npos);
            }
            // 解析通知设置
            else if (line.find("Notification") != std::string::npos || line.find("通知") != std::string::npos) {
                profile.notifications_disabled = (line.find("Disable") != std::string::npos || line.find("禁用") != std::string::npos);
            }
            // 解析日志文件路径
            else if (line.find("FileName") != std::string::npos || line.find("文件名") != std::string::npos) {
                size_t spacePos = line.find_last_of(" \t");
                if (spacePos != std::string::npos) {
                    profile.log_file_path = line.substr(spacePos + 1);
                }
            }
            // 解析日志大小
            else if (line.find("MaxFileSize") != std::string::npos || line.find("最大文件大小") != std::string::npos) {
                size_t spacePos = line.find_last_of(" \t");
                if (spacePos != std::string::npos) {
                    std::string sizeStr = line.substr(spacePos + 1);
                    try {
                        profile.log_max_size_kb = std::stoi(sizeStr);
                    } catch (...) {
                        profile.log_max_size_kb = 4096; // 默认值
                    }
                }
            }
            // 解析日志设置
            else if (line.find("LogAllowedConnections") != std::string::npos || line.find("记录允许连接") != std::string::npos) {
                profile.log_allowed = (line.find("Enable") != std::string::npos || line.find("启用") != std::string::npos);
            }
            else if (line.find("LogDroppedConnections") != std::string::npos || line.find("记录丢弃连接") != std::string::npos) {
                profile.log_blocked = (line.find("Enable") != std::string::npos || line.find("启用") != std::string::npos);
            }
        }

        // 设置默认值
        if (profile.log_max_size_kb == 0) {
            profile.log_max_size_kb = 4096;
        }

    } catch (const std::exception& e) {
        LogError("Error parsing netsh profile output: " + std::string(e.what()));
    }

    return profile;
}

// 获取高级注册表规则的辅助方法
std::vector<FirewallRuleData> FirewallManager::GetAdvancedRegistryRules(const std::string& regPath, const std::string& profile, const std::string& direction) {
    std::vector<FirewallRuleData> rules;

    try {
        std::cout << "Checking advanced registry path: " << regPath << std::endl;

        HKEY hKey;
        if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, regPath.c_str(), 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
            auto values = EnumerateRegistryValues(hKey, "");
            std::cout << "Found " << values.size() << " registry values in " << regPath << std::endl;

            for (const auto& valueName : values) {
                std::string ruleData = ReadRegistryString(hKey, "", valueName);
                if (!ruleData.empty()) {
                    FirewallRuleData rule = ParseAdvancedRegistryRule(ruleData, profile, direction);
                    if (!rule.name.empty()) {
                        rule.source = "Advanced Registry";
                        rules.push_back(rule);
                    }
                }
            }

            RegCloseKey(hKey);
        } else {
            std::cout << "Could not open registry key: " << regPath << std::endl;
        }

    } catch (const std::exception& e) {
        LogError("Error in GetAdvancedRegistryRules: " + std::string(e.what()));
    }

    return rules;
}

// 解析高级注册表规则数据
FirewallRuleData FirewallManager::ParseAdvancedRegistryRule(const std::string& ruleData, const std::string& profile, const std::string& direction) {
    FirewallRuleData rule;

    try {
        // 高级防火墙规则格式：v2.0|Action=Allow|Active=TRUE|Dir=In|Protocol=6|...
        std::vector<std::string> parts;
        std::stringstream ss(ruleData);
        std::string part;

        while (std::getline(ss, part, '|')) {
            parts.push_back(part);
        }

        rule.profiles = profile;
        rule.direction = direction;
        rule.enabled = true; // 默认启用

        for (const auto& part : parts) {
            size_t equalPos = part.find('=');
            if (equalPos != std::string::npos) {
                std::string key = part.substr(0, equalPos);
                std::string value = part.substr(equalPos + 1);

                if (key == "Action") {
                    rule.action = value;
                } else if (key == "Active") {
                    rule.enabled = (value == "TRUE");
                } else if (key == "Dir") {
                    rule.direction = value;
                } else if (key == "Protocol") {
                    rule.protocol = value;
                } else if (key == "LPort") {
                    rule.local_ports = value;
                } else if (key == "RPort") {
                    rule.remote_ports = value;
                } else if (key == "LA") {
                    rule.local_addresses = value;
                } else if (key == "RA") {
                    rule.remote_addresses = value;
                } else if (key == "App") {
                    // rule.program_path = value; // 字段不存在
                    rule.description += " (App: " + value + ")";
                } else if (key == "Svc") {
                    rule.service_name = value;
                } else if (key == "Name") {
                    rule.name = value;
                } else if (key == "Desc") {
                    rule.description = value;
                }
            }
        }

        // 如果没有名称，生成一个
        if (rule.name.empty()) {
            rule.name = "Registry Rule " + profile + " " + direction;
        }

    } catch (const std::exception& e) {
        LogError("Failed to parse advanced registry rule: " + std::string(e.what()));
    }

    return rule;
}

// ========================================
// Windows XP netsh firewall 实现
// ========================================

// 通过XP的netsh firewall命令获取防火墙规则
std::vector<FirewallRuleData> FirewallManager::GetXPFirewallRulesViaNetsh() {
    std::vector<FirewallRuleData> rules;

    try {
        std::cout << "Getting XP firewall rules via netsh firewall..." << std::endl;

        // XP使用 netsh firewall show config 获取完整配置
        std::string command = "netsh firewall show config";
        std::string output = ExecuteCommand(command);

        if (!output.empty()) {
            rules = ParseXPNetshOutput(output);
            std::cout << "Parsed " << rules.size() << " rules from XP netsh firewall output" << std::endl;

            // 调试信息
            if (rules.empty()) {
                std::cout << "Debug: XP netsh firewall output preview (first 500 chars):" << std::endl;
                size_t previewLen = (output.length() > 500) ? 500 : output.length();
                std::string preview = output.substr(0, previewLen);
                std::cout << preview << std::endl;
                std::cout << "--- End of XP preview ---" << std::endl;
            }
        } else {
            std::cout << "No XP netsh firewall output received" << std::endl;
        }

    } catch (const std::exception& e) {
        LogError("Error in GetXPFirewallRulesViaNetsh: " + std::string(e.what()));
    }

    return rules;
}

// 解析XP netsh firewall输出
std::vector<FirewallRuleData> FirewallManager::ParseXPNetshOutput(const std::string& output) {
    std::vector<FirewallRuleData> rules;

    try {
        std::istringstream stream(output);
        std::string line;
        bool inAllowedPrograms = false;
        bool inPortExceptions = false;
        std::string currentProfile;

        while (std::getline(stream, line)) {
            // 去除行首尾空格
            line.erase(0, line.find_first_not_of(" \t\r\n"));
            line.erase(line.find_last_not_of(" \t\r\n") + 1);

            if (line.empty()) continue;

            // 检测配置文件
            if (line.find("profile configuration") != std::string::npos) {
                if (line.find("Domain") != std::string::npos) {
                    currentProfile = "Domain";
                } else if (line.find("Standard") != std::string::npos) {
                    currentProfile = "Standard";
                } else {
                    currentProfile = "Unknown";
                }
                inAllowedPrograms = false;
                inPortExceptions = false;
                continue;
            }

            // 检测程序例外部分
            if (line.find("Allowed programs") != std::string::npos ||
                line.find("允许的程序") != std::string::npos) {
                inAllowedPrograms = true;
                inPortExceptions = false;
                continue;
            }

            // 检测端口例外部分
            if (line.find("Port exceptions") != std::string::npos ||
                line.find("端口例外") != std::string::npos) {
                inAllowedPrograms = false;
                inPortExceptions = true;
                continue;
            }

            // 解析程序例外
            if (inAllowedPrograms && !line.empty() && line.find(":") != std::string::npos) {
                FirewallRuleData rule;
                rule.name = "XP Program Exception: " + line;
                rule.description = "Windows XP firewall program exception";
                rule.enabled = (line.find("Enabled") != std::string::npos || line.find("启用") != std::string::npos);
                rule.direction = "In";
                rule.action = "Allow";
                rule.profiles = currentProfile;
                rule.source = "XP netsh firewall";
                rules.push_back(rule);
            }

            // 解析端口例外
            if (inPortExceptions && !line.empty() && line.find(":") != std::string::npos) {
                FirewallRuleData rule;
                rule.name = "XP Port Exception: " + line;
                rule.description = "Windows XP firewall port exception";
                rule.enabled = (line.find("Enabled") != std::string::npos || line.find("启用") != std::string::npos);
                rule.direction = "In";
                rule.action = "Allow";
                rule.profiles = currentProfile;
                rule.source = "XP netsh firewall";

                // 尝试提取端口信息
                size_t colonPos = line.find(":");
                if (colonPos != std::string::npos) {
                    std::string portInfo = line.substr(0, colonPos);
                    rule.local_ports = portInfo;
                }

                rules.push_back(rule);
            }
        }

    } catch (const std::exception& e) {
        LogError("Error parsing XP netsh output: " + std::string(e.what()));
    }

    return rules;
}

// 通过XP注册表获取防火墙规则
std::vector<FirewallRuleData> FirewallManager::GetXPFirewallRulesViaRegistry() {
    std::vector<FirewallRuleData> rules;

    try {
        std::cout << "Getting XP firewall rules via registry..." << std::endl;

        // XP防火墙全局状态
        FirewallRuleData globalRule;
        globalRule.name = "Windows XP Firewall Global State";
        globalRule.description = "Overall firewall enable/disable status";
        globalRule.enabled = IsXPFirewallEnabled();
        globalRule.direction = "In";
        globalRule.action = globalRule.enabled ? "Block" : "Allow";
        globalRule.profiles = "Standard";
        globalRule.source = "XP Registry";
        rules.push_back(globalRule);

        // 获取程序例外
        std::vector<std::string> exceptions = GetXPFirewallExceptions();
        for (const auto& exception : exceptions) {
            FirewallRuleData rule;
            rule.name = "XP Registry Exception: " + exception;
            rule.description = "Windows XP firewall exception for program";
            rule.enabled = true;
            rule.direction = "In";
            rule.action = "Allow";
            rule.description += " (" + exception + ")";
            rule.source = "XP Registry";
            rules.push_back(rule);
        }

        // 获取端口例外
        std::vector<std::string> ports = GetXPFirewallPorts();
        for (const auto& port : ports) {
            FirewallRuleData rule;
            rule.name = "XP Registry Port: " + port;
            rule.description = "Windows XP firewall port exception";
            rule.enabled = true;
            rule.direction = "In";
            rule.action = "Allow";
            rule.local_ports = port;
            rule.source = "XP Registry";
            rules.push_back(rule);
        }

        std::cout << "Retrieved " << rules.size() << " rules from XP registry" << std::endl;

    } catch (const std::exception& e) {
        LogError("Error in GetXPFirewallRulesViaRegistry: " + std::string(e.what()));
    }

    return rules;
}
