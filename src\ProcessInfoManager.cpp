#include "pch.h"
#include "ProcessInfoManager.h"
#include "Utils.h"  // 添加Utils.h包含，用于BMP格式转换和Base64编码
#include <iostream>
#include <sstream>
#include <iomanip>
#include <memory>
#include <algorithm>
#include <codecvt>
#include <locale>
#include <fstream>
#include <psapi.h>
#include <shellapi.h>
#include <commctrl.h>
#include <stdlib.h> // ����_wsplitpath_s

// ����Windows XP�����Ժ�
#ifndef _WIN32_WINNT
#define _WIN32_WINNT 0x0501  // Windows XP
#endif

// ���ӱ�Ҫ�Ŀ�
#pragma comment(lib, "psapi.lib")
#pragma comment(lib, "shell32.lib")
#pragma comment(lib, "comctl32.lib")
#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib, "iphlpapi.lib")

// ��ȡ�����������еĽ�����Ϣ
std::vector<ProcessInfo> ProcessInfoManager::GetAllProcesses() {
    std::vector<ProcessInfo> processes;

    // �������̿���
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        std::cerr << "Create process snapshot failed, error: " << GetLastError() << std::endl;
        return processes;
    }

    // ��ʼ��������Ŀ�ṹ
    PROCESSENTRY32 pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32);

    // ��ȡ��һ������
    if (!Process32First(hSnapshot, &pe32)) {
        std::cerr << "Get first process failed, error: " << GetLastError() << std::endl;
        CloseHandle(hSnapshot);
        return processes;
    }

    // ��ȡ��������������Ϣ
    std::vector<NetworkConnection> allConnections = GetAllNetworkConnections();

    // �������н���
    do {
        ProcessInfo info;
        info.pid = pe32.th32ProcessID;
        info.processName = pe32.szExeFile;
        info.processPath = GetProcessPath(pe32.th32ProcessID);
        info.create_time = GetProcessCreateTime(pe32.th32ProcessID);

        // ���Ӹý��̵�����������Ϣ
        for (const auto& conn : allConnections) {
            if (conn.owningPid == info.pid) {
                info.connections.push_back(conn);
            }
        }

        processes.push_back(info);
    } while (Process32Next(hSnapshot, &pe32));

    CloseHandle(hSnapshot);
    return processes;
}

// ���ݽ������Ʋ��ҽ���
std::vector<ProcessInfo> ProcessInfoManager::FindProcessesByName(const std::wstring& processName) {
    std::vector<ProcessInfo> matchedProcesses;
    std::vector<ProcessInfo> allProcesses = GetAllProcesses();

    for (const auto& process : allProcesses) {
        // �����ִ�Сд�Ƚ�
        std::wstring lowerProcessName = processName;
        std::wstring lowerCurrentName = process.processName;

        // ת��ΪСд���бȽ�
        std::transform(lowerProcessName.begin(), lowerProcessName.end(), lowerProcessName.begin(), ::tolower);
        std::transform(lowerCurrentName.begin(), lowerCurrentName.end(), lowerCurrentName.begin(), ::tolower);

        if (lowerCurrentName.find(lowerProcessName) != std::wstring::npos) {
            matchedProcesses.push_back(process);
        }
    }

    return matchedProcesses;
}

// ����PID���ҽ���
ProcessInfo ProcessInfoManager::FindProcessByPID(DWORD pid) {
    ProcessInfo info;
    info.pid = 0; // Ĭ��Ϊ0��ʾδ�ҵ�

    std::vector<ProcessInfo> allProcesses = GetAllProcesses();

    for (const auto& process : allProcesses) {
        if (process.pid == pid) {
            return process;
        }
    }

    return info;
}

// �����ַ���ת��ΪUTF-8�ַ�����Windows XP���ݷ�����
std::string WideStringToUTF8(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();

    // ������Ҫ�Ļ�������С
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), (int)wstr.size(), NULL, 0, NULL, NULL);
    if (size_needed <= 0) return std::string();

    // ���仺������ת��
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), (int)wstr.size(), &strTo[0], size_needed, NULL, NULL);

    return strTo;
}

// ��������Ϣת��ΪJSON��ʽ
json ProcessInfoManager::ProcessesToJson(const std::vector<ProcessInfo>& processes) {
    json result = json::array();

    for (const auto& process : processes) {
        json processJson;

        // ʹ��Windows API����ת�����ַ�����UTF-8
        std::string processName = WideStringToUTF8(process.processName);
        std::string processPath = WideStringToUTF8(process.processPath);

        processJson["pid"] = process.pid;
        processJson["name"] = processName;
        processJson["path"] = processPath;
        processJson["create_time"] = process.create_time;

        // ��������������Ϣ
        if (!process.connections.empty()) {
            json connectionsJson = json::array();

            for (const auto& conn : process.connections) {
                json connectionJson;
                connectionJson["localAddress"] = conn.localAddress;
                connectionJson["localPort"] = conn.localPort;
                connectionJson["remoteAddress"] = conn.remoteAddress;
                connectionJson["remotePort"] = conn.remotePort;
                connectionJson["state"] = conn.state;

                connectionsJson.push_back(connectionJson);
            }

            processJson["connections"] = connectionsJson;
        }

        result.push_back(processJson);
    }

    return result;
}

// ��ȡ���н��̵���ϸ��Ϣ������ͼ����������ӣ���ƽ����ʽ
json ProcessInfoManager::GetAllProcessesDetailed() {
    std::vector<ProcessInfo> processes = GetAllProcesses();
    json result = json::array();

    std::cout << "Processing " << processes.size() << " processes for flattened format..." << std::endl;

    // 测试Utils函数是否工作
    std::wstring testIcon = Utils::GetFileIconAsBase64(L"C:\\Windows\\System32\\notepad.exe", 32);
    if (!testIcon.empty()) {
        std::cout << "Test icon retrieval successful, size: " << testIcon.length() << " characters" << std::endl;
    } else {
        std::cout << "Test icon retrieval failed" << std::endl;
    }

    // 为每个进程创建扁平化的JSON条目
    for (const auto& process : processes) {
        // 获取进程图标
        std::string iconBase64 = "";
        if (!process.processPath.empty()) {
            try {
                std::wstring iconBase64Wide = Utils::GetFileIconAsBase64(process.processPath, 32);
                if (!iconBase64Wide.empty()) {
                    iconBase64 = Utils::WStringToUTF8(iconBase64Wide);
                    std::cout << "Successfully added icon for process: " << WideStringToUTF8(process.processName)
                              << ", icon size: " << iconBase64.length() << " bytes" << std::endl;
                }
            }
            catch (const std::exception& e) {
                std::cerr << "Get icon failed for process " << WideStringToUTF8(process.processName)
                          << ": " << e.what() << std::endl;
            }
        }

        // 如果进程有网络连接，为每个连接创建一个条目
        if (!process.connections.empty()) {
            for (const auto& conn : process.connections) {
                json entry;
                entry["pid"] = process.pid;
                entry["name"] = WideStringToUTF8(process.processName);
                entry["path"] = WideStringToUTF8(process.processPath);
                entry["create_time"] = process.create_time;
                entry["icon"] = iconBase64;
                entry["localAddress"] = conn.localAddress;
                entry["localPort"] = conn.localPort;
                entry["remoteAddress"] = conn.remoteAddress;
                entry["remotePort"] = conn.remotePort;
                entry["state"] = conn.state;

                result.push_back(entry);
            }
        } else {
            // 如果进程没有网络连接，仍然创建一个条目，但网络字段为空
            json entry;
            entry["pid"] = process.pid;
            entry["name"] = WideStringToUTF8(process.processName);
            entry["path"] = WideStringToUTF8(process.processPath);
            entry["create_time"] = process.create_time;
            entry["icon"] = iconBase64;
            entry["localAddress"] = "";
            entry["localPort"] = 0;
            entry["remoteAddress"] = "";
            entry["remotePort"] = 0;
            entry["state"] = 0;

            result.push_back(entry);
        }
    }

    std::cout << "Finished processing. Returning flattened JSON with " << result.size() << " entries." << std::endl;
    return result;
}

// ��ȡָ�����̵���ϸ��Ϣ������ͼ����������ӣ���ƽ����ʽ
json ProcessInfoManager::GetProcessDetailedByName(const std::wstring& processName) {
    std::vector<ProcessInfo> processes = FindProcessesByName(processName);
    json result = json::array();

    // 为每个匹配的进程创建扁平化的JSON条目
    for (const auto& process : processes) {
        // 获取进程图标
        std::string iconBase64 = "";
        if (!process.processPath.empty()) {
            try {
                std::wstring iconBase64Wide = Utils::GetFileIconAsBase64(process.processPath, 32);
                if (!iconBase64Wide.empty()) {
                    iconBase64 = Utils::WStringToUTF8(iconBase64Wide);
                }
            }
            catch (const std::exception& e) {
                std::cerr << "Get icon failed: " << e.what() << std::endl;
            }
        }

        // 如果进程有网络连接，为每个连接创建一个条目
        if (!process.connections.empty()) {
            for (const auto& conn : process.connections) {
                json entry;
                entry["pid"] = process.pid;
                entry["name"] = WideStringToUTF8(process.processName);
                entry["path"] = WideStringToUTF8(process.processPath);
                entry["create_time"] = process.create_time;
                entry["icon"] = iconBase64;
                entry["localAddress"] = conn.localAddress;
                entry["localPort"] = conn.localPort;
                entry["remoteAddress"] = conn.remoteAddress;
                entry["remotePort"] = conn.remotePort;
                entry["state"] = conn.state;

                result.push_back(entry);
            }
        } else {
            // 如果进程没有网络连接，仍然创建一个条目，但网络字段为空
            json entry;
            entry["pid"] = process.pid;
            entry["name"] = WideStringToUTF8(process.processName);
            entry["path"] = WideStringToUTF8(process.processPath);
            entry["create_time"] = process.create_time;
            entry["icon"] = iconBase64;
            entry["localAddress"] = "";
            entry["localPort"] = 0;
            entry["remoteAddress"] = "";
            entry["remotePort"] = 0;
            entry["state"] = 0;

            result.push_back(entry);
        }
    }

    return result;
}

// ��ȡ�������������ӵĽ�����ƽ����ʽ
json ProcessInfoManager::GetAllNetworkProcesses() {
    std::vector<ProcessInfo> allProcesses = GetAllProcesses();
    json result = json::array();

    // 只处理有网络连接的进程，为每个连接创建扁平化条目
    for (const auto& process : allProcesses) {
        if (!process.connections.empty()) {
            // 获取进程图标
            std::string iconBase64 = "";
            if (!process.processPath.empty()) {
                try {
                    std::wstring iconBase64Wide = Utils::GetFileIconAsBase64(process.processPath, 32);
                    if (!iconBase64Wide.empty()) {
                        iconBase64 = Utils::WStringToUTF8(iconBase64Wide);
                    }
                }
                catch (const std::exception& e) {
                    std::cerr << "Get icon failed: " << e.what() << std::endl;
                }
            }

            // 为每个网络连接创建一个条目
            for (const auto& conn : process.connections) {
                json entry;
                entry["pid"] = process.pid;
                entry["name"] = WideStringToUTF8(process.processName);
                entry["path"] = WideStringToUTF8(process.processPath);
                entry["create_time"] = process.create_time;
                entry["icon"] = iconBase64;
                entry["localAddress"] = conn.localAddress;
                entry["localPort"] = conn.localPort;
                entry["remoteAddress"] = conn.remoteAddress;
                entry["remotePort"] = conn.remotePort;
                entry["state"] = conn.state;

                result.push_back(entry);
            }
        }
    }

    return result;
}

// ��ȡ���н��̲�ת��ΪJSON�ַ���
std::string ProcessInfoManager::GetAllProcessesAsJsonString() {
    std::vector<ProcessInfo> processes = GetAllProcesses();
    json processesJson = ProcessesToJson(processes);
    return processesJson.dump(4); // ʹ��4���ո�������ʽ�����
}

// ��ȡ����·��
std::wstring ProcessInfoManager::GetProcessPath(DWORD pid) {
    std::wstring path;

    // �򿪽���
    HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, pid);
    if (hProcess != NULL) {
        path = GetProcessExecutablePath(hProcess);
        CloseHandle(hProcess);
    }

    return path;
}

// ��ȡ���̿�ִ���ļ�·��
std::wstring ProcessInfoManager::GetProcessExecutablePath(HANDLE hProcess) {
    std::wstring path;
    WCHAR buffer[MAX_PATH];
    DWORD bufferSize = MAX_PATH;

    // ��Windows XP�ϣ�ֱ��ʹ��GetModuleFileNameEx
    // QueryFullProcessImageName��Vista�����ϰ汾���е�API
    if (GetModuleFileNameExW(hProcess, NULL, buffer, bufferSize)) {
        path = buffer;
    }

    return path;
}

// ��IP��ַ�������ֽ���ת��Ϊ�ַ���
std::string ProcessInfoManager::IpAddressToString(DWORD ipAddress) {
    char buffer[16]; // �㹻�洢IPv4��ַ
    struct in_addr addr;
    addr.s_addr = ipAddress;

    // ʹ��inet_ntoa�������ֽ����IP��ַת��Ϊ�ַ���
    // ���������Windows XP�п���
    const char* ipStr = inet_ntoa(addr);
    if (ipStr) {
        return std::string(ipStr);
    }

    return "0.0.0.0"; // Ĭ�Ϸ���
}

// ��ȡ��������������Ϣ
std::vector<NetworkConnection> ProcessInfoManager::GetAllNetworkConnections() {
    std::vector<NetworkConnection> connections;

    // ��ʼ��Winsock
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        std::cerr << "WSAStartup failed, error: " << WSAGetLastError() << std::endl;
        return connections;
    }

    // ��ȡTCP���ӱ�
    PMIB_TCPTABLE_OWNER_PID pTcpTable = NULL;
    DWORD dwSize = 0;
    DWORD dwRetVal = 0;

    // ���Ȼ�ȡ����Ļ�������С
    dwRetVal = GetExtendedTcpTable(NULL, &dwSize, TRUE, AF_INET, TCP_TABLE_OWNER_PID_ALL, 0);
    if (dwRetVal == ERROR_INSUFFICIENT_BUFFER) {
        pTcpTable = (PMIB_TCPTABLE_OWNER_PID)malloc(dwSize);
        if (pTcpTable == NULL) {
            std::cerr << "Memory allocation failed" << std::endl;
            WSACleanup();
            return connections;
        }
    }
    else {
        std::cerr << "GetExtendedTcpTable failed, error: " << dwRetVal << std::endl;
        WSACleanup();
        return connections;
    }

    // ��ȡTCP���ӱ�
    dwRetVal = GetExtendedTcpTable(pTcpTable, &dwSize, TRUE, AF_INET, TCP_TABLE_OWNER_PID_ALL, 0);
    if (dwRetVal == NO_ERROR) {
        // ����TCP���ӱ�
        for (DWORD i = 0; i < pTcpTable->dwNumEntries; i++) {
            NetworkConnection conn;

            // ��ȡ���ص�ַ�Ͷ˿�
            conn.localAddress = IpAddressToString(pTcpTable->table[i].dwLocalAddr);
            conn.localPort = ntohs((u_short)pTcpTable->table[i].dwLocalPort);

            // ��ȡԶ�̵�ַ�Ͷ˿�
            conn.remoteAddress = IpAddressToString(pTcpTable->table[i].dwRemoteAddr);
            conn.remotePort = ntohs((u_short)pTcpTable->table[i].dwRemotePort);

            // ��ȡ����״̬����������ID
            conn.state = pTcpTable->table[i].dwState;
            conn.owningPid = pTcpTable->table[i].dwOwningPid;

            connections.push_back(conn);
        }
    }
    else {
        std::cerr << "GetExtendedTcpTable failed, error: " << dwRetVal << std::endl;
    }

    // �ͷ��ڴ�
    if (pTcpTable != NULL) {
        free(pTcpTable);
        pTcpTable = NULL;
    }

    // ��ȡUDP���ӱ�
    PMIB_UDPTABLE_OWNER_PID pUdpTable = NULL;
    dwSize = 0;

    // ���Ȼ�ȡ����Ļ�������С
    dwRetVal = GetExtendedUdpTable(NULL, &dwSize, TRUE, AF_INET, UDP_TABLE_OWNER_PID, 0);
    if (dwRetVal == ERROR_INSUFFICIENT_BUFFER) {
        pUdpTable = (PMIB_UDPTABLE_OWNER_PID)malloc(dwSize);
        if (pUdpTable == NULL) {
            std::cerr << "Memory allocation failed" << std::endl;
            WSACleanup();
            return connections;
        }
    }
    else {
        std::cerr << "GetExtendedUdpTable failed, error: " << dwRetVal << std::endl;
        WSACleanup();
        return connections;
    }

    // ��ȡUDP���ӱ�
    dwRetVal = GetExtendedUdpTable(pUdpTable, &dwSize, TRUE, AF_INET, UDP_TABLE_OWNER_PID, 0);
    if (dwRetVal == NO_ERROR) {
        // ����UDP���ӱ�
        for (DWORD i = 0; i < pUdpTable->dwNumEntries; i++) {
            NetworkConnection conn;

            // ��ȡ���ص�ַ�Ͷ˿�
            conn.localAddress = IpAddressToString(pUdpTable->table[i].dwLocalAddr);
            conn.localPort = ntohs((u_short)pUdpTable->table[i].dwLocalPort);

            // UDPû��Զ��������Ϣ
            conn.remoteAddress = "0.0.0.0";
            conn.remotePort = 0;

            // UDPû������״̬������Ϊ0
            conn.state = 0;
            conn.owningPid = pUdpTable->table[i].dwOwningPid;

            connections.push_back(conn);
        }
    }
    else {
        std::cerr << "GetExtendedUdpTable failed, error: " << dwRetVal << std::endl;
    }

    // �ͷ��ڴ�
    if (pUdpTable != NULL) {
        free(pUdpTable);
        pUdpTable = NULL;
    }

    // ����Winsock
    WSACleanup();

    return connections;
}

// ��ȡ���̵�����������Ϣ
std::vector<NetworkConnection> ProcessInfoManager::GetProcessConnections(DWORD pid) {
    std::vector<NetworkConnection> processConnections;
    std::vector<NetworkConnection> allConnections = GetAllNetworkConnections();

    // ɸѡ��ָ�����̵�����
    for (const auto& conn : allConnections) {
        if (conn.owningPid == pid) {
            processConnections.push_back(conn);
        }
    }

    return processConnections;
}

// 注意：Base64编码函数已移至Utils类中，这里保留是为了向后兼容
// 建议使用 Utils::Base64Encode 替代此函数
std::string Base64Encode(const std::vector<BYTE>& data) {
    return Utils::Base64Encode(data);
}

// 注意：此方法已废弃，建议使用 Utils::GetFileIconAsBase64 替代
// 保留此方法仅为向后兼容
HICON ProcessInfoManager::GetFileIcon(const std::wstring& filePath, BOOL largeIcon) {
    SHFILEINFOW fileInfo = { 0 };
    UINT flags = SHGFI_ICON | SHGFI_USEFILEATTRIBUTES;

    if (largeIcon)
        flags |= SHGFI_LARGEICON;
    else
        flags |= SHGFI_SMALLICON;

    DWORD_PTR result = SHGetFileInfoW(
        filePath.c_str(),
        FILE_ATTRIBUTE_NORMAL,
        &fileInfo,
        sizeof(SHFILEINFOW),
        flags
    );

    if (result == 0) {
        DWORD error = GetLastError();
        std::cerr << "SHGetFileInfoW failed, error: " << error << std::endl;
        return NULL;
    }

    return fileInfo.hIcon;
}





// 获取进程创建时间
std::string ProcessInfoManager::GetProcessCreateTime(DWORD pid) {
    // 打开进程
    HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, pid);
    if (hProcess == NULL) {
        return "Unknown";
    }

    FILETIME createTime, exitTime, kernelTime, userTime;
    if (GetProcessTimes(hProcess, &createTime, &exitTime, &kernelTime, &userTime)) {
        // 转换UTC时间到本地时间
        FILETIME localFileTime;
        if (FileTimeToLocalFileTime(&createTime, &localFileTime)) {
            SYSTEMTIME systemTime;
            if (FileTimeToSystemTime(&localFileTime, &systemTime)) {
                char timeStr[64];
                sprintf_s(timeStr, sizeof(timeStr), "%04d-%02d-%02d %02d:%02d:%02d",
                    systemTime.wYear, systemTime.wMonth, systemTime.wDay,
                    systemTime.wHour, systemTime.wMinute, systemTime.wSecond);
                CloseHandle(hProcess);
                return std::string(timeStr);
            }
        }
    }

    CloseHandle(hProcess);
    return "Unknown";
}

// 获取可执行文件图标的Base64编码
// 注意：此方法已重构为使用Utils中的统一图标获取方法
std::string ProcessInfoManager::GetExeIconAsBase64(const std::wstring& exePath) {
    try {
        // 使用Utils中的统一图标获取方法
        std::wstring iconBase64Wide = Utils::GetFileIconAsBase64(exePath, 32);
        if (!iconBase64Wide.empty()) {
            // 转换为UTF-8字符串
            return Utils::WStringToUTF8(iconBase64Wide);
        }
    }
    catch (const std::exception& e) {
        std::cerr << "GetExeIconAsBase64 failed: " << e.what() << std::endl;
    }

    return "";
}