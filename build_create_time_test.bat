@echo off
echo Building network connection create time test...

cl /EHsc /std:c++14 /I. /I"C:\vcpkg\installed\x86-windows\include" ^
   test_network_create_time.cpp ^
   src\NetworkConnectionManager.cpp ^
   src\Utils.cpp ^
   /link /LIBPATH:"C:\vcpkg\installed\x86-windows\lib" ^
   /LIBPATH:"Release" ^
   BrowserDataExtractor.lib ^
   iphlpapi.lib ws2_32.lib psapi.lib shell32.lib gdi32.lib user32.lib advapi32.lib ^
   /out:test_network_create_time.exe

if %ERRORLEVEL% EQU 0 (
    echo Build successful! Running test...
    echo.
    test_network_create_time.exe
) else (
    echo Build failed!
)

pause
