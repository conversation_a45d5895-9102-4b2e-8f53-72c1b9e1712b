#pragma once
// ����Windows XP�����Ժ�
#ifndef _WIN32_WINNT
#define _WIN32_WINNT 0x0501  // Windows XP
#endif

// ����Windowsͷ�ļ�����ȷ˳��
#include <winsock2.h>
#include <windows.h>
#include <ws2tcpip.h>
#include <iphlpapi.h>
#include <tlhelp32.h>
#include "Utils.h"

#include <string>
#include <vector>
#include <nlohmann/json.hpp>

// ʹ��nlohmann/json�����ռ�
using json = nlohmann::json;

// ����������Ϣ�ṹ��
struct NetworkConnection {
    std::string localAddress;    // ���ص�ַ
    DWORD localPort;            // ���ض˿�
    std::string remoteAddress;   // Զ�̵�ַ
    DWORD remotePort;           // Զ�̶˿�
    DWORD state;                // ����״̬
    DWORD owningPid;            // ��������ID
};

// ������Ϣ�ṹ��
struct ProcessInfo {
    DWORD pid;                  // ����ID
    std::wstring processName;   // ��������
    std::wstring processPath;   // ��������·��
    std::string create_time;    // 进程创建时间
    std::vector<NetworkConnection> connections; // ����������Ϣ
};

class ProcessInfoManager
{
public:
    // ���캯��
    ProcessInfoManager() = default;

    // ��ȡ�����������еĽ�����Ϣ
    std::vector<ProcessInfo> GetAllProcesses();

    // ���ݽ������Ʋ��ҽ���
    std::vector<ProcessInfo> FindProcessesByName(const std::wstring& processName);

    // ����PID���ҽ���
    ProcessInfo FindProcessByPID(DWORD pid);

    // ��������Ϣת��ΪJSON��ʽ
    json ProcessesToJson(const std::vector<ProcessInfo>& processes);

    // ��ȡ���н��̲�ת��ΪJSON�ַ���
    std::string GetAllProcessesAsJsonString();

    // ��ȡ����·��
    static std::wstring GetProcessPath(DWORD pid);

    // ��ȡ���̵�����������Ϣ
    static std::vector<NetworkConnection> GetProcessConnections(DWORD pid);

    // ��ȡ��������������Ϣ
    static std::vector<NetworkConnection> GetAllNetworkConnections();

    // 获取文件图标（已废弃，建议使用Utils::GetFileIconAsBase64）
    static HICON GetFileIcon(const std::wstring& filePath, BOOL largeIcon = TRUE);

    // 获取可执行文件图标的Base64编码（已重构为使用Utils中的方法）
    static std::string GetExeIconAsBase64(const std::wstring& exePath);

    // ��ȡ���н��̵���ϸ��Ϣ������ͼ����������ӣ�
    json GetAllProcessesDetailed();

    // ��ȡָ�����̵���ϸ��Ϣ������ͼ����������ӣ�
    json GetProcessDetailedByName(const std::wstring& processName);

    // ��ȡ�������������ӵĽ���
    json GetAllNetworkProcesses();

private:
    // ��ȡ���̿�ִ���ļ�·��
    static std::wstring GetProcessExecutablePath(HANDLE hProcess);

    // ��IP��ַ�������ֽ���ת��Ϊ�ַ���
    static std::string IpAddressToString(DWORD ipAddress);

    // 获取进程创建时间
    static std::string GetProcessCreateTime(DWORD pid);
};

