#include "pch.h"
#include "include/NetworkConnectionManager.h"
#include <iostream>
#include <string>
#include <nlohmann/json.hpp>

using json = nlohmann::json;

int main() {
    std::cout << u8"=== 测试网络连接JSON数据中的图标信息 ===" << std::endl;
    
    try {
        // 创建网络连接管理器实例
        NetworkConnectionManager networkManager;
        
        std::cout << u8"\n步骤1: 获取所有网络连接的JSON数据..." << std::endl;
        
        // 获取网络连接的JSON字符串
        std::string jsonString = networkManager.GetAllNetworkConnectionsAsJsonString();
        
        std::cout << u8"JSON数据大小: " << jsonString.size() << u8" 字节" << std::endl;
        
        if (jsonString.empty() || jsonString == "[]") {
            std::cout << u8"⚠ 没有找到网络连接数据" << std::endl;
            std::cout << u8"这可能是因为当前没有活跃的网络连接" << std::endl;
        } else {
            std::cout << u8"\n步骤2: 解析JSON数据..." << std::endl;
            
            // 解析JSON
            json networkData = json::parse(jsonString);
            
            if (networkData.is_array() && !networkData.empty()) {
                std::cout << u8"找到 " << networkData.size() << u8" 个网络连接记录" << std::endl;
                
                int recordsWithIcons = 0;
                int totalIconSize = 0;
                
                // 检查前几个记录的图标数据
                int checkCount = std::min(5, static_cast<int>(networkData.size()));
                
                for (int i = 0; i < checkCount; i++) {
                    const auto& record = networkData[i];
                    
                    std::cout << u8"\n记录 " << (i + 1) << u8":" << std::endl;
                    
                    if (record.contains("processName")) {
                        std::cout << u8"  进程名: " << record["processName"].get<std::string>() << std::endl;
                    }
                    
                    if (record.contains("processPath")) {
                        std::cout << u8"  进程路径: " << record["processPath"].get<std::string>() << std::endl;
                    }
                    
                    if (record.contains("iconBase64")) {
                        std::string iconData = record["iconBase64"].get<std::string>();
                        std::cout << u8"  图标数据: ";
                        
                        if (iconData.empty()) {
                            std::cout << u8"空" << std::endl;
                        } else {
                            std::cout << iconData.length() << u8" 字符" << std::endl;
                            recordsWithIcons++;
                            totalIconSize += iconData.length();
                            
                            // 显示图标数据的前50个字符作为预览
                            if (iconData.length() > 50) {
                                std::cout << u8"  图标预览: " << iconData.substr(0, 50) << u8"..." << std::endl;
                            } else {
                                std::cout << u8"  图标数据: " << iconData << std::endl;
                            }
                        }
                    } else {
                        std::cout << u8"  图标数据: 字段不存在" << std::endl;
                    }
                    
                    if (record.contains("protocol") && record.contains("localPort")) {
                        std::cout << u8"  连接: " << record["protocol"].get<std::string>() 
                                  << u8" 端口 " << record["localPort"].get<int>() << std::endl;
                    }
                }
                
                std::cout << u8"\n=== 图标数据统计 ===" << std::endl;
                std::cout << u8"检查的记录数: " << checkCount << std::endl;
                std::cout << u8"包含图标的记录数: " << recordsWithIcons << std::endl;
                std::cout << u8"图标数据总大小: " << totalIconSize << u8" 字符" << std::endl;
                
                if (recordsWithIcons > 0) {
                    std::cout << u8"✓ JSON数据中包含图标信息！" << std::endl;
                    std::cout << u8"平均图标大小: " << (totalIconSize / recordsWithIcons) << u8" 字符" << std::endl;
                } else {
                    std::cout << u8"⚠ JSON数据中没有图标信息" << std::endl;
                    std::cout << u8"可能的原因:" << std::endl;
                    std::cout << u8"1. 进程路径获取失败" << std::endl;
                    std::cout << u8"2. 图标提取失败" << std::endl;
                    std::cout << u8"3. Utils::GetFileIconAsBase64 方法有问题" << std::endl;
                }
                
            } else {
                std::cout << u8"JSON数据为空数组" << std::endl;
            }
        }
        
        std::cout << u8"\n步骤3: 测试统一接口..." << std::endl;
        
        // 测试统一接口
        std::string unifiedJsonString = networkManager.GetNetworkConnectionDataAsJsonString();
        std::cout << u8"统一接口JSON数据大小: " << unifiedJsonString.size() << u8" 字节" << std::endl;
        
        if (!unifiedJsonString.empty() && unifiedJsonString != "[]") {
            json unifiedData = json::parse(unifiedJsonString);
            if (unifiedData.is_array() && !unifiedData.empty()) {
                const auto& firstRecord = unifiedData[0];
                if (firstRecord.contains("process_icon")) {
                    std::string iconData = firstRecord["process_icon"].get<std::string>();
                    std::cout << u8"统一接口图标数据: " << iconData.length() << u8" 字符" << std::endl;
                    if (!iconData.empty()) {
                        std::cout << u8"✓ 统一接口也包含图标数据！" << std::endl;
                    }
                }
            }
        }
        
        std::cout << u8"\n=== 测试完成 ===" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << u8"\n✗ 测试失败 - 标准异常: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << u8"\n✗ 测试失败 - 未知异常" << std::endl;
        return 1;
    }
    
    std::cout << u8"\n按任意键退出..." << std::endl;
    system("pause");
    return 0;
}
