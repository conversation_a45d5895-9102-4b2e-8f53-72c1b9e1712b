﻿// Windows XP兼容性和安全函数定义
#define _CRT_SECURE_NO_WARNINGS
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif

#include "pch.h"
#include "../include/ExifManager.h"
#include <sstream>
#include <iomanip>
#include <comdef.h>
#include <gdiplus.h>
#include <algorithm>
#include <vector>
#include <io.h>
#include <direct.h>
#include <locale>
#include <codecvt>
#include <fstream>
#include <ctime>
// 注意：移除了chrono和thread以确保Windows XP兼容性

#pragma comment(lib, "gdiplus.lib")

// Windows XP兼容的数字转字符串函数
template<typename T>
std::string NumberToString(T value) {
    std::ostringstream oss;
    oss << value;
    return oss.str();
}

// Windows XP兼容的字符串转整数函数
int StringToInt(const std::string& str) {
    try {
        return atoi(str.c_str());
    } catch (...) {
        return 0;
    }
}

// 改进的UTF-8字符串清理函数 - 保留有效的UTF-8字符
std::string CleanUtf8String(const std::string& input) {
    if (input.empty()) {
        return input;
    }

    std::string result;
    result.reserve(input.length() * 2); // 预留更多空间用于转义字符

    for (size_t i = 0; i < input.length(); ++i) {
        unsigned char c = static_cast<unsigned char>(input[i]);

        // 处理ASCII字符
        if (c < 128) {
            // 处理控制字符
            if (c < 32) {
                if (c == '\n') {
                    result += "\\n";
                } else if (c == '\r') {
                    result += "\\r";
                } else if (c == '\t') {
                    result += "\\t";
                } else if (c == '\b') {
                    result += "\\b";
                } else if (c == '\f') {
                    result += "\\f";
                } else if (c == '\0') {
                    // 跳过null字符
                    continue;
                } else {
                    // 其他控制字符用Unicode转义 - Windows XP兼容版本
                    char buffer[8];
#ifdef _MSC_VER
                    sprintf_s(buffer, sizeof(buffer), "\\u%04x", c);
#else
                    sprintf(buffer, "\\u%04x", c);
#endif
                    result += buffer;
                }
            }
            // 处理JSON特殊字符
            else if (c == '"') {
                result += "\\\"";
            } else if (c == '\\') {
                result += "\\\\";
            } else {
                result += c;
            }
        }
        // 处理UTF-8多字节字符 - 保留有效的UTF-8序列
        else {
            // 检查是否是有效的UTF-8序列开始
            if ((c & 0xE0) == 0xC0) {
                // 2字节UTF-8序列
                if (i + 1 < input.length()) {
                    unsigned char c2 = static_cast<unsigned char>(input[i + 1]);
                    if ((c2 & 0xC0) == 0x80 && c >= 0xC2) {
                        result += c;
                        result += c2;
                        i++;
                        continue;
                    }
                }
            }
            else if ((c & 0xF0) == 0xE0) {
                // 3字节UTF-8序列
                if (i + 2 < input.length()) {
                    unsigned char c2 = static_cast<unsigned char>(input[i + 1]);
                    unsigned char c3 = static_cast<unsigned char>(input[i + 2]);
                    if ((c2 & 0xC0) == 0x80 && (c3 & 0xC0) == 0x80) {
                        result += c;
                        result += c2;
                        result += c3;
                        i += 2;
                        continue;
                    }
                }
            }
            else if ((c & 0xF8) == 0xF0) {
                // 4字节UTF-8序列
                if (i + 3 < input.length()) {
                    unsigned char c2 = static_cast<unsigned char>(input[i + 1]);
                    unsigned char c3 = static_cast<unsigned char>(input[i + 2]);
                    unsigned char c4 = static_cast<unsigned char>(input[i + 3]);
                    if ((c2 & 0xC0) == 0x80 && (c3 & 0xC0) == 0x80 && (c4 & 0xC0) == 0x80) {
                        result += c;
                        result += c2;
                        result += c3;
                        result += c4;
                        i += 3;
                        continue;
                    }
                }
            }

            // 无效的UTF-8字节，替换为?
            result += '?';
        }
    }

    return result;
}

// 专门用于文件路径的清理函数（保留中文字符，不进行JSON转义）
std::string CleanFilePathString(const std::string& input) {
    if (input.empty()) {
        return input;
    }

    std::string result;
    result.reserve(input.length());

    for (size_t i = 0; i < input.length(); ++i) {
        unsigned char c = static_cast<unsigned char>(input[i]);

        // 处理ASCII字符
        if (c < 128) {
            // 跳过控制字符，但保留可打印字符
            if (c >= 32 || c == '\t') {
                result += c;
            }
        }
        // 处理UTF-8多字节字符 - 保留有效的UTF-8序列
        else {
            // 检查是否是有效的UTF-8序列开始
            if ((c & 0xE0) == 0xC0) {
                // 2字节UTF-8序列
                if (i + 1 < input.length()) {
                    unsigned char c2 = static_cast<unsigned char>(input[i + 1]);
                    if ((c2 & 0xC0) == 0x80 && c >= 0xC2) {
                        result += c;
                        result += c2;
                        i++;
                        continue;
                    }
                }
            }
            else if ((c & 0xF0) == 0xE0) {
                // 3字节UTF-8序列
                if (i + 2 < input.length()) {
                    unsigned char c2 = static_cast<unsigned char>(input[i + 1]);
                    unsigned char c3 = static_cast<unsigned char>(input[i + 2]);
                    if ((c2 & 0xC0) == 0x80 && (c3 & 0xC0) == 0x80) {
                        result += c;
                        result += c2;
                        result += c3;
                        i += 2;
                        continue;
                    }
                }
            }
            else if ((c & 0xF8) == 0xF0) {
                // 4字节UTF-8序列
                if (i + 3 < input.length()) {
                    unsigned char c2 = static_cast<unsigned char>(input[i + 1]);
                    unsigned char c3 = static_cast<unsigned char>(input[i + 2]);
                    unsigned char c4 = static_cast<unsigned char>(input[i + 3]);
                    if ((c2 & 0xC0) == 0x80 && (c3 & 0xC0) == 0x80 && (c4 & 0xC0) == 0x80) {
                        result += c;
                        result += c2;
                        result += c3;
                        result += c4;
                        i += 3;
                        continue;
                    }
                }
            }

            // 无效的UTF-8字节，替换为?
            result += '?';
        }
    }

    return result;
}

// 生成带时间戳的JSON文件名 - Windows XP兼容版本
std::string GenerateJsonFileName() {
    time_t now = time(0);
    struct tm* timeinfo;

#ifdef _MSC_VER
    struct tm timeinfo_buf;
    if (localtime_s(&timeinfo_buf, &now) == 0) {
        timeinfo = &timeinfo_buf;
    } else {
        timeinfo = localtime(&now);  // 备用方案
    }
#else
    timeinfo = localtime(&now);  // Windows XP兼容
#endif

    char timestamp[32];
    if (timeinfo) {
        strftime(timestamp, sizeof(timestamp), "%Y%m%d_%H%M%S", timeinfo);
    } else {
        strncpy(timestamp, "unknown_time", sizeof(timestamp) - 1);
        timestamp[sizeof(timestamp) - 1] = '\0';
    }

    return std::string("EXIF_Analysis_") + timestamp + ".json";
}

// 保存JSON结果到文件
bool SaveJsonToFile(const std::string& jsonContent, const std::string& fileName) {
    try {
        std::ofstream file(fileName, std::ios::out | std::ios::trunc);
        if (!file.is_open()) {
            return false;
        }

        file << jsonContent;
        file.close();
        return true;
    } catch (const std::exception& e) {
        return false;
    }
}

// 创建只包含ASCII字符的安全JSON
nlohmann::json CreateSafeJson(const nlohmann::json& originalJson) {
    nlohmann::json safeJson;

    try {
        // 递归清理JSON中的所有字符串
        for (auto it = originalJson.begin(); it != originalJson.end(); ++it) {
            const std::string& key = it.key();
            std::string safeKey = CleanUtf8String(key);

            if (it.value().is_string()) {
                std::string originalStr = it.value().get<std::string>();
                safeJson[safeKey] = CleanUtf8String(originalStr);
            } else if (it.value().is_number()) {
                safeJson[safeKey] = it.value();
            } else if (it.value().is_boolean()) {
                safeJson[safeKey] = it.value();
            } else if (it.value().is_object()) {
                safeJson[safeKey] = CreateSafeJson(it.value());
            } else if (it.value().is_array()) {
                nlohmann::json safeArray = nlohmann::json::array();
                for (const auto& item : it.value()) {
                    if (item.is_string()) {
                        safeArray.push_back(CleanUtf8String(item.get<std::string>()));
                    } else if (item.is_object()) {
                        safeArray.push_back(CreateSafeJson(item));
                    } else {
                        safeArray.push_back(item);
                    }
                }
                safeJson[safeKey] = safeArray;
            } else {
                // 对于其他类型，直接使用原值，避免双重转义
                safeJson[safeKey] = it.value();
            }
        }
    } catch (...) {
        // 如果清理过程失败，返回基本错误信息
        safeJson["status"] = "error";
        safeJson["message"] = "Failed to clean JSON data";
    }

    return safeJson;
}

// 安全的JSON转换函数
std::string SafeJsonDump(const nlohmann::json& json) {
    try {
        return json.dump(4);
    } catch (const nlohmann::json::exception& e) {
        // 如果JSON dump失败，创建清理后的版本
        try {
            nlohmann::json safeJson = CreateSafeJson(json);
            return safeJson.dump(4);
        } catch (...) {
            // 最后的备用方案
            nlohmann::json errorJson;
            errorJson["status"] = "error";
            errorJson["message"] = "JSON encoding error";
            errorJson["error_code"] = e.id;
            errorJson["error_message"] = "Failed to encode JSON due to invalid characters";

            try {
                return errorJson.dump(4);
            } catch (...) {
                return "{\"status\":\"error\",\"message\":\"Critical JSON encoding failure\"}";
            }
        }
    }
}

ExifExtractor::ExifExtractor() : m_initialized(false)
{
}

ExifExtractor::~ExifExtractor()
{
    Cleanup();
}

bool ExifExtractor::Initialize()
{
    if (m_initialized)
        return true;

    // 初始化GDI+
    Gdiplus::GdiplusStartupInput gdiplusStartupInput;
    Gdiplus::Status status = Gdiplus::GdiplusStartup(&m_gdiplusToken, &gdiplusStartupInput, NULL);
    if (status != Gdiplus::Ok)
    {
        SetError("Failed to initialize GDI+");
        return false;
    }

    m_initialized = true;
    return true;
}

void ExifExtractor::Cleanup()
{
    if (m_initialized)
    {
        Gdiplus::GdiplusShutdown(m_gdiplusToken);
        m_initialized = false;
    }
}

bool ExifExtractor::ExtractExifInfo(const std::string& filePath, ExifInfo& exifInfo)
{
    if (!m_initialized)
    {
        SetError("ExifExtractor not initialized");
        return false;
    }

    exifInfo.Clear();

    // 转换文件路径为宽字符 - 优先使用UTF-8转换（与文件扫描保持一致）
    int widePathLength = MultiByteToWideChar(CP_UTF8, 0, filePath.c_str(), -1, NULL, 0);
    std::vector<wchar_t> widePath;

    if (widePathLength > 0) {
        widePath.resize(widePathLength);
        int result = MultiByteToWideChar(CP_UTF8, 0, filePath.c_str(), -1, &widePath[0], widePathLength);
        if (result == 0) {
            // UTF-8转换失败，尝试ANSI转换
            widePathLength = MultiByteToWideChar(CP_ACP, 0, filePath.c_str(), -1, NULL, 0);
            if (widePathLength > 0) {
                widePath.resize(widePathLength);
                result = MultiByteToWideChar(CP_ACP, 0, filePath.c_str(), -1, &widePath[0], widePathLength);
                if (result == 0) {
                    SetError("Failed to convert file path from both UTF-8 and ANSI");
                    return false;
                }
            } else {
                SetError("Failed to convert file path to wide characters");
                return false;
            }
        }
    } else {
        // UTF-8转换失败，尝试ANSI转换
        widePathLength = MultiByteToWideChar(CP_ACP, 0, filePath.c_str(), -1, NULL, 0);
        if (widePathLength > 0) {
            widePath.resize(widePathLength);
            int result = MultiByteToWideChar(CP_ACP, 0, filePath.c_str(), -1, &widePath[0], widePathLength);
            if (result == 0) {
                SetError("Failed to convert file path to wide characters");
                return false;
            }
        } else {
            SetError("Failed to convert file path to wide characters");
            return false;
        }
    }

    if (widePath.empty())
    {
        SetError("Failed to convert file path to wide characters");
        return false;
    }

    // 使用GDI+加载图像
    Gdiplus::Image* image = Gdiplus::Image::FromFile(&widePath[0]);
    if (!image || image->GetLastStatus() != Gdiplus::Ok)
    {
        std::string errorMsg = "Failed to load image with GDI+";
        if (image) {
            Gdiplus::Status status = image->GetLastStatus();
            errorMsg += " (Status: " + NumberToString(status) + ")";
            delete image;
        }

        // 检查文件是否真的存在
        DWORD attributes = GetFileAttributesW(&widePath[0]);
        if (attributes == INVALID_FILE_ATTRIBUTES) {
            errorMsg += " - File not found";
        } else {
            errorMsg += " - File exists but cannot be loaded";
        }

        SetError(errorMsg);
        return false;
    }

    // 获取图像尺寸
    exifInfo.width = NumberToString(image->GetWidth());
    exifInfo.height = NumberToString(image->GetHeight());

    // 获取属性项数量
    UINT totalBufferSize, numProperties;
    image->GetPropertySize(&totalBufferSize, &numProperties);

    if (numProperties == 0)
    {
        // 没有EXIF数据，设置默认值
        exifInfo.manufacturer = "Unknown";
        exifInfo.model = "Unknown";
        exifInfo.dateTime = "Unknown";
        delete image;
        return true;
    }

    // 获取所有属性项
    Gdiplus::PropertyItem* propertyItems = (Gdiplus::PropertyItem*)malloc(totalBufferSize);
    if (!propertyItems)
    {
        SetError("Failed to allocate memory for property items");
        delete image;
        return false;
    }

    Gdiplus::Status status = image->GetAllPropertyItems(totalBufferSize, numProperties, propertyItems);
    if (status != Gdiplus::Ok)
    {
        SetError("Failed to get property items");
        free(propertyItems);
        delete image;
        return false;
    }

    // 解析EXIF数据
    for (UINT i = 0; i < numProperties; i++)
    {
        Gdiplus::PropertyItem* item = &propertyItems[i];

        switch (item->id)
        {
        case 0x010F: // 制造商
            if (item->type == 2 && item->value && item->length > 0) // ASCII字符串
            {
                std::string rawStr((char*)item->value, item->length - 1); // 排除null终止符
                exifInfo.manufacturer = CleanUtf8String(rawStr);
                if (exifInfo.manufacturer.empty()) {
                    exifInfo.manufacturer = "Unknown";
                }
            }
            break;

        case 0x0110: // 相机型号
            if (item->type == 2 && item->value && item->length > 0) // ASCII字符串
            {
                std::string rawStr((char*)item->value, item->length - 1); // 排除null终止符
                exifInfo.model = CleanUtf8String(rawStr);
                if (exifInfo.model.empty()) {
                    exifInfo.model = "Unknown";
                }
            }
            break;

        case 0x0132: // 拍摄日期时间
            if (item->type == 2 && item->value && item->length > 0) // ASCII字符串
            {
                std::string rawStr((char*)item->value, item->length - 1); // 排除null终止符
                exifInfo.dateTime = CleanUtf8String(rawStr);
                if (exifInfo.dateTime.empty()) {
                    exifInfo.dateTime = "Unknown";
                }
            }
            break;
        }
    }

    free(propertyItems);
    delete image;
    return true;
}

void ExifExtractor::SetError(const std::string& error)
{
    m_lastError = error;
}

bool ExifExtractor::HasGpsInfo(const ExifInfo& exifInfo)
{
    return !exifInfo.gpsLatitude.empty() && !exifInfo.gpsLongitude.empty();
}

std::vector<std::string> ExifExtractor::ScanImageFiles(void(*progressCallback)(const std::string&, int))
{
    std::vector<std::string> imageFiles;
    std::vector<std::string> imageExtensions = {".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".tif", ".gif"};

    if (progressCallback) {
        progressCallback("Scanning for image files...", 5);
    }

    // 获取所有驱动器
    DWORD drives = GetLogicalDrives();
    std::vector<std::string> driveLetters;

    for (int i = 0; i < 26; i++) {
        if (drives & (1 << i)) {
            char driveLetter = 'A' + i;
            std::string drivePath = std::string(1, driveLetter) + ":\\";

            // 检查驱动器类型，只扫描固定驱动器和可移动驱动器
            UINT driveType = GetDriveTypeA(drivePath.c_str());
            if (driveType == DRIVE_FIXED || driveType == DRIVE_REMOVABLE) {
                driveLetters.push_back(drivePath);
            }
        }
    }

    if (progressCallback) {
        progressCallback("Found " + NumberToString(driveLetters.size()) + " drives to scan", 10);
    }

    int totalProgress = 10;
    int driveCount = (int)driveLetters.size();
    int progressPerDrive = 80 / (driveCount > 0 ? driveCount : 1);

    for (const auto& drive : driveLetters) {
        if (progressCallback) {
            progressCallback("Starting smart scan of drive " + drive, totalProgress);
        }

        std::wstring drivePath = std::wstring(drive.begin(), drive.end());
        std::vector<std::wstring> wideImagePaths;

        if (progressCallback) {
            progressCallback("Scanning user directories first on " + drive, totalProgress + 2);
        }

        // 优先扫描用户相关目录
        std::vector<std::wstring> priorityDirs = {
            drivePath + L"Users",
            drivePath + L"Documents and Settings"  // Windows XP兼容
        };

        for (const auto& priorityDir : priorityDirs) {
            WIN32_FIND_DATAW findData;
            HANDLE hFind = FindFirstFileW(priorityDir.c_str(), &findData);
            if (hFind != INVALID_HANDLE_VALUE) {
                FindClose(hFind);
                if (progressCallback) {
                    progressCallback("Scanning priority directory: " + std::string(priorityDir.begin(), priorityDir.end()), totalProgress + 3);
                }
                ScanDirectoryW(priorityDir, wideImagePaths);
            }
        }

        if (progressCallback) {
            progressCallback("Scanning remaining directories on " + drive, totalProgress + 5);
        }

        // 然后扫描其他目录（但跳过已扫描的用户目录）
        ScanDirectoryW(drivePath, wideImagePaths);

        if (progressCallback) {
            progressCallback("Found " + NumberToString(wideImagePaths.size()) + " images on " + drive, totalProgress + 10);
        }

        // 转换宽字符路径为UTF-8并添加到结果中
        for (const auto& widePath : wideImagePaths) {
            int pathLength = WideCharToMultiByte(CP_UTF8, 0, widePath.c_str(), -1, NULL, 0, NULL, NULL);
            std::string narrowPath;

            if (pathLength > 0) {
                std::vector<char> pathBuffer(pathLength);
                int convertResult = WideCharToMultiByte(CP_UTF8, 0, widePath.c_str(), -1, &pathBuffer[0], pathLength, NULL, NULL);
                if (convertResult > 0) {
                    narrowPath = std::string(&pathBuffer[0]);
                }
            }

            if (narrowPath.empty()) {
                pathLength = WideCharToMultiByte(CP_ACP, 0, widePath.c_str(), -1, NULL, 0, NULL, NULL);
                if (pathLength > 0) {
                    std::vector<char> pathBuffer(pathLength);
                    WideCharToMultiByte(CP_ACP, 0, widePath.c_str(), -1, &pathBuffer[0], pathLength, NULL, NULL);
                    narrowPath = std::string(&pathBuffer[0]);
                }
            }

            if (!narrowPath.empty()) {
                imageFiles.push_back(narrowPath);
            }
        }



        totalProgress += progressPerDrive;
    }

    if (progressCallback) {
        progressCallback("Found " + NumberToString(imageFiles.size()) + " image files", 90);
    }

    return imageFiles;
}

// 检查是否为图片文件（宽字符版本）
bool ExifExtractor::IsImageFileW(const std::wstring& filename) {
    std::vector<std::wstring> imageExtensions = {
        L".jpg", L".jpeg", L".png", L".bmp", L".gif", L".tiff", L".webp", L".ico"
    };

    // 转换文件名为小写进行比较
    std::wstring lowerFilename = filename;
    std::transform(lowerFilename.begin(), lowerFilename.end(), lowerFilename.begin(), ::towlower);

    for (const auto& ext : imageExtensions) {
        if (lowerFilename.length() >= ext.length() &&
            lowerFilename.compare(lowerFilename.length() - ext.length(), ext.length(), ext) == 0) {
            return true;
        }
    }
    return false;
}

// 宽字符版本的目录扫描函数 - 完全递归搜索
void ExifExtractor::ScanDirectoryW(const std::wstring& directory,
                                   std::vector<std::wstring>& imagePaths) {
    WIN32_FIND_DATAW findFileData;
    HANDLE hFind = INVALID_HANDLE_VALUE;

    // 构建目录搜索模式
    std::wstring searchPath = directory + L"\\*";
    hFind = FindFirstFileW(searchPath.c_str(), &findFileData);

    if (hFind == INVALID_HANDLE_VALUE) {
        return; // 无法打开目录
    }

    do {
        const std::wstring fileName = findFileData.cFileName;

        // 排除 "." 和 ".."
        if (fileName == L"." || fileName == L"..") continue;

        std::wstring fullPath = directory + L"\\" + fileName;

        if (findFileData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY) {
            // 如果是目录，递归查找
            ScanDirectoryW(fullPath, imagePaths);
        }
        else {
            // 如果是文件，检查是否为图片
            if (IsImageFileW(fileName)) {
                imagePaths.push_back(fullPath);
            }
        }
    } while (FindNextFileW(hFind, &findFileData) != 0);

    FindClose(hFind);
}

// 兼容性包装函数 - 转换为多字节字符串版本
void ExifExtractor::ScanDirectory(const std::string& dirPath,
                                  const std::vector<std::string>& extensions,
                                  std::vector<std::string>& imageFiles)
{
    // 转换输入路径为宽字符
    int widePathLength = MultiByteToWideChar(CP_UTF8, 0, dirPath.c_str(), -1, NULL, 0);
    if (widePathLength == 0) {
        // UTF-8转换失败，尝试ANSI
        widePathLength = MultiByteToWideChar(CP_ACP, 0, dirPath.c_str(), -1, NULL, 0);
        if (widePathLength == 0) {
            return;
        }
    }

    std::vector<wchar_t> wideDirPath(widePathLength);
    int result = MultiByteToWideChar(CP_UTF8, 0, dirPath.c_str(), -1, &wideDirPath[0], widePathLength);
    if (result == 0) {
        // UTF-8转换失败，尝试ANSI
        result = MultiByteToWideChar(CP_ACP, 0, dirPath.c_str(), -1, &wideDirPath[0], widePathLength);
        if (result == 0) {
            return;
        }
    }

    std::wstring wideDirectory(&wideDirPath[0]);
    std::vector<std::wstring> wideImagePaths;

    // 使用宽字符版本扫描（完全递归）
    ScanDirectoryW(wideDirectory, wideImagePaths);

    // 转换结果为多字节字符串
    for (const auto& widePath : wideImagePaths) {
        // 优先使用UTF-8转换
        int pathLength = WideCharToMultiByte(CP_UTF8, 0, widePath.c_str(), -1, NULL, 0, NULL, NULL);
        std::string narrowPath;

        if (pathLength > 0) {
            std::vector<char> pathBuffer(pathLength);
            int convertResult = WideCharToMultiByte(CP_UTF8, 0, widePath.c_str(), -1, &pathBuffer[0], pathLength, NULL, NULL);
            if (convertResult > 0) {
                narrowPath = std::string(&pathBuffer[0]);
            }
        }

        // 如果UTF-8转换失败，使用ANSI作为备用
        if (narrowPath.empty()) {
            pathLength = WideCharToMultiByte(CP_ACP, 0, widePath.c_str(), -1, NULL, 0, NULL, NULL);
            if (pathLength > 0) {
                std::vector<char> pathBuffer(pathLength);
                WideCharToMultiByte(CP_ACP, 0, widePath.c_str(), -1, &pathBuffer[0], pathLength, NULL, NULL);
                narrowPath = std::string(&pathBuffer[0]);
            }
        }

        // 只有成功转换的路径才添加到结果中
        if (!narrowPath.empty()) {
            imageFiles.push_back(narrowPath);
        }
    }
}

