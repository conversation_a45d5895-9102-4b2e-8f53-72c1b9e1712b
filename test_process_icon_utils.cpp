#include "pch.h"
#include "include/ProcessInfoManager.h"
#include "include/Utils.h"
#include <iostream>
#include <string>

int main() {
    std::cout << u8"测试 Init_ProcessInfoMsg 中的图标获取方法是否使用 Utils..." << std::endl;
    
    try {
        // 创建进程信息管理器实例
        ProcessInfoManager processManager;
        
        // 测试1: 直接测试Utils中的图标获取方法
        std::cout << u8"\n测试1: 直接使用 Utils::GetFileIconAsBase64" << std::endl;
        std::wstring testPath = L"C:\\Windows\\System32\\notepad.exe";
        std::wstring iconBase64 = Utils::GetFileIconAsBase64(testPath, 32);
        
        if (!iconBase64.empty()) {
            std::cout << u8"✓ Utils::GetFileIconAsBase64 工作正常" << std::endl;
            std::cout << u8"  图标数据大小: " << iconBase64.length() << u8" 字符" << std::endl;
        } else {
            std::cout << u8"✗ Utils::GetFileIconAsBase64 返回空结果" << std::endl;
        }
        
        // 测试2: 测试ProcessInfoManager中重构后的方法
        std::cout << u8"\n测试2: 使用 ProcessInfoManager::GetExeIconAsBase64" << std::endl;
        std::string iconBase64Str = ProcessInfoManager::GetExeIconAsBase64(testPath);
        
        if (!iconBase64Str.empty()) {
            std::cout << u8"✓ ProcessInfoManager::GetExeIconAsBase64 工作正常" << std::endl;
            std::cout << u8"  图标数据大小: " << iconBase64Str.length() << u8" 字符" << std::endl;
        } else {
            std::cout << u8"✗ ProcessInfoManager::GetExeIconAsBase64 返回空结果" << std::endl;
        }
        
        // 测试3: 验证两种方法返回的结果是否一致
        std::cout << u8"\n测试3: 验证两种方法的一致性" << std::endl;
        std::string utilsResult = Utils::WStringToUTF8(iconBase64);
        
        if (utilsResult == iconBase64Str) {
            std::cout << u8"✓ 两种方法返回的结果一致" << std::endl;
        } else {
            std::cout << u8"✗ 两种方法返回的结果不一致" << std::endl;
            std::cout << u8"  Utils方法长度: " << utilsResult.length() << std::endl;
            std::cout << u8"  ProcessInfoManager方法长度: " << iconBase64Str.length() << std::endl;
        }
        
        // 测试4: 测试GetAllProcessesDetailed方法
        std::cout << u8"\n测试4: 测试 GetAllProcessesDetailed 方法" << std::endl;
        json processInfo = processManager.GetAllProcessesDetailed();
        
        if (processInfo.is_array() && !processInfo.empty()) {
            std::cout << u8"✓ GetAllProcessesDetailed 返回了进程数据" << std::endl;
            std::cout << u8"  进程条目数量: " << processInfo.size() << std::endl;
            
            // 检查前几个进程是否有图标数据
            int iconCount = 0;
            int checkCount = std::min(5, (int)processInfo.size());
            
            for (int i = 0; i < checkCount; i++) {
                if (processInfo[i].contains("icon") && 
                    processInfo[i]["icon"].is_string() && 
                    !processInfo[i]["icon"].get<std::string>().empty()) {
                    iconCount++;
                }
            }
            
            std::cout << u8"  前" << checkCount << u8"个进程中有图标的数量: " << iconCount << std::endl;
            
            if (iconCount > 0) {
                std::cout << u8"✓ 进程数据包含图标信息" << std::endl;
            } else {
                std::cout << u8"⚠ 前几个进程都没有图标信息" << std::endl;
            }
        } else {
            std::cout << u8"✗ GetAllProcessesDetailed 返回空数据或格式错误" << std::endl;
        }
        
        std::cout << u8"\n测试完成！" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << u8"测试过程中发生错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
