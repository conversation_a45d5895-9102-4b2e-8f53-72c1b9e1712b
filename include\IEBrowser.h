#pragma once
#include "BrowserDataExtractor.h"
#include <windows.h>
#include <wincred.h>
#include <shlobj.h>
#include <vector>
#include <string>

class IEBrowser : public IBrowser {
public:
    IEBrowser();
    virtual ~IEBrowser();

     std::vector<PasswordData> GetPasswords() override;
     std::vector<HistoryData> GetHistory() override;
     std::vector<DownloadData> GetDownloads() override;
     std::vector<CookieData> GetCookie() override;
     std::vector<BookmarkData> GetBookmarks() override;
     std::vector<CacheFileData> GetBroswerCache() override;
protected:
    virtual std::wstring GetProfilePath() override;

private:
    std::wstring m_profilePath;

    // ��ע�����ȡ��ʷ��¼
    std::vector<HistoryData> GetRegistryHistory();

    // ��ע�����ȡ���ؼ�¼
    std::vector<DownloadData> GetRegistryDownloads();
    std::vector<std::wstring> EnumerateCookieUrls();
    std::wstring GetCookiesForUrl(const std::wstring& url);

    // ��Windowsƾ�ݹ�������ȡ����
    std::vector<PasswordData> GetCredentialPasswords();

    // ��������
    std::wstring GetIEDownloadFolder();
    std::wstring GetHistoryPath();

    // ��TypedURLs��ȡ��ʷ��¼
    void GetTypedURLs(std::vector<HistoryData>& history);

    // ��ע�����ȡʱ���
    FILETIME GetLastVisitTime(const std::wstring& url);

    std::wstring GetUrlFromCache(const std::wstring& filePath);

    // 缓存相关的私有方法
    void ScanIECacheDirectory(const std::wstring& cachePath, std::vector<CacheFileData>& caches);
    void GetCacheInfoFromWinInet(std::vector<CacheFileData>& caches);
    std::wstring GetIEContentTypeFromExtension(const std::wstring& filePath);
    std::wstring AnalyzeIERiskLevel(const std::wstring& url, const std::wstring& filePath);
    std::vector<std::wstring> CheckIESensitiveKeywords(const std::wstring& url, const std::wstring& filePath);

    // 多用户支持方法
    std::vector<std::wstring> GetAllIEUserProfiles();
    void ScanUserIECacheDirectory(const std::wstring& userPath, const std::wstring& userName, std::vector<CacheFileData>& caches);
    std::wstring GetCurrentUserName();
    void GetUserPasswords(const std::wstring& userPath, const std::wstring& userName, std::vector<PasswordData>& passwords);
    void GetUserHistory(const std::wstring& userPath, const std::wstring& userName, std::vector<HistoryData>& history);
    void GetUserDownloads(const std::wstring& userPath, const std::wstring& userName, std::vector<DownloadData>& downloads);
    void GetUserCookies(const std::wstring& userPath, const std::wstring& userName, std::vector<CookieData>& cookies);
    void GetUserBookmarks(const std::wstring& userPath, const std::wstring& userName, std::vector<BookmarkData>& bookmarks);
};
