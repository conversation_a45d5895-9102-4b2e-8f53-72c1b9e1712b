﻿#pragma once

// 确保Windows XP兼容性
#ifndef _WIN32_WINNT
#define _WIN32_WINNT 0x0501  // Windows XP
#endif

// 防止Winsock重定义
#define WIN32_LEAN_AND_MEAN

// 包含必要的系统头文件（避免与其他模块冲突）
#include <windows.h>
#include <iphlpapi.h>
#include <tlhelp32.h>
#include <psapi.h>
#include <shellapi.h>

#include <string>
#include <vector>
#include <functional>
#include <nlohmann/json.hpp>
#include "NetworkConnectionData.h"
#include "Utils.h"

// 使用nlohmann/json命名空间
using json = nlohmann::json;



// 网络连接信息结构体
struct NetworkConnectionInfo {
    std::string localAddress;    // 本地地址
    DWORD localPort;            // 本地端口
    std::string remoteAddress;   // 远程地址
    DWORD remotePort;           // 远程端口
    DWORD state;                // 连接状态
    DWORD owningPid;            // 拥有进程ID
    std::string protocol;       // 协议类型 (TCP/UDP)
    std::string createTime;     // 网络连接创建时间
};

// 网络进程信息结构体
struct NetworkProcessInfo {
    DWORD pid;                  // 进程ID
    std::string processName;    // 进程名称
    std::string processPath;    // 进程完整路径
    std::string createTime;     // 进程创建时间
    std::string iconBase64;     // 进程图标Base64编码
    std::vector<NetworkConnectionInfo> connections; // 网络连接信息
};

// JSON序列化支持
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(NetworkConnectionInfo,
    localAddress, localPort, remoteAddress, remotePort, state, owningPid, protocol, createTime)

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(NetworkProcessInfo,
    pid, processName, processPath, createTime, iconBase64, connections)

class NetworkConnectionManager
{
public:
    // 构造函数
    NetworkConnectionManager() = default;
    
    // 析构函数
    ~NetworkConnectionManager() = default;

    // 获取所有网络连接信息
    std::vector<NetworkConnectionInfo> GetAllNetworkConnections();

    // 获取指定进程的网络连接
    std::vector<NetworkConnectionInfo> GetProcessConnections(DWORD pid);

    // 获取所有有网络连接的进程信息
    std::vector<NetworkProcessInfo> GetAllNetworkProcesses();

    // 获取指定进程名称的网络连接信息
    std::vector<NetworkProcessInfo> GetNetworkProcessesByName(const std::string& processName);

    // 将网络连接信息转换为JSON格式（扁平化格式，适合网络监控软件）
    json NetworkConnectionsToJson(const std::vector<NetworkProcessInfo>& processes);

    // 获取所有网络连接并转换为JSON字符串
    std::string GetAllNetworkConnectionsAsJsonString();

    // 获取活跃网络连接（仅包含已建立连接的TCP和所有UDP）
    json GetActiveNetworkConnections();

    // 获取网络连接统计信息
    json GetNetworkConnectionStats();

    // 根据端口查找进程
    std::vector<NetworkProcessInfo> FindProcessesByPort(DWORD port);

    // 根据远程地址查找进程
    std::vector<NetworkProcessInfo> FindProcessesByRemoteAddress(const std::string& remoteAddress);

    // ========== 统一接口方法 ==========

    // 获取所有网络连接数据（统一格式）
    std::vector<NetworkConnectionData> GetNetworkConnectionData();

    // 获取网络连接统计信息（统一格式）
    NetworkConnectionStats GetConnectionStats();

    // 获取网络连接数据并转换为JSON字符串（统一接口）
    std::string GetNetworkConnectionDataAsJsonString();

    // ========== 封装接口方法 ==========



private:
    // 获取TCP连接表
    std::vector<NetworkConnectionInfo> GetTcpConnections();

    // 获取UDP连接表
    std::vector<NetworkConnectionInfo> GetUdpConnections();

    // 获取进程信息
    NetworkProcessInfo GetProcessInfo(DWORD pid);

    // 获取进程路径
    std::string GetProcessPath(DWORD pid);

    // 获取进程创建时间
    std::string GetProcessCreateTime(DWORD pid);

    // 获取进程图标Base64编码
    std::string GetProcessIconBase64(const std::string& processPath);

    // 获取网络连接创建时间（基于进程创建时间的估算）
    std::string GetConnectionCreateTime(DWORD pid, const std::string& protocol,
                                       const std::string& localAddr, DWORD localPort);

    // IP地址网络字节序转换为字符串
    std::string IpAddressToString(DWORD ipAddress);

    // 获取TCP连接状态字符串
    std::string GetTcpStateString(DWORD state);

    // 注意：字符串转换函数已移至Utils类中
    // 使用 Utils::WStringToUTF8() 和 Utils::UTF8ToWString() 替代
};

// TCP连接状态常量
namespace TcpState {
    const DWORD CLOSED = 1;
    const DWORD LISTEN = 2;
    const DWORD SYN_SENT = 3;
    const DWORD SYN_RCVD = 4;
    const DWORD ESTABLISHED = 5;
    const DWORD FIN_WAIT1 = 6;
    const DWORD FIN_WAIT2 = 7;
    const DWORD CLOSE_WAIT = 8;
    const DWORD CLOSING = 9;
    const DWORD LAST_ACK = 10;
    const DWORD TIME_WAIT = 11;
    const DWORD DELETE_TCB = 12;
}
