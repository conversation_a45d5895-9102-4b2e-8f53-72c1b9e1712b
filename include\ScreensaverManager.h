﻿#pragma once
#include "ScreensaverData.h"
#include <vector>
#include <string>
#include <functional>
#include <windows.h>
#include <nlohmann/json.hpp>



class ScreensaverManager {
public:
    ScreensaverManager();
    ~ScreensaverManager();

    // 初始化屏保管理器
    bool Initialize();

    // 清理资源
    void Cleanup();

    // 获取所有屏保程序
    std::vector<ScreensaverData> GetAllScreensavers();

    // 获取屏保统计信息
    ScreensaverStatistics GetScreensaverStatistics();

    // 获取完整的屏保信息并返回JSON格式
    nlohmann::json GetScreensaversInfoAsJson();

    // 保存屏保信息到JSON文件
    bool SaveScreensaversInfoToFile(const std::string& filename);

    
private:
    bool m_initialized;
    std::chrono::system_clock::time_point m_startTime;

    // 辅助函数
    std::string ConvertToString(const std::wstring& wstr);
    std::wstring ConvertToWString(const std::string& str);

    // 屏保发现方法
    std::vector<ScreensaverData> ScanSystemScreensavers();
    std::vector<ScreensaverData> ScanRegistryScreensavers();
    std::vector<ScreensaverData> ScanDirectoryScreensavers(const std::string& directory);

    // 屏保信息获取
    bool GetScreensaverDetails(const std::string& filePath, ScreensaverData& data);
    bool GetCurrentScreensaverSettings(ScreensaverData& activeScreensaver);
    
    // 注册表操作
    std::string ReadRegistryString(HKEY hKey, const std::string& subKey, const std::string& valueName);
    DWORD ReadRegistryDWORD(HKEY hKey, const std::string& subKey, const std::string& valueName);
    bool ReadRegistryBool(HKEY hKey, const std::string& subKey, const std::string& valueName);

    // 文件信息获取
    std::string GetFileVersion(const std::string& filePath);
    std::string GetFileManufacturer(const std::string& filePath);
    std::string GetFileDescription(const std::string& filePath);
    std::string GetFileSize(const std::string& filePath);
    std::string GetFileCreationTime(const std::string& filePath);
    std::string GetFileModificationTime(const std::string& filePath);
    bool IsFileSigned(const std::string& filePath);
    std::string GetFileSignatureInfo(const std::string& filePath);

    // 屏保分类
    std::string ClassifyScreensaver(const std::string& filePath, const std::string& manufacturer);
    bool IsSystemScreensaver(const std::string& filePath);

    // 路径处理
    std::string ExpandEnvironmentPath(const std::string& path);
    std::string NormalizePath(const std::string& path);
    bool FileExists(const std::string& filePath);

    // 屏保配置解析
    std::string ParseScreensaverName(const std::string& filePath);
    std::string GetScreensaverDisplayName(const std::string& filePath);
    bool HasConfigurationDialog(const std::string& filePath);

    // 屏保配置获取
    bool GetScreensaverConfiguration(const std::string& filePath, ScreensaverData& data);
    std::string GetScreensaverCommandLine(const std::string& filePath);
    std::string GetScreensaverRegistryKey(const std::string& filePath);

    // 屏保功能检测
    bool CanPreview(const std::string& filePath);
    bool IsScreensaverRunning();
    std::string GetCurrentRunningScreensaver();

    // 屏保控制功能
    bool StartScreensaver(const std::string& filePath);
    bool StopScreensaver();
    bool PreviewScreensaver(const std::string& filePath, HWND parentWindow);
    bool ConfigureScreensaver(const std::string& filePath, HWND parentWindow);

    // 屏保安全和权限检查
    bool CheckScreensaverPermissions(const std::string& filePath);
    std::string GetScreensaverSecurityInfo(const std::string& filePath);
    bool IsScreensaverTrusted(const std::string& filePath);

    // 屏保性能分析
    DWORD GetScreensaverMemoryUsage(const std::string& filePath);
    double GetScreensaverCpuUsage(const std::string& filePath);
    std::string GetScreensaverPerformanceInfo(const std::string& filePath);

    // 屏保历史和使用统计
    std::vector<std::string> GetScreensaverHistory();
    DWORD GetScreensaverUsageCount(const std::string& filePath);
    std::string GetLastScreensaverActivation();

    // 高级屏保信息
    std::vector<std::string> GetScreensaverDependencies(const std::string& filePath);
    std::string GetScreensaverArchitecture(const std::string& filePath);
    bool IsScreensaverCompatible(const std::string& filePath);
    std::string GetScreensaverCompatibilityInfo(const std::string& filePath);

    // 系统目录获取
    std::vector<std::string> GetSystemDirectories();
    std::vector<std::string> GetCommonScreensaverDirectories();

    // 错误处理
    std::string GetLastErrorString();
};
