﻿#pragma once
#include "DriverData.h"
#include <vector>
#include <string>
#include <functional>
#include <memory>
#include <chrono>
#include <windows.h>
#include <psapi.h>
#include <nlohmann/json.hpp>



// 驱动程序信息缓存结构
struct DriverCache {
    std::vector<DriverData> drivers;
    DriverStatistics statistics;
    std::chrono::system_clock::time_point last_update;
    bool is_valid;

    DriverCache() : is_valid(false) {}
};

class DriverManager {
public:
    DriverManager();
    ~DriverManager();

    // 初始化驱动程序管理器
    bool Initialize();

    // 清理资源
    void Cleanup();

    // 获取所有Windows驱动程序（改进版本）
    std::vector<DriverData> GetAllDrivers();

    // 获取驱动程序统计信息
    DriverStatistics GetDriverStatistics();

    // 获取完整的驱动程序信息并返回JSON格式
    nlohmann::json GetDriversInfoAsJson();

    // 保存驱动程序信息到JSON文件
    bool SaveDriversInfoToFile(const std::string& filename);

    // 刷新驱动程序缓存
    bool RefreshDriverCache();

 

private:
    SC_HANDLE m_scManager;
    bool m_initialized;
    DriverCache m_cache;
    std::chrono::system_clock::time_point m_startTime;

    // 核心枚举方法
    std::vector<DriverData> EnumerateServiceDrivers();
    std::vector<DriverData> EnumerateDeviceDrivers();
    std::vector<DriverData> EnumerateSystemDrivers();

    // 驱动程序信息获取
    bool GetDriverInfo(const std::string& driverName, DriverData& driverData);
    bool GetServiceDriverInfo(const std::string& serviceName, DriverData& driverData);
    bool GetDeviceDriverInfo(LPVOID baseAddress, DriverData& driverData);

    // 文件信息获取（优化版本）
    bool GetFileInformation(const std::string& filePath, DriverData& driverData);
    std::string GetFileVersion(const std::string& filePath);
    std::string GetFileManufacturer(const std::string& filePath);
    std::string GetFileDate(const std::string& filePath);
    std::string GetFileSize(const std::string& filePath);
    bool GetFileSignatureInfo(const std::string& filePath, std::string& signer, bool& isSigned);

    // 添加缺失的方法声明
    bool IsFileSigned(const std::string& filePath);
    std::string GetFileCreationTime(const std::string& filePath);
    std::string GetFileModificationTime(const std::string& filePath);
    std::string GetDriverDescription(SC_HANDLE serviceHandle);
    bool GetDriverConfig(SC_HANDLE serviceHandle, DriverData& driverData);
    bool IsDriverService(DWORD serviceType);

    // 路径转换函数
    std::string ConvertToAbsolutePath(const std::string& path);

    // 驱动程序分类
    std::string ClassifyDriver(const std::string& driverName, const std::string& filePath);
    bool IsSystemDriver(const std::string& manufacturer, const std::string& signer);

    // 内存和地址信息（真实实现）
    std::string GetDriverLoadAddress(LPVOID baseAddress);
    std::string GetDriverMemoryUsage(LPVOID baseAddress);

    // 注意：字符串转换函数已移至Utils类中
    // 使用 Utils::WStringToUTF8() 替代 ConvertToString()
    // 使用 Utils::UTF8ToWString() 替代 ConvertToWString()
    std::string GetStatusString(DWORD status);
    std::string GetStartupTypeString(DWORD startType);
    std::string FormatFileSize(DWORD64 size);
    std::string FormatAddress(LPVOID address);
    std::string GetCurrentTimeString();
    std::string GetDurationString(const std::chrono::system_clock::time_point& start);

    // 错误处理
    std::string GetLastErrorString();
    void LogError(const std::string& operation, const std::string& details);
};
