#pragma once
#include <string>
#include <vector>
#include <windows.h>
#include <fstream>
#include <iostream>
#include "BrowserDataExtractor.h"

class Utils {
public:
    static std::wstring ConvertChromeTimestamp(__int64 timestamp);
    static std::wstring ConvertFirefoxTimestamp(__int64 timestamp);
    static std::vector<BYTE> Base64Decode(const std::string& input);
    static std::wstring UTF8ToWString(const std::string& str);
    static std::string WStringToUTF8(const std::wstring& wstr);

    // AES-GCM������غ���
    static std::vector<BYTE> AesGcmDecrypt(
        const std::vector<BYTE>& key,
        const std::vector<BYTE>& iv,
        const std::vector<BYTE>& cipher_text,
        const std::vector<BYTE>& auth_tag);

    // AES-GCM解密函数（支持关联数据）
    static std::vector<BYTE> AesGcmDecryptWithAAD(
        const std::vector<BYTE>& key,
        const std::vector<BYTE>& iv,
        const std::vector<BYTE>& cipher_text,
        const std::vector<BYTE>& auth_tag,
        const std::vector<BYTE>& aad);

    // AES-CBC解密函数
    static std::vector<BYTE> AesCbcDecrypt(
        const std::vector<BYTE>& key,
        const std::vector<BYTE>& iv,
        const std::vector<BYTE>& cipher_text);

    // 尝试DPAPI解密（作为备用）
    static std::vector<BYTE> DpapiDecrypt(const std::vector<BYTE>& encrypted_data);

    // 文件系统辅助函数
    static bool FileExists(const std::wstring& filePath);

    // 文件图标提取函数
    // 功能：为浏览器下载历史记录中的文件获取对应的系统图标
    // 输入：文件路径（file_path字段）
    // 输出：纯Base64编码字符串（32x32像素BMP格式，支持透明度）
    static std::wstring GetFileIconAsBase64(const std::wstring& filePath, int iconSize = 32);
    static std::vector<BYTE> ExtractIconToBMP(HICON hIcon, int size);
    static std::string Base64Encode(const std::vector<BYTE>& data);

    // CSV导出函数
    static bool ExportPasswordsToCSV(const std::vector<PasswordData>& passwords, const std::wstring& filename);
    static bool ExportHistoryToCSV(const std::vector<HistoryData>& history, const std::wstring& filename);
    static bool ExportDownloadsToCSV(const std::vector<DownloadData>& downloads, const std::wstring& filename);
    static bool ExportCookieToCSV(const std::vector<CookieData>& passwords, const std::wstring& filename);

private:
    static std::string EscapeCSV(const std::wstring& str);
    static void WriteUTF8BOM(std::ofstream& file);
};

