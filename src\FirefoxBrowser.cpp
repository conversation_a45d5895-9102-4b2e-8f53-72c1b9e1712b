#define WINVER 0x0501
#define _WIN32_WINNT 0x0501
#define _WIN32_IE 0x0600
#define NTDDI_VERSION 0x05010000

#include "pch.h"
#include "FirefoxBrowser.h"
#include "Utils.h"
#include <filesystem>
#include <fstream>
#include "sqlite3.h"
#include <windows.h>
#include <shlobj.h>
#include <iostream>
#include <vector>
#include <string>
#include <wincrypt.h>
#include <nlohmann/json.hpp>


namespace fs = std::filesystem;

// 定义支持的Firefox版本配置
const std::vector<std::wstring> FirefoxBrowser::firefox_versions = {
    L"Mozilla\\Firefox",
    L"Mozilla Firefox",
    L"Firefox",
    L"Mozilla\\Firefox Beta",
    L"Mozilla\\Firefox Developer Edition",
    L"Mozilla\\Firefox Nightly",
    L"Mozilla\\Firefox ESR"
};

FirefoxBrowser::FirefoxBrowser() : m_nssModule(nullptr), m_pSetDllDirectoryW(nullptr) {
    // 动态获取SetDllDirectoryW函数
    HMODULE hKernel32 = GetModuleHandleW(L"kernel32.dll");
    if (hKernel32) {
        m_pSetDllDirectoryW = reinterpret_cast<SetDllDirectoryWPtr>(GetProcAddress(hKernel32, "SetDllDirectoryW"));
    }

    WCHAR appData[MAX_PATH];

    // 获取%APPDATA%路径（Firefox标准配置文件位置）
    if (SUCCEEDED(SHGetFolderPathW(NULL, CSIDL_APPDATA, NULL, 0, appData))) {

        printf("Searching for Firefox profiles...\n");
        printf("APPDATA path: %ls\n", appData);

        // 遍历所有Firefox版本
        for (const auto& version : firefox_versions) {
            // 检查Firefox标准配置文件路径
            fs::path profilesPath = fs::path(appData) / version / L"Profiles";
            printf("Checking Firefox profiles path: %ls\n", profilesPath.c_str());

            if (!fs::exists(profilesPath)) {
                printf("Profiles path not found for %ls, continuing to next version\n", version.c_str());
                continue;
            }

            try {
                printf("Found Profiles folder: %ls\n", profilesPath.c_str());
                // 查找所有配置文件
                for (const auto& entry : fs::directory_iterator(profilesPath)) {
                    if (entry.is_directory()) {
                        std::wstring profileName = entry.path().filename().wstring();
                        printf("Found profile folder: %ls\n", profileName.c_str());

                        // 检查是否是有效的配置文件夹
                        fs::path loginsPath = entry.path() / L"logins.json";
                        fs::path key4Path = entry.path() / L"key4.db";
                        fs::path placesPath = entry.path() / L"places.sqlite";


                        bool hasLoginJson = fs::exists(loginsPath);
                        bool hasKey4Db = fs::exists(key4Path);
                        bool hasPlacesSqlite = fs::exists(placesPath);

                        printf("Checking key files in profile %ls:\n", profileName.c_str());
                        printf("logins.json: %ls\n", (hasLoginJson ? L"exists" : L"not exists"));
                        printf("key4.db: %ls\n", (hasKey4Db ? L"exists" : L"not exists"));
                        printf("places.sqlite: %ls\n", (hasPlacesSqlite ? L"exists" : L"not exists"));

                        // 如果包含所需的文件,就使用这个配置文件夹
                        if (hasLoginJson || hasKey4Db || hasPlacesSqlite) {
                            m_profilePath = entry.path().wstring();
                            printf("Using Firefox profile: %ls\n", m_profilePath.c_str());
                            return;
                        }
                    }
                }
            }
            catch (const std::exception& e) {
                printf("Error while searching for Firefox profiles: %s\n", e.what());
            }
        }
    }

    if (m_profilePath.empty()) {
        printf("No Firefox profiles found\n");
    }
}

FirefoxBrowser::~FirefoxBrowser() {
    if (m_nssModule) {
        if (NSS_ShutdownPtr) {
            NSS_ShutdownPtr();
        }
        FreeLibrary(m_nssModule);
    }
}

std::wstring FirefoxBrowser::GetProfilePath() {
    return m_profilePath;
}

std::optional<std::filesystem::path> FirefoxBrowser::FindNssLibrary() {
    // 获取Firefox安装路径
    WCHAR programFiles[MAX_PATH];
    if (FAILED(SHGetFolderPathW(NULL, CSIDL_PROGRAM_FILES, NULL, 0, programFiles))) {
        return std::nullopt;
    }

    std::vector<fs::path> searchPaths;

    // 获取系统目录
    WCHAR programFilesX86[MAX_PATH];
    WCHAR localAppData[MAX_PATH];

    if (SUCCEEDED(SHGetFolderPathW(NULL, CSIDL_PROGRAM_FILES, NULL, 0, programFiles))) {
        searchPaths.push_back(fs::path(programFiles));
    }

    if (SUCCEEDED(SHGetFolderPathW(NULL, CSIDL_PROGRAM_FILESX86, NULL, 0, programFilesX86))) {
        searchPaths.push_back(fs::path(programFilesX86));
    }

    if (SUCCEEDED(SHGetFolderPathW(NULL, CSIDL_LOCAL_APPDATA, NULL, 0, localAppData))) {
        searchPaths.push_back(fs::path(localAppData));
    }

    // Firefox可能的安装目录
    std::vector<std::wstring> firefoxDirs = {
        L"Mozilla Firefox",
        L"Firefox",
        L"Firefox Developer Edition",
        L"Firefox Nightly",
        L"Mozilla\\Firefox",
        L"Mozilla\\Firefox Beta",
        L"Mozilla\\Firefox Developer Edition",
        L"Mozilla\\Firefox Nightly",
        L"Mozilla\\Firefox ESR"
    };

    // 遍历所有可能的路径
    for (const auto& basePath : searchPaths) {
        for (const auto& firefoxDir : firefoxDirs) {
            fs::path firefoxPath = basePath / firefoxDir;
            if (!fs::exists(firefoxPath)) {
                continue;
            }

            printf("Found Firefox installation: %ls\n", firefoxPath.c_str());

            // 设置DLL搜索路径
            if (m_pSetDllDirectoryW) {
                if (!m_pSetDllDirectoryW(firefoxPath.c_str())) {
                    printf("Failed to set DLL directory: %ls\n", firefoxPath.c_str());
                    continue;
                }
            }

            // 尝试加载nss3.dll
            m_nssModule = LoadLibraryW(L"nss3.dll");
            if (m_nssModule) {
                printf("Successfully loaded nss3.dll from: %ls\n", firefoxPath.c_str());

                // 重置DLL搜索路径
                if (m_pSetDllDirectoryW) {
                    if (!m_pSetDllDirectoryW(NULL)) {
                        printf("Warning: Failed to reset DLL search path. Error code: %lu\n", GetLastError());
                    }
                }

                return firefoxPath;
            }

            // 重置DLL搜索路径
            if (m_pSetDllDirectoryW) {
                if (!m_pSetDllDirectoryW(NULL)) {
                    printf("Warning: Failed to reset DLL search path. Error code: %lu\n", GetLastError());
                }
            }
        }
    }

    // 如果在所有常规路径都没找到，尝试直接在默认安装路径查找
    fs::path defaultFirefoxPath = L"Mozilla Firefox";
    if (fs::exists(defaultFirefoxPath)) {
        printf("Trying default Firefox installation path: %ls\n", defaultFirefoxPath.c_str());

        // 设置DLL搜索路径
        if (m_pSetDllDirectoryW) {
            if (!m_pSetDllDirectoryW(defaultFirefoxPath.c_str())) {
                printf("Failed to set DLL directory for default path: %ls (Error: %lu)\n",
                    defaultFirefoxPath.c_str(), GetLastError());
            }
            else {
                // 尝试加载nss3.dll
                m_nssModule = LoadLibraryW(L"nss3.dll");
                if (m_nssModule) {
                    printf("Successfully loaded nss3.dll from default path: %ls\n", defaultFirefoxPath.c_str());

                    // 重置DLL搜索路径
                    if (m_pSetDllDirectoryW) {
                        m_pSetDllDirectoryW(NULL);
                    }

                    return defaultFirefoxPath;
                }
                else {
                    DWORD error = GetLastError();
                    printf("Failed to load nss3.dll from default path. Error: %lu\n", error);

                    // 尝试直接通过完整路径加载
                    fs::path fullDllPath = defaultFirefoxPath / L"nss3.dll";
                    if (fs::exists(fullDllPath)) {
                        printf("nss3.dll exists at %ls, trying to load directly...\n", fullDllPath.c_str());
                        m_nssModule = LoadLibraryW(fullDllPath.c_str());
                        if (m_nssModule) {
                            printf("Successfully loaded nss3.dll with full path\n");
                            return defaultFirefoxPath;
                        }
                        else {
                            error = GetLastError();
                            printf("Failed to load nss3.dll with full path. Error: %lu\n", error);
                        }
                    }
                    else {
                        printf("nss3.dll does not exist at %ls\n", fullDllPath.c_str());
                    }
                }

                // 重置DLL搜索路径
                if (m_pSetDllDirectoryW) {
                    m_pSetDllDirectoryW(NULL);
                }
            }
        }
        else {
            // 尝试直接通过完整路径加载
            fs::path fullDllPath = defaultFirefoxPath / L"nss3.dll";
            if (fs::exists(fullDllPath)) {
                printf("SetDllDirectoryW not available, trying to load nss3.dll directly: %ls\n",
                    fullDllPath.c_str());
                m_nssModule = LoadLibraryW(fullDllPath.c_str());
                if (m_nssModule) {
                    printf("Successfully loaded nss3.dll with full path\n");
                    return defaultFirefoxPath;
                }
                else {
                    DWORD error = GetLastError();
                    printf("Failed to load nss3.dll with full path. Error: %lu\n", error);
                }
            }
            else {
                printf("nss3.dll does not exist at %ls\n", fullDllPath.c_str());
            }
        }
    }
    else {
        printf("Default Firefox installation path does not exist: %ls\n", defaultFirefoxPath.c_str());
    }

    printf("Could not find nss3.dll in any Firefox installation\n");
    return std::nullopt;
}

bool FirefoxBrowser::InitializeNss(const std::filesystem::path& profile_path) {
    if (!m_nssModule) {
        auto nssPath = FindNssLibrary();
        if (!nssPath) {
            printf("NSS library not found\n");
            return false;
        }

        printf("Loading NSS library from: %ls\n", nssPath->c_str());

        // 设置DLL搜索路径
        if (m_pSetDllDirectoryW) {
            if (!m_pSetDllDirectoryW(nssPath->parent_path().c_str())) {
                printf("Failed to set DLL directory: %ls\n", nssPath->parent_path().c_str());
                return false;
            }
        }

        // 加载NSS库
        m_nssModule = LoadLibraryW(L"nss3.dll");

        // 重置DLL搜索路径
        if (m_pSetDllDirectoryW) {
            if (!m_pSetDllDirectoryW(NULL)) {
                printf("Warning: Failed to reset DLL search path. Error code: %lu\n", GetLastError());
            }
        }

        if (!m_nssModule) {
            DWORD error = GetLastError();
            printf("Failed to load NSS library. Error: %lu\n", error);

            // 获取详细错误信息
            LPWSTR messageBuffer = nullptr;
            FormatMessageW(
                FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
                NULL, error, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
                (LPWSTR)&messageBuffer, 0, NULL);

            if (messageBuffer) {
                printf("Error message: %ls\n", messageBuffer);
                LocalFree(messageBuffer);
            }
            return false;
        }

        // 获取NSS函数
        printf("Getting NSS functions...\n");
        NSS_InitializePtr = std::function<NSS_Initialize>(reinterpret_cast<NSS_Initialize*>(
            GetProcAddress(m_nssModule, "NSS_Initialize")));
        NSS_ShutdownPtr = std::function<NSS_Shutdown>(reinterpret_cast<NSS_Shutdown*>(
            GetProcAddress(m_nssModule, "NSS_Shutdown")));
        SECITEM_AllocItemPtr = std::function<SECITEM_AllocItem>(reinterpret_cast<SECITEM_AllocItem*>(
            GetProcAddress(m_nssModule, "SECITEM_AllocItem")));
        SECITEM_ZfreeItemPtr = std::function<SECITEM_ZfreeItem>(reinterpret_cast<SECITEM_ZfreeItem*>(
            GetProcAddress(m_nssModule, "SECITEM_ZfreeItem")));
        PK11SDR_DecryptPtr = std::function<PK11SDR_Decrypt>(reinterpret_cast<PK11SDR_Decrypt*>(
            GetProcAddress(m_nssModule, "PK11SDR_Decrypt")));

        if (!NSS_InitializePtr || !NSS_ShutdownPtr || !SECITEM_AllocItemPtr ||
            !SECITEM_ZfreeItemPtr || !PK11SDR_DecryptPtr) {
            printf("Failed to get NSS functions:\n");
            printf("NSS_Initialize: %p\n", NSS_InitializePtr.target<NSS_Initialize>());
            printf("NSS_Shutdown: %p\n", NSS_ShutdownPtr.target<NSS_Shutdown>());
            printf("SECITEM_AllocItem: %p\n", SECITEM_AllocItemPtr.target<SECITEM_AllocItem>());
            printf("SECITEM_ZfreeItem: %p\n", SECITEM_ZfreeItemPtr.target<SECITEM_ZfreeItem>());
            printf("PK11SDR_Decrypt: %p\n", PK11SDR_DecryptPtr.target<PK11SDR_Decrypt>());
            FreeLibrary(m_nssModule);
            m_nssModule = nullptr;
            return false;
        }
    }

    printf("Initializing NSS with profile: %ls\n", profile_path.c_str());

    // 初始化NSS
    if (NSS_InitializePtr(profile_path.string().c_str(), "", "", SECMOD_DB, NSS_INIT_READONLY) != SECSuccess) {
        printf("NSS_Initialize failed\n");
        return false;
    }

    printf("NSS initialized successfully\n");
    return true;
}

std::string FirefoxBrowser::DecryptData(const std::string& ciphertext_b64) {
    std::vector<BYTE> ciphertext = Utils::Base64Decode(ciphertext_b64);
    if (ciphertext.empty()) {
        return "";
    }

    SECItem in;
    in.type = siBuffer;
    in.data = ciphertext.data();
    in.len = (unsigned int)ciphertext.size();

    SECItem* out = SECITEM_AllocItemPtr(nullptr, nullptr, 0);
    if (!out) {
        return "";
    }

    if (PK11SDR_DecryptPtr(&in, out, nullptr) != SECSuccess) {
        SECITEM_ZfreeItemPtr(out, PR_TRUE);
        return "";
    }

    std::string result(reinterpret_cast<char*>(out->data), out->len);
    SECITEM_ZfreeItemPtr(out, PR_TRUE);
    return result;
}

std::vector<PasswordData> FirefoxBrowser::GetPasswords() {
    std::vector<PasswordData> passwords;

    printf("Starting Firefox password extraction for all users...\n");

    try {
        // 获取所有用户配置文件路径
        std::vector<std::wstring> userProfiles = GetAllFirefoxUserProfiles();

        if (userProfiles.empty()) {
            printf("No user profiles found, falling back to current user\n");
            // 回退到当前用户
            if (!m_profilePath.empty()) {
                // 获取当前用户名
                wchar_t username[256];
                DWORD usernameLen = sizeof(username) / sizeof(username[0]);
                GetUserNameW(username, &usernameLen);
                GetUserPasswords(m_profilePath, username, passwords);
            }
        } else {
            // 扫描所有用户的Firefox密码
            for (const auto& userPath : userProfiles) {
                std::wstring userName = fs::path(userPath).filename().wstring();
                printf("Scanning Firefox passwords for user: %ls\n", userName.c_str());
                GetUserPasswords(userPath, userName, passwords);
            }
        }

        printf("Successfully retrieved %zu Firefox password entries from all users\n", passwords.size());
    }
    catch (const std::exception& e) {
        printf("Error getting Firefox passwords: %s\n", e.what());
    }
    catch (...) {
        printf("Unknown error occurred while getting Firefox passwords\n");
    }

    return passwords;
}

std::vector<HistoryData> FirefoxBrowser::GetHistory() {
    std::vector<HistoryData> history;

    printf("Starting Firefox history extraction for all users...\n");

    try {
        // 获取所有用户配置文件路径
        std::vector<std::wstring> userProfiles = GetAllFirefoxUserProfiles();

        if (userProfiles.empty()) {
            printf("No user profiles found, falling back to current user\n");
            // 回退到当前用户
            if (!m_profilePath.empty()) {
                // 获取当前用户名
                wchar_t username[256];
                DWORD usernameLen = sizeof(username) / sizeof(username[0]);
                GetUserNameW(username, &usernameLen);
                GetUserHistory(m_profilePath, username, history);
            }
        } else {
            // 扫描所有用户的Firefox历史记录
            for (const auto& userPath : userProfiles) {
                std::wstring userName = fs::path(userPath).filename().wstring();
                printf("Scanning Firefox history for user: %ls\n", userName.c_str());
                GetUserHistory(userPath, userName, history);
            }
        }

        printf("Successfully retrieved %zu Firefox history entries from all users\n", history.size());
    }
    catch (const std::exception& e) {
        printf("Error getting Firefox history: %s\n", e.what());
    }
    catch (...) {
        printf("Unknown error occurred while getting Firefox history\n");
    }

    return history;
}

std::vector<DownloadData> FirefoxBrowser::GetDownloads() {
    std::vector<DownloadData> downloads;

    printf("Starting Firefox downloads extraction for all users...\n");

    try {
        // 获取所有用户配置文件路径
        std::vector<std::wstring> userProfiles = GetAllFirefoxUserProfiles();

        if (userProfiles.empty()) {
            printf("No user profiles found, falling back to current user\n");
            // 回退到当前用户
            if (!m_profilePath.empty()) {
                // 获取当前用户名
                wchar_t username[256];
                DWORD usernameLen = sizeof(username) / sizeof(username[0]);
                GetUserNameW(username, &usernameLen);
                GetUserDownloads(m_profilePath, username, downloads);
            }
        } else {
            // 扫描所有用户的Firefox下载记录
            for (const auto& userPath : userProfiles) {
                std::wstring userName = fs::path(userPath).filename().wstring();
                printf("Scanning Firefox downloads for user: %ls\n", userName.c_str());
                GetUserDownloads(userPath, userName, downloads);
            }
        }

        printf("Successfully retrieved %zu Firefox download entries from all users\n", downloads.size());
    }
    catch (const std::exception& e) {
        printf("Error getting Firefox downloads: %s\n", e.what());
    }
    catch (...) {
        printf("Unknown error occurred while getting Firefox downloads\n");
    }

    return downloads;
}


std::vector<CookieData> FirefoxBrowser::GetCookie()
{
    std::vector<CookieData> cookies;

    printf("Starting Firefox cookies extraction for all users...\n");

    try {
        // 获取所有用户配置文件路径
        std::vector<std::wstring> userProfiles = GetAllFirefoxUserProfiles();

        if (userProfiles.empty()) {
            printf("No user profiles found, falling back to current user\n");
            // 回退到当前用户
            if (!m_profilePath.empty()) {
                // 获取当前用户名
                wchar_t username[256];
                DWORD usernameLen = sizeof(username) / sizeof(username[0]);
                GetUserNameW(username, &usernameLen);
                GetUserCookies(m_profilePath, username, cookies);
            }
        } else {
            // 扫描所有用户的Firefox Cookie
            for (const auto& userPath : userProfiles) {
                std::wstring userName = fs::path(userPath).filename().wstring();
                printf("Scanning Firefox cookies for user: %ls\n", userName.c_str());
                GetUserCookies(userPath, userName, cookies);
            }
        }

        printf("Successfully retrieved %zu Firefox cookie entries from all users\n", cookies.size());
    }
    catch (const std::exception& e) {
        printf("Error getting Firefox cookies: %s\n", e.what());
    }
    catch (...) {
        printf("Unknown error occurred while getting Firefox cookies\n");
    }

    return cookies;
}

std::vector<BookmarkData> FirefoxBrowser::GetBookmarks()
{
    std::vector<BookmarkData> bookmarks;

    printf("Starting Firefox bookmarks extraction for all users...\n");

    try {
        // 获取所有用户配置文件路径
        std::vector<std::wstring> userProfiles = GetAllFirefoxUserProfiles();

        if (userProfiles.empty()) {
            printf("No user profiles found, falling back to current user\n");
            // 回退到当前用户
            if (!m_profilePath.empty()) {
                // 获取当前用户名
                wchar_t username[256];
                DWORD usernameLen = sizeof(username) / sizeof(username[0]);
                GetUserNameW(username, &usernameLen);
                GetUserBookmarks(m_profilePath, username, bookmarks);
            }
        } else {
            // 扫描所有用户的Firefox书签
            for (const auto& userPath : userProfiles) {
                std::wstring userName = fs::path(userPath).filename().wstring();
                printf("Scanning Firefox bookmarks for user: %ls\n", userName.c_str());
                GetUserBookmarks(userPath, userName, bookmarks);
            }
        }

        printf("Successfully retrieved %zu Firefox bookmark entries from all users\n", bookmarks.size());
    }
    catch (const std::exception& e) {
        printf("Error getting Firefox bookmarks: %s\n", e.what());
    }
    catch (...) {
        printf("Unknown error occurred while getting Firefox bookmarks\n");
    }

    return bookmarks;
}


std::vector<CacheFileData> FirefoxBrowser::GetBroswerCache()
{
    std::vector<CacheFileData> caches;

    printf("Starting Firefox cache file extraction for all users...\n");

    try {
        // 获取所有用户的Firefox配置文件路径
        std::vector<std::wstring> userProfiles = GetAllFirefoxUserProfiles();

        if (userProfiles.empty()) {
            printf("No Firefox user profiles found, falling back to current user\n");
            // 回退到当前用户
            if (!m_profilePath.empty()) {
                std::wstring cacheBasePath = m_profilePath + L"\\cache2\\entries";
                if (fs::exists(cacheBasePath)) {
                    ScanFirefoxCacheDirectory(cacheBasePath, caches);
                } else {
                    std::wstring altCachePath = m_profilePath + L"\\cache\\";
                    if (fs::exists(altCachePath)) {
                        ScanFirefoxCacheDirectory(altCachePath, caches);
                    }
                }
            }
        } else {
            // 扫描所有用户的Firefox缓存
            for (const auto& userPath : userProfiles) {
                std::wstring userName = fs::path(userPath).filename().wstring();
                printf("Scanning Firefox cache for user: %ls\n", userName.c_str());
                ScanUserFirefoxCacheDirectory(userPath, userName, caches);
            }
        }

        printf("Successfully retrieved %zu Firefox cache entries from all users\n", caches.size());
    }
    catch (const std::exception& e) {
        printf("Error getting Firefox cache: %s\n", e.what());
    }
    catch (...) {
        printf("Unknown error occurred while getting Firefox cache\n");
    }

    return caches;
}

// 扫描Firefox缓存目录获取缓存文件信息
void FirefoxBrowser::ScanFirefoxCacheDirectory(const std::wstring& cachePath, std::vector<CacheFileData>& caches) {
    printf("Starting Firefox cache directory scan: %ls\n", cachePath.c_str());

    try {
        // 获取当前用户名
        wchar_t username[256];
        DWORD usernameLen = sizeof(username) / sizeof(username[0]);
        GetUserNameW(username, &usernameLen);

        int fileCount = 0;
        const int maxFiles = 800; // Firefox缓存文件通常较多，适当增加限制

        // 递归遍历缓存目录
        for (const auto& entry : fs::recursive_directory_iterator(cachePath)) {
            if (fileCount >= maxFiles) {
                printf("Reached maximum file scan limit (%d), stopping scan\n", maxFiles);
                break;
            }

            if (entry.is_regular_file()) {
                try {
                    std::wstring filePath = entry.path().wstring();
                    std::wstring fileName = entry.path().filename().wstring();

                    // Firefox缓存文件通常没有扩展名，跳过一些明显的系统文件
                    if (fileName.find(L"index") != std::wstring::npos ||
                        fileName.find(L"_CACHE_") != std::wstring::npos ||
                        entry.path().extension() == L".tmp" ||
                        entry.path().extension() == L".lock") {
                        continue;
                    }

                    CacheFileData cacheData;
                    cacheData.browser_type = L"Firefox";
                    cacheData.local_file_path = filePath;
                    cacheData.user_name = username;

                    // 获取文件时间信息
                    auto ftime = fs::last_write_time(entry.path());
                    auto sctp = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
                        ftime - fs::file_time_type::clock::now() + std::chrono::system_clock::now());
                    std::time_t cftime = std::chrono::system_clock::to_time_t(sctp);

                    wchar_t timeBuffer[100];
                    struct tm timeinfo;
                    if (localtime_s(&timeinfo, &cftime) == 0) {
                        wcsftime(timeBuffer, sizeof(timeBuffer) / sizeof(wchar_t), L"%Y-%m-%d %H:%M:%S", &timeinfo);
                        cacheData.last_modified_time = timeBuffer;
                        cacheData.create_time = timeBuffer;
                        cacheData.last_access_time = timeBuffer;
                    }

                    // 设置默认URL（Firefox缓存文件名通常是哈希值）
                    cacheData.url = L"Firefox cache: " + fileName;

                    // 尝试从文件名推测内容类型（Firefox缓存文件名通常是哈希）
                    cacheData.content_type = L"application/octet-stream";

                    // 分析风险级别
                    cacheData.risk_level = AnalyzeFirefoxRiskLevel(cacheData.url, filePath);

                    // 检查敏感关键字
                    cacheData.matched_keywords = CheckFirefoxSensitiveKeywords(cacheData.url, filePath);
                    cacheData.is_suspicious = !cacheData.matched_keywords.empty();

                    // 设置检查结论
                    if (cacheData.is_suspicious) {
                        cacheData.check_result = L"Sensitive content found";
                    } else {
                        cacheData.check_result = L"Normal cache file";
                    }

                    caches.push_back(cacheData);
                    fileCount++;

                    if (fileCount % 100 == 0) {
                        printf("Scanned %d Firefox cache files...\n", fileCount);
                    }
                }
                catch (const std::exception& e) {
                    printf("Error processing Firefox cache file: %s\n", e.what());
                    continue;
                }
            }
        }

        printf("Firefox cache directory scan completed, processed %d files\n", fileCount);
    }
    catch (const std::exception& e) {
        printf("Error scanning Firefox cache directory: %s\n", e.what());
    }
}

// Firefox缓存文件是二进制格式，包含完整的缓存信息
// 不需要通过数据库读取，直接从缓存文件中解析即可

// 根据URL推测内容类型
std::wstring FirefoxBrowser::GetContentTypeFromUrl(const std::wstring& url) {
    std::wstring urlLower = url;
    std::transform(urlLower.begin(), urlLower.end(), urlLower.begin(), ::towlower);

    if (urlLower.find(L".jpg") != std::wstring::npos || urlLower.find(L".jpeg") != std::wstring::npos) {
        return L"image/jpeg";
    } else if (urlLower.find(L".png") != std::wstring::npos) {
        return L"image/png";
    } else if (urlLower.find(L".gif") != std::wstring::npos) {
        return L"image/gif";
    } else if (urlLower.find(L".svg") != std::wstring::npos) {
        return L"image/svg+xml";
    } else if (urlLower.find(L".css") != std::wstring::npos) {
        return L"text/css";
    } else if (urlLower.find(L".js") != std::wstring::npos) {
        return L"application/javascript";
    } else if (urlLower.find(L".json") != std::wstring::npos) {
        return L"application/json";
    } else if (urlLower.find(L".xml") != std::wstring::npos) {
        return L"application/xml";
    } else if (urlLower.find(L".pdf") != std::wstring::npos) {
        return L"application/pdf";
    } else if (urlLower.find(L".mp4") != std::wstring::npos) {
        return L"video/mp4";
    } else if (urlLower.find(L".mp3") != std::wstring::npos) {
        return L"audio/mpeg";
    } else if (urlLower.find(L".zip") != std::wstring::npos) {
        return L"application/zip";
    } else if (urlLower.find(L".exe") != std::wstring::npos) {
        return L"application/x-msdownload";
    } else {
        return L"text/html";
    }
}

// 分析Firefox缓存的风险级别
std::wstring FirefoxBrowser::AnalyzeFirefoxRiskLevel(const std::wstring& url, const std::wstring& filePath) {
    std::wstring urlLower = url;
    std::wstring pathLower = filePath;
    std::transform(urlLower.begin(), urlLower.end(), urlLower.begin(), ::towlower);
    std::transform(pathLower.begin(), pathLower.end(), pathLower.begin(), ::towlower);

    // 高风险关键字
    std::vector<std::wstring> highRiskKeywords = {
        L"password", L"login", L"signin", L"admin", L"bank", L"credit", L"card",
        L"payment", L"paypal", L"alipay", L"wechat", L"qq", L"weibo",
        L"download", L".exe", L".bat", L".cmd", L".scr", L".vbs", L".msi"
    };

    // 中风险关键字
    std::vector<std::wstring> mediumRiskKeywords = {
        L"user", L"account", L"profile", L"personal", L"private",
        L"secure", L"auth", L"token", L"session", L"cookie", L"api"
    };

    // 检查高风险
    for (const auto& keyword : highRiskKeywords) {
        if (urlLower.find(keyword) != std::wstring::npos ||
            pathLower.find(keyword) != std::wstring::npos) {
            return L"High";
        }
    }

    // 检查中风险
    for (const auto& keyword : mediumRiskKeywords) {
        if (urlLower.find(keyword) != std::wstring::npos ||
            pathLower.find(keyword) != std::wstring::npos) {
            return L"Medium";
        }
    }

    return L"Low";
}

// 检查Firefox缓存的敏感关键字
std::vector<std::wstring> FirefoxBrowser::CheckFirefoxSensitiveKeywords(const std::wstring& url, const std::wstring& filePath) {
    std::vector<std::wstring> matchedKeywords;
    std::wstring urlLower = url;
    std::wstring pathLower = filePath;
    std::transform(urlLower.begin(), urlLower.end(), urlLower.begin(), ::towlower);
    std::transform(pathLower.begin(), pathLower.end(), pathLower.begin(), ::towlower);

    // 敏感关键字列表
    std::vector<std::wstring> sensitiveKeywords = {
        L"password", L"login", L"signin", L"admin", L"bank", L"credit", L"payment",
        L"paypal", L"alipay", L"wechat", L"qq", L"weibo", L"personal",
        L"private", L"secure", L"confidential", L"secret", L"download",
        L".exe", L".bat", L".cmd", L".scr", L".msi", L"api", L"token"
    };

    for (const auto& keyword : sensitiveKeywords) {
        if (urlLower.find(keyword) != std::wstring::npos ||
            pathLower.find(keyword) != std::wstring::npos) {
            matchedKeywords.push_back(keyword);
        }
    }

    return matchedKeywords;
}

// 获取所有用户的Firefox配置文件路径
std::vector<std::wstring> FirefoxBrowser::GetAllFirefoxUserProfiles() {
    std::vector<std::wstring> userProfiles;

    try {
        // 获取系统盘路径
        WCHAR systemDrive[4];
        if (GetEnvironmentVariableW(L"SystemDrive", systemDrive, 4) == 0) {
            wcscpy_s(systemDrive, L"C:");
        }

        std::wstring usersPath = std::wstring(systemDrive) + L"\\Users";
        printf("Scanning users directory for Firefox profiles: %ls\n", usersPath.c_str());

        if (!fs::exists(usersPath)) {
            printf("Users directory not found: %ls\n", usersPath.c_str());
            return userProfiles;
        }

        // 遍历Users目录下的所有用户文件夹
        for (const auto& entry : fs::directory_iterator(usersPath)) {
            if (entry.is_directory()) {
                std::wstring userName = entry.path().filename().wstring();

                // 跳过系统用户和特殊文件夹
                if (userName == L"Public" || userName == L"Default" ||
                    userName == L"All Users" || userName == L"Default User") {
                    continue;
                }

                std::wstring userPath = entry.path().wstring();
                printf("Found user for Firefox scan: %ls\n", userName.c_str());
                userProfiles.push_back(userPath);
            }
        }

        printf("Found %zu user profiles for Firefox\n", userProfiles.size());
    }
    catch (const std::exception& e) {
        printf("Error scanning user profiles for Firefox: %s\n", e.what());
    }

    return userProfiles;
}

// 扫描指定用户的Firefox缓存目录
void FirefoxBrowser::ScanUserFirefoxCacheDirectory(const std::wstring& userPath, const std::wstring& userName, std::vector<CacheFileData>& caches) {
    try {
        // Firefox配置文件路径（修正：使用Roaming而不是Local）
        std::wstring firefoxProfilesPath = userPath + L"\\AppData\\Roaming\\Mozilla\\Firefox\\Profiles";

        if (!fs::exists(firefoxProfilesPath)) {
            printf("Firefox profiles directory not found for user %ls\n", userName.c_str());
            return;
        }

        printf("Found Firefox profiles directory for user %ls: %ls\n", userName.c_str(), firefoxProfilesPath.c_str());

        // 遍历所有Firefox配置文件
        for (const auto& profileEntry : fs::directory_iterator(firefoxProfilesPath)) {
            if (profileEntry.is_directory()) {
                std::wstring profilePath = profileEntry.path().wstring();
                std::wstring profileName = profileEntry.path().filename().wstring();

                printf("Scanning Firefox profile for user %ls: %ls\n", userName.c_str(), profileName.c_str());

                // 尝试不同的缓存路径
                std::vector<std::wstring> cachePaths = {
                    profilePath + L"\\cache2\\entries",
                    profilePath + L"\\cache",
                    profilePath + L"\\OfflineCache"
                };

                for (const auto& cachePath : cachePaths) {
                    if (fs::exists(cachePath)) {
                        printf("Scanning Firefox cache directory for user %ls: %ls\n", userName.c_str(), cachePath.c_str());

                        // 临时修改缓存数据的用户名
                        size_t beforeCount = caches.size();
                        ScanFirefoxCacheDirectory(cachePath, caches);

                        // 更新新添加的缓存条目的用户名
                        for (size_t i = beforeCount; i < caches.size(); ++i) {
                            caches[i].user_name = userName;
                            caches[i].browser_type = L"Firefox";
                        }
                    }
                }
            }
        }
    }
    catch (const std::exception& e) {
        printf("Error scanning Firefox cache for user %ls: %s\n", userName.c_str(), e.what());
    }
}

// 获取指定用户的Firefox密码
void FirefoxBrowser::GetUserPasswords(const std::wstring& userPath, const std::wstring& userName, std::vector<PasswordData>& passwords) {
    try {
        // Firefox配置文件路径
        std::wstring firefoxProfilesPath = userPath + L"\\AppData\\Roaming\\Mozilla\\Firefox\\Profiles";

        if (!fs::exists(firefoxProfilesPath)) {
            printf("Firefox profiles directory not found for user %ls\n", userName.c_str());
            return;
        }

        printf("Found Firefox profiles directory for user %ls: %ls\n", userName.c_str(), firefoxProfilesPath.c_str());

        // 遍历所有Firefox配置文件
        for (const auto& profileEntry : fs::directory_iterator(firefoxProfilesPath)) {
            if (profileEntry.is_directory()) {
                std::wstring profilePath = profileEntry.path().wstring();
                std::wstring profileName = profileEntry.path().filename().wstring();

                printf("Scanning Firefox profile for user %ls: %ls\n", userName.c_str(), profileName.c_str());

                fs::path loginsPath = profilePath + L"\\logins.json";
                if (!fs::exists(loginsPath)) {
                    continue;
                }

                printf("Found Firefox logins.json for user %ls: %ls\n", userName.c_str(), loginsPath.c_str());

                try {
                    // 初始化NSS
                    if (!InitializeNss(fs::path(profilePath))) {
                        printf("Failed to initialize NSS for user %ls\n", userName.c_str());
                        continue;
                    }

                    // 读取logins.json文件
                    std::ifstream file(loginsPath);
                    if (!file.is_open()) {
                        printf("Failed to open logins.json for user %ls\n", userName.c_str());
                        continue;
                    }

                    nlohmann::json root;
                    file >> root;
                    file.close();

                    // 解析登录数据
                    const nlohmann::json& logins = root["logins"];
                    if (!logins.is_array()) {
                        printf("No logins data found in logins.json for user %ls\n", userName.c_str());
                        continue;
                    }

                    for (const auto& login : logins) {
                        if (!login.is_object()) continue;

                        PasswordData pwd;
                        if (login.contains("hostname") && login["hostname"].is_string()) {
                            pwd.url = Utils::UTF8ToWString(login["hostname"].get<std::string>());
                        }

                        // 解密用户名
                        if (login.contains("encryptedUsername") && login["encryptedUsername"].is_string()) {
                            std::string encUsername = login["encryptedUsername"].get<std::string>();
                            if (!encUsername.empty()) {
                                pwd.username = Utils::UTF8ToWString(DecryptData(encUsername));
                            }
                        }

                        // 解密密码
                        if (login.contains("encryptedPassword") && login["encryptedPassword"].is_string()) {
                            std::string encPassword = login["encryptedPassword"].get<std::string>();
                            if (!encPassword.empty()) {
                                pwd.password = Utils::UTF8ToWString(DecryptData(encPassword));
                            }
                        }

                        // 获取创建时间
                        if (login.contains("timeCreated") && login["timeCreated"].is_number_unsigned()) {
                            __int64 timestamp = login["timeCreated"].get<__int64>();
                            pwd.create_time = Utils::ConvertFirefoxTimestamp(timestamp);
                        }

                        // 设置用户和浏览器信息
                        pwd.user_name = userName;
                        pwd.browser_type = L"Firefox";

                        passwords.push_back(pwd);
                    }
                }
                catch (const std::exception& e) {
                    printf("Error processing Firefox passwords for user %ls: %s\n", userName.c_str(), e.what());
                }
            }
        }
    }
    catch (const std::exception& e) {
        printf("Error getting Firefox passwords for user %ls: %s\n", userName.c_str(), e.what());
    }
}

// 获取指定用户的Firefox历史记录
void FirefoxBrowser::GetUserHistory(const std::wstring& userPath, const std::wstring& userName, std::vector<HistoryData>& history) {
    try {
        // Firefox配置文件路径
        std::wstring firefoxProfilesPath = userPath + L"\\AppData\\Roaming\\Mozilla\\Firefox\\Profiles";

        if (!fs::exists(firefoxProfilesPath)) {
            printf("Firefox profiles directory not found for user %ls\n", userName.c_str());
            return;
        }

        printf("Found Firefox profiles directory for user %ls: %ls\n", userName.c_str(), firefoxProfilesPath.c_str());

        // 遍历所有Firefox配置文件
        for (const auto& profileEntry : fs::directory_iterator(firefoxProfilesPath)) {
            if (profileEntry.is_directory()) {
                std::wstring profilePath = profileEntry.path().wstring();
                std::wstring profileName = profileEntry.path().filename().wstring();

                printf("Scanning Firefox profile for user %ls: %ls\n", userName.c_str(), profileName.c_str());

                fs::path dbPath = profilePath + L"\\places.sqlite";
                if (!fs::exists(dbPath)) {
                    continue;
                }

                printf("Found Firefox places.sqlite for user %ls: %ls\n", userName.c_str(), dbPath.c_str());

                std::wstring tempDbPath = L"temp_places_" + userName;

                try {
                    // 复制数据库文件以避免锁定
                    fs::copy_file(dbPath, tempDbPath, fs::copy_options::overwrite_existing);

                    sqlite3* db = nullptr;
                    if (sqlite3_open16(tempDbPath.c_str(), &db) != SQLITE_OK) {
                        if (db) {
                            printf("Failed to open places.sqlite for user %ls: %s\n", userName.c_str(), sqlite3_errmsg(db));
                            sqlite3_close(db);
                        }
                        fs::remove(tempDbPath);
                        continue;
                    }

                    // 优化SQL查询
                    const char* sql =
                        "SELECT h.url, h.title, v.visit_date, h.visit_count "
                        "FROM moz_places h JOIN moz_historyvisits v ON h.id = v.place_id "
                        "WHERE v.visit_date IS NOT NULL "
                        "ORDER BY v.visit_date DESC LIMIT 1000";

                    sqlite3_stmt* stmt = nullptr;
                    if (sqlite3_prepare_v2(db, sql, -1, &stmt, nullptr) == SQLITE_OK) {
                        while (sqlite3_step(stmt) == SQLITE_ROW) {
                            HistoryData data;

                            const unsigned char* url = sqlite3_column_text(stmt, 0);
                            const unsigned char* title = sqlite3_column_text(stmt, 1);

                            if (url) {
                                data.url = Utils::UTF8ToWString(std::string(reinterpret_cast<const char*>(url)));
                            }

                            if (title) {
                                data.title = Utils::UTF8ToWString(std::string(reinterpret_cast<const char*>(title)));
                            }

                            data.visit_time = Utils::ConvertFirefoxTimestamp(sqlite3_column_int64(stmt, 2));
                            data.visit_count = sqlite3_column_int(stmt, 3);

                            // 设置用户和浏览器信息
                            data.user_name = userName;
                            data.browser_type = L"Firefox";

                            history.push_back(data);
                        }
                        sqlite3_finalize(stmt);
                    }
                    else {
                        printf("Failed to prepare SQL statement for user %ls: %s\n", userName.c_str(), sqlite3_errmsg(db));
                    }

                    sqlite3_close(db);
                    fs::remove(tempDbPath);
                }
                catch (const std::exception& e) {
                    printf("Error processing Firefox history for user %ls: %s\n", userName.c_str(), e.what());
                    if (fs::exists(tempDbPath)) {
                        fs::remove(tempDbPath);
                    }
                }
            }
        }
    }
    catch (const std::exception& e) {
        printf("Error getting Firefox history for user %ls: %s\n", userName.c_str(), e.what());
    }
}

// 获取指定用户的Firefox下载记录
void FirefoxBrowser::GetUserDownloads(const std::wstring& userPath, const std::wstring& userName, std::vector<DownloadData>& downloads) {
    try {
        // Firefox配置文件路径
        std::wstring firefoxProfilesPath = userPath + L"\\AppData\\Roaming\\Mozilla\\Firefox\\Profiles";

        if (!fs::exists(firefoxProfilesPath)) {
            printf("Firefox profiles directory not found for user %ls\n", userName.c_str());
            return;
        }

        printf("Found Firefox profiles directory for user %ls: %ls\n", userName.c_str(), firefoxProfilesPath.c_str());

        // 遍历所有Firefox配置文件
        for (const auto& profileEntry : fs::directory_iterator(firefoxProfilesPath)) {
            if (profileEntry.is_directory()) {
                std::wstring profilePath = profileEntry.path().wstring();
                std::wstring profileName = profileEntry.path().filename().wstring();

                printf("Scanning Firefox profile for user %ls: %ls\n", userName.c_str(), profileName.c_str());

                fs::path dbPath = profilePath + L"\\places.sqlite";
                if (!fs::exists(dbPath)) {
                    continue;
                }

                printf("Found Firefox places.sqlite for user %ls: %ls\n", userName.c_str(), dbPath.c_str());

                std::wstring tempDbPath = L"temp_downloads_" + userName;

                try {
                    // 复制数据库文件以避免锁定
                    fs::copy_file(dbPath, tempDbPath, fs::copy_options::overwrite_existing);

                    sqlite3* db = nullptr;
                    if (sqlite3_open16(tempDbPath.c_str(), &db) != SQLITE_OK) {
                        if (db) {
                            printf("Failed to open places.sqlite for user %ls: %s\n", userName.c_str(), sqlite3_errmsg(db));
                            sqlite3_close(db);
                        }
                        fs::remove(tempDbPath);
                        continue;
                    }

                    // 优化SQL查询
                    const char* sql =
                        "SELECT a.content, p.url, a.dateAdded, "
                        "(SELECT content FROM moz_annos WHERE place_id = p.id "
                        "AND anno_attribute_id = (SELECT id FROM moz_anno_attributes "
                        "WHERE name = 'downloads/endTime')) as endTime, "
                        "(SELECT content FROM moz_annos WHERE place_id = p.id "
                        "AND anno_attribute_id = (SELECT id FROM moz_anno_attributes "
                        "WHERE name = 'downloads/metaData')) as metaData "
                        "FROM moz_annos a "
                        "JOIN moz_places p ON a.place_id = p.id "
                        "JOIN moz_anno_attributes aa ON a.anno_attribute_id = aa.id "
                        "WHERE aa.name = 'downloads/destinationFileURI' "
                        "ORDER BY a.dateAdded DESC LIMIT 1000";

                    sqlite3_stmt* stmt = nullptr;
                    if (sqlite3_prepare_v2(db, sql, -1, &stmt, nullptr) == SQLITE_OK) {
                        while (sqlite3_step(stmt) == SQLITE_ROW) {
                            DownloadData data;

                            const unsigned char* filePath = sqlite3_column_text(stmt, 0);
                            const unsigned char* url = sqlite3_column_text(stmt, 1);

                            if (filePath) {
                                data.file_path = Utils::UTF8ToWString(std::string(reinterpret_cast<const char*>(filePath)));
                            }

                            if (url) {
                                data.url = Utils::UTF8ToWString(std::string(reinterpret_cast<const char*>(url)));
                            }

                            data.start_time = Utils::ConvertFirefoxTimestamp(sqlite3_column_int64(stmt, 2));

                            if (sqlite3_column_type(stmt, 3) != SQLITE_NULL) {
                                data.end_time = Utils::ConvertFirefoxTimestamp(sqlite3_column_int64(stmt, 3));
                            }

                            // 获取文件大小
                            if (sqlite3_column_type(stmt, 4) != SQLITE_NULL) {
                                const unsigned char* metaDataStr = sqlite3_column_text(stmt, 4);
                                if (metaDataStr) {
                                    try {
                                        nlohmann::json metaData = nlohmann::json::parse(reinterpret_cast<const char*>(metaDataStr));
                                        if (metaData.contains("fileSize")) {
                                            data.file_size = metaData["fileSize"].get<uint64_t>();
                                        }
                                    }
                                    catch (const nlohmann::json::parse_error& e) {
                                        printf("Failed to parse metadata JSON for user %ls: %s\n", userName.c_str(), e.what());
                                    }
                                    catch (const std::exception& e) {
                                        printf("Error processing metadata for user %ls: %s\n", userName.c_str(), e.what());
                                        data.file_size = 0;
                                    }
                                }
                            }
                            else {
                                data.file_size = 0;
                            }

                            // 设置用户和浏览器信息
                            data.user_name = userName;
                            data.browser_type = L"Firefox";

                            // 提取文件图标
                            if (!data.file_path.empty()) {
                                printf("提取Firefox文件图标: %ls\n", data.file_path.c_str());
                                data.file_icon = Utils::GetFileIconAsBase64(data.file_path, 32);
                                if (data.file_icon.empty()) {
                                    printf("Firefox文件图标提取失败或文件不存在: %ls\n", data.file_path.c_str());
                                } else {
                                    printf("Firefox文件图标提取成功，Base64长度: %zu\n", data.file_icon.length());
                                }
                            }

                            downloads.push_back(data);
                        }
                        sqlite3_finalize(stmt);
                    }
                    else {
                        printf("Failed to prepare SQL statement for user %ls: %s\n", userName.c_str(), sqlite3_errmsg(db));
                    }

                    sqlite3_close(db);
                    fs::remove(tempDbPath);
                }
                catch (const std::exception& e) {
                    printf("Error processing Firefox downloads for user %ls: %s\n", userName.c_str(), e.what());
                    if (fs::exists(tempDbPath)) {
                        fs::remove(tempDbPath);
                    }
                }
            }
        }
    }
    catch (const std::exception& e) {
        printf("Error getting Firefox downloads for user %ls: %s\n", userName.c_str(), e.what());
    }
}

// 获取指定用户的Firefox Cookie
void FirefoxBrowser::GetUserCookies(const std::wstring& userPath, const std::wstring& userName, std::vector<CookieData>& cookies) {
    try {
        // Firefox配置文件路径
        std::wstring firefoxProfilesPath = userPath + L"\\AppData\\Roaming\\Mozilla\\Firefox\\Profiles";

        if (!fs::exists(firefoxProfilesPath)) {
            printf("Firefox profiles directory not found for user %ls\n", userName.c_str());
            return;
        }

        printf("Found Firefox profiles directory for user %ls: %ls\n", userName.c_str(), firefoxProfilesPath.c_str());

        // 遍历所有Firefox配置文件
        for (const auto& profileEntry : fs::directory_iterator(firefoxProfilesPath)) {
            if (profileEntry.is_directory()) {
                std::wstring profilePath = profileEntry.path().wstring();
                std::wstring profileName = profileEntry.path().filename().wstring();

                printf("Scanning Firefox profile for user %ls: %ls\n", userName.c_str(), profileName.c_str());

                fs::path cookiesPath = profilePath + L"\\cookies.sqlite";
                if (!fs::exists(cookiesPath)) {
                    continue;
                }

                printf("Found Firefox cookies.sqlite for user %ls: %ls\n", userName.c_str(), cookiesPath.c_str());

                // 创建临时文件以避免数据库锁定
                std::wstring tempDbPath = L"temp_firefox_cookies_" + userName;

                try {
                    // 复制数据库文件以避免锁定
                    fs::copy_file(cookiesPath, tempDbPath, fs::copy_options::overwrite_existing);

                    sqlite3* db = nullptr;
                    int rc = sqlite3_open16(tempDbPath.c_str(), &db);
                    if (rc != SQLITE_OK) {
                        printf("Failed to open cookies.sqlite for user %ls: %s\n", userName.c_str(), sqlite3_errmsg(db));
                        if (db) sqlite3_close(db);
                        fs::remove(tempDbPath);
                        continue;
                    }

                    // 首先检查表是否存在
                    const char* checkTableSql = "SELECT name FROM sqlite_master WHERE type='table' AND name='moz_cookies';";
                    sqlite3_stmt* checkStmt = nullptr;
                    rc = sqlite3_prepare_v2(db, checkTableSql, -1, &checkStmt, nullptr);
                    if (rc == SQLITE_OK) {
                        if (sqlite3_step(checkStmt) == SQLITE_ROW) {
                            printf("moz_cookies table exists for user %ls\n", userName.c_str());
                        } else {
                            printf("moz_cookies table does not exist for user %ls!\n", userName.c_str());
                            sqlite3_finalize(checkStmt);
                            sqlite3_close(db);
                            fs::remove(tempDbPath);
                            continue;
                        }
                        sqlite3_finalize(checkStmt);
                    }

                    // 首先检查表结构，然后使用兼容的SQL查询
                    const char* sql = "SELECT host, name, value, path, expiry, isSecure, isHttpOnly FROM moz_cookies LIMIT 1000;";
                    sqlite3_stmt* stmt = nullptr;
                    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, nullptr);
                    if (rc != SQLITE_OK) {
                        printf("Failed to prepare SQL statement for user %ls: %s\n", userName.c_str(), sqlite3_errmsg(db));
                        sqlite3_close(db);
                        fs::remove(tempDbPath);
                        continue;
                    }

                    int cookieCount = 0;
                    while (sqlite3_step(stmt) == SQLITE_ROW) {
                        CookieData cookie;

                        const unsigned char* host = sqlite3_column_text(stmt, 0);
                        const unsigned char* name = sqlite3_column_text(stmt, 1);
                        const unsigned char* value = sqlite3_column_text(stmt, 2);
                        const unsigned char* path = sqlite3_column_text(stmt, 3);

                        // 修正字段映射
                        if (host) {
                            cookie.Host = Utils::UTF8ToWString(reinterpret_cast<const char*>(host));
                        }
                        if (name) {
                            cookie.keyname = Utils::UTF8ToWString(reinterpret_cast<const char*>(name));
                        }
                        if (path) {
                            cookie.path = Utils::UTF8ToWString(reinterpret_cast<const char*>(path));
                        }

                        // 处理Cookie值（Firefox Cookie通常不加密）
                        if (value) {
                            std::string cookieValue(reinterpret_cast<const char*>(value));
                            cookie.Cookie = Utils::UTF8ToWString(cookieValue);
                        }

                        // 设置默认创建时间
                        cookie.createdata = L"Unknown";

                        // 设置用户和浏览器信息
                        cookie.user_name = userName;
                        cookie.browser_type = L"Firefox";

                        cookies.push_back(cookie);
                        cookieCount++;
                    }

                    sqlite3_finalize(stmt);
                    sqlite3_close(db);
                    fs::remove(tempDbPath);

                    printf("Successfully retrieved %d Firefox cookies for user %ls\n", cookieCount, userName.c_str());
                }
                catch (const std::exception& e) {
                    printf("Error processing Firefox cookies for user %ls: %s\n", userName.c_str(), e.what());
                    if (fs::exists(tempDbPath)) {
                        fs::remove(tempDbPath);
                    }
                }
            }
        }
    }
    catch (const std::exception& e) {
        printf("Error getting Firefox cookies for user %ls: %s\n", userName.c_str(), e.what());
    }
}

// 获取指定用户的Firefox书签
void FirefoxBrowser::GetUserBookmarks(const std::wstring& userPath, const std::wstring& userName, std::vector<BookmarkData>& bookmarks) {
    try {
        // Firefox配置文件路径
        std::wstring firefoxProfilesPath = userPath + L"\\AppData\\Roaming\\Mozilla\\Firefox\\Profiles";

        if (!fs::exists(firefoxProfilesPath)) {
            printf("Firefox profiles directory not found for user %ls\n", userName.c_str());
            return;
        }

        printf("Found Firefox profiles directory for user %ls: %ls\n", userName.c_str(), firefoxProfilesPath.c_str());

        // 遍历所有Firefox配置文件
        for (const auto& profileEntry : fs::directory_iterator(firefoxProfilesPath)) {
            if (profileEntry.is_directory()) {
                std::wstring profilePath = profileEntry.path().wstring();
                std::wstring profileName = profileEntry.path().filename().wstring();

                printf("Scanning Firefox profile for user %ls: %ls\n", userName.c_str(), profileName.c_str());

                fs::path placesDbPath = profilePath + L"\\places.sqlite";
                if (!fs::exists(placesDbPath)) {
                    continue;
                }

                printf("Found Firefox places.sqlite for user %ls: %ls\n", userName.c_str(), placesDbPath.c_str());

                // 创建临时文件以避免数据库锁定
                std::wstring tempPath = L"temp_firefox_bookmarks_" + userName;

                try {
                    // 复制数据库文件
                    fs::copy_file(placesDbPath, tempPath, fs::copy_options::overwrite_existing);

                    // 打开数据库
                    sqlite3* db;
                    if (sqlite3_open16(tempPath.c_str(), &db) != SQLITE_OK) {
                        printf("Failed to open Firefox bookmarks database for user %ls: %s\n", userName.c_str(), sqlite3_errmsg(db));
                        if (db) sqlite3_close(db);
                        fs::remove(tempPath);
                        continue;
                    }

                    // 简化的书签查询SQL（使用UTF-8）
                    const char* sql = "SELECT b.title, p.url, b.dateAdded "
                                     "FROM moz_bookmarks AS b "
                                     "JOIN moz_places AS p ON b.fk = p.id "
                                     "WHERE b.type = 1 AND b.title IS NOT NULL AND p.url IS NOT NULL "
                                     "ORDER BY b.dateAdded DESC LIMIT 1000";

                    sqlite3_stmt* stmt;
                    if (sqlite3_prepare_v2(db, sql, -1, &stmt, NULL) == SQLITE_OK) {
                        printf("SQL statement prepared successfully for Firefox bookmarks for user %ls\n", userName.c_str());

                        while (sqlite3_step(stmt) == SQLITE_ROW) {
                            BookmarkData data;

                            // 获取标题
                            const unsigned char* title = sqlite3_column_text(stmt, 0);
                            if (title) {
                                data.title = Utils::UTF8ToWString(reinterpret_cast<const char*>(title));
                            }

                            // 获取URL
                            const unsigned char* url = sqlite3_column_text(stmt, 1);
                            if (url) {
                                data.url = Utils::UTF8ToWString(reinterpret_cast<const char*>(url));
                            }

                            // 获取添加日期
                            sqlite3_int64 timestamp = sqlite3_column_int64(stmt, 2);
                            if (timestamp > 0) {
                                // Firefox时间戳是从1970年1月1日开始的微秒
                                timestamp /= 1000000; // 转换为秒
                                data.date_added = Utils::ConvertFirefoxTimestamp(timestamp);
                            }

                            // 设置默认文件夹路径
                            data.folder_path = L"Bookmarks";

                            // 设置用户和浏览器信息
                            data.user_name = userName;
                            data.browser_type = L"Firefox";

                            bookmarks.push_back(data);
                        }
                        sqlite3_finalize(stmt);
                    } else {
                        printf("Failed to prepare SQL statement for Firefox bookmarks for user %ls: %s\n", userName.c_str(), sqlite3_errmsg(db));
                    }

                    sqlite3_close(db);
                    fs::remove(tempPath);

                    printf("Successfully retrieved Firefox bookmarks for user %ls\n", userName.c_str());
                }
                catch (const std::exception& e) {
                    printf("Error processing Firefox bookmarks for user %ls: %s\n", userName.c_str(), e.what());
                    if (fs::exists(tempPath)) {
                        fs::remove(tempPath);
                    }
                }
            }
        }
    }
    catch (const std::exception& e) {
        printf("Error getting Firefox bookmarks for user %ls: %s\n", userName.c_str(), e.what());
    }
}