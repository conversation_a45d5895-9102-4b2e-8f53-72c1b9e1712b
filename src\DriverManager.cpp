﻿#define _CRT_SECURE_NO_WARNINGS
#include "pch.h"
#include "DriverManager.h"
#include "Utils.h"  // 添加Utils.h包含，用于统一的字符串转换
#include <iostream>
#include <sstream>
#include <memory>
#include <ctime>
#include <iomanip>
#include <fstream>
#include <algorithm>
#include <unordered_set>
#include <chrono>
#include <psapi.h>
#include <winver.h>
#include <wintrust.h>
#include <softpub.h>
#include <mscat.h>
#include <setupapi.h>
#include <devguid.h>

#pragma comment(lib, "advapi32.lib")
#pragma comment(lib, "psapi.lib")
#pragma comment(lib, "version.lib")
#pragma comment(lib, "wintrust.lib")
#pragma comment(lib, "setupapi.lib")

DriverManager::DriverManager() : m_scManager(nullptr), m_initialized(false) {
    m_startTime = std::chrono::system_clock::now();
}

DriverManager::~DriverManager() {
    Cleanup();
}

bool DriverManager::Initialize() {
    if (m_initialized) {
        return true;
    }

    // 打开服务控制管理器
    m_scManager = OpenSCManager(nullptr, nullptr, SC_MANAGER_ENUMERATE_SERVICE | SC_MANAGER_CONNECT);
    if (m_scManager == nullptr) {
        std::cout << u8"[错误] 打开服务控制管理器: " << GetLastErrorString() << std::endl;
        return false;
    }

    m_initialized = true;
    return true;
}

void DriverManager::Cleanup() {
    if (m_scManager) {
        CloseServiceHandle(m_scManager);
        m_scManager = nullptr;
    }
    m_initialized = false;
    m_cache.is_valid = false;
}

std::vector<DriverData> DriverManager::GetAllDrivers() {
    if (!m_initialized) {
        return std::vector<DriverData>();
    }

    // 检查缓存是否有效（5分钟内）
    auto now = std::chrono::system_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::minutes>(now - m_cache.last_update);

    if (m_cache.is_valid && duration.count() < 5) {
        return m_cache.drivers;
    }

    // 刷新缓存
    RefreshDriverCache();
    return m_cache.drivers;
}

bool DriverManager::RefreshDriverCache() {
    if (!m_initialized) {
        return false;
    }

    m_cache.drivers.clear();
    std::unordered_set<std::string> processedDrivers;

    try {
        // 1. 枚举服务驱动程序
        auto serviceDrivers = EnumerateServiceDrivers();
        for (const auto& driver : serviceDrivers) {
            if (processedDrivers.find(driver.name) == processedDrivers.end()) {
                m_cache.drivers.push_back(driver);
                processedDrivers.insert(driver.name);
            }
        }

        // 2. 枚举设备驱动程序
        auto deviceDrivers = EnumerateDeviceDrivers();
        for (const auto& driver : deviceDrivers) {
            if (processedDrivers.find(driver.name) == processedDrivers.end()) {
                m_cache.drivers.push_back(driver);
                processedDrivers.insert(driver.name);
            }
        }

        // 3. 枚举系统驱动程序
        auto systemDrivers = EnumerateSystemDrivers();
        for (const auto& driver : systemDrivers) {
            if (processedDrivers.find(driver.name) == processedDrivers.end()) {
                m_cache.drivers.push_back(driver);
                processedDrivers.insert(driver.name);
            }
        }

        // 计算统计信息
        m_cache.statistics = DriverStatistics();
        m_cache.statistics.total_drivers = static_cast<int>(m_cache.drivers.size());
        m_cache.statistics.scan_time = GetCurrentTimeString();
        m_cache.statistics.scan_duration = GetDurationString(m_startTime);

        for (const auto& driver : m_cache.drivers) {
            if (driver.status == "Running" || driver.status.find(u8"运行") != std::string::npos) {
                m_cache.statistics.running_drivers++;
            } else if (driver.status == "Stopped" || driver.status.find(u8"停止") != std::string::npos) {
                m_cache.statistics.stopped_drivers++;
            }

            // 签名和系统驱动统计已删除
        }

        m_cache.last_update = std::chrono::system_clock::now();
        m_cache.is_valid = true;
        return true;
    }
    catch (const std::exception& e) {
        std::cout << u8"[错误] 刷新驱动程序缓存: " << e.what() << std::endl;
        return false;
    }
}

DriverStatistics DriverManager::GetDriverStatistics() {
    if (!m_cache.is_valid) {
        RefreshDriverCache();
    }
    return m_cache.statistics;
}

nlohmann::json DriverManager::GetDriversInfoAsJson() {
    nlohmann::json result;

    try {
        std::vector<DriverData> drivers = GetAllDrivers();
        DriverStatistics stats = GetDriverStatistics();

        // 构建JSON结果
        result["metadata"] = {
            {"tool_name", u8"Windows驱动程序扫描工具"},
            {"version", "2.0.0"},
            {"scan_time", stats.scan_time},
            {"scan_duration", stats.scan_duration},
            {"total_drivers_found", stats.total_drivers}
        };

        result["statistics"] = stats;

        // 按分类组织驱动程序
        nlohmann::json categorized_drivers;
        for (const auto& driver : drivers) {
            categorized_drivers[driver.driver_category].push_back(driver);
        }

        result["drivers_by_category"] = categorized_drivers;
        result["all_drivers"] = drivers;

        return result;
    }
    catch (const std::exception& e) {
        nlohmann::json error_result;
        error_result["error"] = u8"获取驱动程序信息时发生错误";
        error_result["details"] = e.what();
        return error_result;
    }
}

bool DriverManager::SaveDriversInfoToFile(const std::string& filename) {
    try {
        nlohmann::json driversInfo = GetDriversInfoAsJson();

        std::ofstream file(filename, std::ios::out | std::ios::trunc);
        if (!file.is_open()) {
            std::cout << u8"[错误] 保存文件: 无法打开文件进行写入: " << filename << std::endl;
            return false;
        }

        file << driversInfo.dump(4); // 4 spaces indentation
        file.close();

        std::cout << u8"驱动程序信息已保存到: " << filename << std::endl;
        return true;
    }
    catch (const std::exception& e) {
        std::cout << u8"[错误] 保存驱动程序信息: " << e.what() << std::endl;
        return false;
    }
}


// 注意：字符串转换函数已移至Utils类中，使用以下方法替代：
// Utils::WStringToUTF8() 替代 ConvertToString()
// Utils::UTF8ToWString() 替代 ConvertToWString()

// 核心枚举方法实现
std::vector<DriverData> DriverManager::EnumerateServiceDrivers() {
    std::vector<DriverData> drivers;

    if (!m_scManager) {
        return drivers;
    }

    // 第一次调用获取所需缓冲区大小
    DWORD bytesNeeded = 0;
    DWORD servicesReturned = 0;
    DWORD resumeHandle = 0;

    // 枚举驱动程序服务
    EnumServicesStatusEx(m_scManager, SC_ENUM_PROCESS_INFO, SERVICE_DRIVER,
                        SERVICE_STATE_ALL, nullptr, 0, &bytesNeeded,
                        &servicesReturned, &resumeHandle, nullptr);

    if (GetLastError() != ERROR_MORE_DATA) {
        std::cout << u8"[错误] 枚举服务驱动程序: " << GetLastErrorString() << std::endl;
        return drivers;
    }

    // 分配缓冲区并获取驱动程序列表
    std::vector<BYTE> buffer(bytesNeeded);
    LPENUM_SERVICE_STATUS_PROCESS services = reinterpret_cast<LPENUM_SERVICE_STATUS_PROCESS>(buffer.data());

    if (!EnumServicesStatusEx(m_scManager, SC_ENUM_PROCESS_INFO, SERVICE_DRIVER,
                             SERVICE_STATE_ALL, buffer.data(), bytesNeeded,
                             &bytesNeeded, &servicesReturned, &resumeHandle, nullptr)) {
        std::cout << u8"[错误] 枚举服务驱动程序: " << GetLastErrorString() << std::endl;
        return drivers;
    }

    // 处理每个驱动程序
    for (DWORD i = 0; i < servicesReturned; i++) {
        DriverData driverData;

        // 基本信息
        driverData.name = Utils::WStringToUTF8(services[i].lpServiceName);
        driverData.display_name = Utils::WStringToUTF8(services[i].lpDisplayName);
        driverData.status = GetStatusString(services[i].ServiceStatusProcess.dwCurrentState);

        // 获取详细信息
        if (GetServiceDriverInfo(driverData.name, driverData)) {
            drivers.push_back(driverData);
        }
    }

    return drivers;
}

std::vector<DriverData> DriverManager::EnumerateDeviceDrivers() {
    std::vector<DriverData> drivers;

    // 获取设备驱动程序列表
    LPVOID* driverAddresses = nullptr;
    DWORD needed = 0;

    // 第一次调用获取所需缓冲区大小
    if (!EnumDeviceDrivers(nullptr, 0, &needed)) {
        std::cout << u8"[错误] 获取设备驱动程序缓冲区大小: " << GetLastErrorString() << std::endl;
        return drivers;
    }

    DWORD driverCount = needed / sizeof(LPVOID);
    driverAddresses = new LPVOID[driverCount];

    if (!EnumDeviceDrivers(driverAddresses, needed, &needed)) {
        std::cout << u8"[错误] 枚举设备驱动程序: " << GetLastErrorString() << std::endl;
        delete[] driverAddresses;
        return drivers;
    }

    // 处理每个驱动程序
    for (DWORD i = 0; i < driverCount; i++) {
        DriverData driverData;

        if (GetDeviceDriverInfo(driverAddresses[i], driverData)) {
            drivers.push_back(driverData);
        }
    }

    delete[] driverAddresses;
    return drivers;
}

std::vector<DriverData> DriverManager::EnumerateSystemDrivers() {
    std::vector<DriverData> drivers;

    // 这里可以添加其他系统驱动程序枚举方法
    // 例如通过注册表、WMI等方式获取更多驱动信息

    return drivers;
}

// 驱动程序信息获取方法
bool DriverManager::GetDriverInfo(const std::string& driverName, DriverData& driverData) {
    // 尝试从服务获取信息
    if (GetServiceDriverInfo(driverName, driverData)) {
        return true;
    }

    // 如果服务方式失败，设置基本信息
    driverData.name = driverName;
    return false;
}

bool DriverManager::GetServiceDriverInfo(const std::string& serviceName, DriverData& driverData) {
    if (!m_scManager) {
        return false;
    }

    // 打开服务句柄
    std::wstring wServiceName = Utils::UTF8ToWString(serviceName);
    SC_HANDLE serviceHandle = OpenService(m_scManager, wServiceName.c_str(),
                                         SERVICE_QUERY_CONFIG | SERVICE_QUERY_STATUS);

    if (serviceHandle == nullptr) {
        return false;
    }

    try {
        // 获取服务配置
        DWORD bytesNeeded = 0;
        QueryServiceConfig(serviceHandle, nullptr, 0, &bytesNeeded);

        if (GetLastError() == ERROR_INSUFFICIENT_BUFFER) {
            std::vector<BYTE> buffer(bytesNeeded);
            LPQUERY_SERVICE_CONFIG serviceConfig = reinterpret_cast<LPQUERY_SERVICE_CONFIG>(buffer.data());

            if (QueryServiceConfig(serviceHandle, serviceConfig, bytesNeeded, &bytesNeeded)) {
                // 填充基本信息
                driverData.startup_type = GetStartupTypeString(serviceConfig->dwStartType);
                driverData.driver_category = ClassifyDriver(serviceName, "");

                if (serviceConfig->lpBinaryPathName) {
                    std::string rawPath = Utils::WStringToUTF8(serviceConfig->lpBinaryPathName);
                    driverData.binary_path = ConvertToAbsolutePath(rawPath);

                    // 获取文件信息
                    GetFileInformation(driverData.binary_path, driverData);
                }
            }
        }

        // 获取服务描述
        bytesNeeded = 0;
        QueryServiceConfig2(serviceHandle, SERVICE_CONFIG_DESCRIPTION, nullptr, 0, &bytesNeeded);

        if (GetLastError() == ERROR_INSUFFICIENT_BUFFER) {
            std::vector<BYTE> buffer(bytesNeeded);
            LPSERVICE_DESCRIPTION serviceDesc = reinterpret_cast<LPSERVICE_DESCRIPTION>(buffer.data());

            // 描述信息已删除，跳过获取
        }

        CloseServiceHandle(serviceHandle);
        return true;
    }
    catch (const std::exception& e) {
        CloseServiceHandle(serviceHandle);
        return false;
    }
}

bool DriverManager::GetDeviceDriverInfo(LPVOID baseAddress, DriverData& driverData) {
    if (!baseAddress) {
        return false;
    }

    // 获取驱动程序文件名
    WCHAR driverFileName[MAX_PATH];
    if (!GetDeviceDriverFileName(baseAddress, driverFileName, MAX_PATH)) {
        return false;
    }

    // 获取驱动程序基本名称
    WCHAR driverBaseName[MAX_PATH];
    if (GetDeviceDriverBaseName(baseAddress, driverBaseName, MAX_PATH)) {
        driverData.name = Utils::WStringToUTF8(driverBaseName);
        driverData.display_name = driverData.name;
    }

    // 设置文件路径
    std::string rawPath = Utils::WStringToUTF8(driverFileName);
    driverData.binary_path = ConvertToAbsolutePath(rawPath);

    // 设置加载地址
    driverData.load_address = GetDriverLoadAddress(baseAddress);

    // 获取文件信息
    GetFileInformation(driverData.binary_path, driverData);

    // 分类驱动程序
    driverData.driver_category = ClassifyDriver(driverData.name, driverData.binary_path);

    // 设置状态（设备驱动程序通常是运行中的）
    driverData.status = u8"运行中";

    return true;
}

// 文件信息获取（优化版本）
bool DriverManager::GetFileInformation(const std::string& filePath, DriverData& driverData) {
    if (filePath.empty()) {
        return false;
    }

    try {
        // 签名信息已删除，跳过获取
        return true;
    }
    catch (const std::exception& e) {
        return false;
    }
}

// 文件版本信息获取
std::string DriverManager::GetFileVersion(const std::string& filePath) {
    if (filePath.empty()) return "";

    std::wstring wFilePath = Utils::UTF8ToWString(filePath);
    DWORD verHandle = 0;
    DWORD verSize = GetFileVersionInfoSize(wFilePath.c_str(), &verHandle);

    if (verSize == 0) {
        return "";
    }

    std::vector<BYTE> verData(verSize);
    if (!GetFileVersionInfo(wFilePath.c_str(), verHandle, verSize, verData.data())) {
        return "";
    }

    VS_FIXEDFILEINFO* fileInfo = nullptr;
    UINT size = 0;
    if (!VerQueryValue(verData.data(), L"\\", (LPVOID*)&fileInfo, &size)) {
        return "";
    }

    if (size == 0) {
        return "";
    }

    // 格式化版本号
    std::ostringstream version;
    version << HIWORD(fileInfo->dwFileVersionMS) << "."
            << LOWORD(fileInfo->dwFileVersionMS) << "."
            << HIWORD(fileInfo->dwFileVersionLS) << "."
            << LOWORD(fileInfo->dwFileVersionLS);

    return version.str();
}

std::string DriverManager::GetFileManufacturer(const std::string& filePath) {
    if (filePath.empty()) return "";

    std::wstring wFilePath = Utils::UTF8ToWString(filePath);
    DWORD verHandle = 0;
    DWORD verSize = GetFileVersionInfoSize(wFilePath.c_str(), &verHandle);

    if (verSize == 0) {
        return "";
    }

    std::vector<BYTE> verData(verSize);
    if (!GetFileVersionInfo(wFilePath.c_str(), verHandle, verSize, verData.data())) {
        return "";
    }

    // 获取语言和代码页信息
    LPVOID lpTranslate = nullptr;
    UINT cbTranslate = 0;
    if (!VerQueryValue(verData.data(), L"\\VarFileInfo\\Translation", &lpTranslate, &cbTranslate)) {
        return "";
    }

    if (cbTranslate < sizeof(DWORD)) {
        return "";
    }

    // 构建查询字符串
    DWORD* pdwTranslate = (DWORD*)lpTranslate;
    std::wostringstream query;
    query << L"\\StringFileInfo\\" << std::hex << std::setw(4) << std::setfill(L'0')
          << LOWORD(pdwTranslate[0]) << std::setw(4) << HIWORD(pdwTranslate[0])
          << L"\\CompanyName";

    LPVOID lpBuffer = nullptr;
    UINT dwBytes = 0;
    if (VerQueryValue(verData.data(), query.str().c_str(), &lpBuffer, &dwBytes)) {
        return Utils::WStringToUTF8((LPCWSTR)lpBuffer);
    }

    return "";
}

std::string DriverManager::GetFileDate(const std::string& filePath) {
    if (filePath.empty()) return "";

    std::wstring wFilePath = Utils::UTF8ToWString(filePath);
    WIN32_FILE_ATTRIBUTE_DATA fileAttr;

    if (!GetFileAttributesEx(wFilePath.c_str(), GetFileExInfoStandard, &fileAttr)) {
        return "";
    }

    SYSTEMTIME sysTime;
    if (!FileTimeToSystemTime(&fileAttr.ftLastWriteTime, &sysTime)) {
        return "";
    }

    std::ostringstream result;
    result << std::setfill('0') << std::setw(4) << sysTime.wYear << "-"
           << std::setw(2) << sysTime.wMonth << "-"
           << std::setw(2) << sysTime.wDay;

    return result.str();
}

bool DriverManager::GetFileSignatureInfo(const std::string& filePath, std::string& signer, bool& isSigned) {
    if (filePath.empty()) {
        signer = u8"未知";
        isSigned = false;
        return false;
    }

    std::wstring wFilePath = Utils::UTF8ToWString(filePath);

    WINTRUST_FILE_INFO fileData;
    memset(&fileData, 0, sizeof(fileData));
    fileData.cbStruct = sizeof(WINTRUST_FILE_INFO);
    fileData.pcwszFilePath = wFilePath.c_str();
    fileData.hFile = NULL;
    fileData.pgKnownSubject = NULL;

    GUID WVTPolicyGUID = WINTRUST_ACTION_GENERIC_VERIFY_V2;
    WINTRUST_DATA winTrustData;

    memset(&winTrustData, 0, sizeof(winTrustData));
    winTrustData.cbStruct = sizeof(winTrustData);
    winTrustData.pPolicyCallbackData = NULL;
    winTrustData.pSIPClientData = NULL;
    winTrustData.dwUIChoice = WTD_UI_NONE;
    winTrustData.fdwRevocationChecks = WTD_REVOKE_NONE;
    winTrustData.dwUnionChoice = WTD_CHOICE_FILE;
    winTrustData.dwStateAction = WTD_STATEACTION_VERIFY;
    winTrustData.hWVTStateData = NULL;
    winTrustData.pwszURLReference = NULL;
    winTrustData.dwUIContext = 0;
    winTrustData.pFile = &fileData;

    LONG lStatus = WinVerifyTrust(NULL, &WVTPolicyGUID, &winTrustData);

    // 清理
    winTrustData.dwStateAction = WTD_STATEACTION_CLOSE;
    WinVerifyTrust(NULL, &WVTPolicyGUID, &winTrustData);

    isSigned = (lStatus == ERROR_SUCCESS);

    if (isSigned) {
        signer = u8"Microsoft Corporation"; // 简化实现，大多数系统驱动由微软签名
    } else {
        signer = u8"未签名";
    }

    return isSigned;
}

std::string DriverManager::GetFileSize(const std::string& filePath) {
    if (filePath.empty()) return "0 KB";

    std::wstring wFilePath = Utils::UTF8ToWString(filePath);
    WIN32_FILE_ATTRIBUTE_DATA fileAttr;

    if (!GetFileAttributesEx(wFilePath.c_str(), GetFileExInfoStandard, &fileAttr)) {
        return "0 KB";
    }

    LARGE_INTEGER fileSize;
    fileSize.LowPart = fileAttr.nFileSizeLow;
    fileSize.HighPart = fileAttr.nFileSizeHigh;

    double sizeInKB = fileSize.QuadPart / 1024.0;

    std::ostringstream result;
    if (sizeInKB < 1024) {
        result << std::fixed << std::setprecision(1) << sizeInKB << " KB";
    } else {
        result << std::fixed << std::setprecision(1) << (sizeInKB / 1024.0) << " MB";
    }

    return result.str();
}

// 驱动程序分类
std::string DriverManager::ClassifyDriver(const std::string& driverName, const std::string& filePath) {
    std::string lowerName = driverName;
    std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);

    std::string lowerPath = filePath;
    std::transform(lowerPath.begin(), lowerPath.end(), lowerPath.begin(), ::tolower);

    // 网络驱动
    if (lowerName.find("net") != std::string::npos ||
        lowerName.find("wifi") != std::string::npos ||
        lowerName.find("ethernet") != std::string::npos ||
        lowerName.find("wlan") != std::string::npos) {
        return u8"网络驱动";
    }

    // 显示驱动
    if (lowerName.find("display") != std::string::npos ||
        lowerName.find("video") != std::string::npos ||
        lowerName.find("graphics") != std::string::npos ||
        lowerName.find("nvidia") != std::string::npos ||
        lowerName.find("amd") != std::string::npos ||
        lowerName.find("intel") != std::string::npos) {
        return u8"显示驱动";
    }

    // 音频驱动
    if (lowerName.find("audio") != std::string::npos ||
        lowerName.find("sound") != std::string::npos ||
        lowerName.find("speaker") != std::string::npos) {
        return u8"音频驱动";
    }

    // USB驱动
    if (lowerName.find("usb") != std::string::npos ||
        lowerName.find("hid") != std::string::npos) {
        return u8"USB驱动";
    }

    // 存储驱动
    if (lowerName.find("disk") != std::string::npos ||
        lowerName.find("storage") != std::string::npos ||
        lowerName.find("scsi") != std::string::npos ||
        lowerName.find("ide") != std::string::npos ||
        lowerName.find("sata") != std::string::npos) {
        return u8"存储驱动";
    }

    // 文件系统驱动
    if (lowerName.find("fs") != std::string::npos ||
        lowerName.find("file") != std::string::npos ||
        lowerName.find("ntfs") != std::string::npos ||
        lowerName.find("fat") != std::string::npos) {
        return u8"文件系统驱动";
    }

    // 系统驱动
    if (lowerPath.find("system32") != std::string::npos ||
        lowerPath.find("windows") != std::string::npos) {
        return u8"系统驱动";
    }

    return u8"其他驱动";
}

bool DriverManager::IsSystemDriver(const std::string& manufacturer, const std::string& signer) {
    std::string lowerManufacturer = manufacturer;
    std::transform(lowerManufacturer.begin(), lowerManufacturer.end(), lowerManufacturer.begin(), ::tolower);

    std::string lowerSigner = signer;
    std::transform(lowerSigner.begin(), lowerSigner.end(), lowerSigner.begin(), ::tolower);

    return (lowerManufacturer.find("microsoft") != std::string::npos ||
            lowerSigner.find("microsoft") != std::string::npos ||
            lowerManufacturer.find(u8"微软") != std::string::npos);
}

// 内存和地址信息（真实实现）
std::string DriverManager::GetDriverLoadAddress(LPVOID baseAddress) {
    if (!baseAddress) {
        return "N/A";
    }

    return FormatAddress(baseAddress);
}

std::string DriverManager::GetDriverMemoryUsage(LPVOID baseAddress) {
    if (!baseAddress) {
        return "N/A";
    }

    // 简化实现，实际应该通过更复杂的方法获取驱动程序内存使用情况
    return "N/A";
}

// 辅助函数实现
std::string DriverManager::GetStatusString(DWORD status) {
    switch (status) {
        case SERVICE_STOPPED: return u8"已停止";
        case SERVICE_START_PENDING: return u8"启动中";
        case SERVICE_STOP_PENDING: return u8"停止中";
        case SERVICE_RUNNING: return u8"运行中";
        case SERVICE_CONTINUE_PENDING: return u8"继续中";
        case SERVICE_PAUSE_PENDING: return u8"暂停中";
        case SERVICE_PAUSED: return u8"已暂停";
        default: return u8"未知状态";
    }
}

std::string DriverManager::GetStartupTypeString(DWORD startType) {
    switch (startType) {
        case SERVICE_BOOT_START: return u8"引导启动";
        case SERVICE_SYSTEM_START: return u8"系统启动";
        case SERVICE_AUTO_START: return u8"自动启动";
        case SERVICE_DEMAND_START: return u8"手动启动";
        case SERVICE_DISABLED: return u8"已禁用";
        default: return u8"未知类型";
    }
}

std::string DriverManager::FormatFileSize(DWORD64 size) {
    if (size < 1024) {
        return std::to_string(size) + " B";
    } else if (size < 1024 * 1024) {
        return std::to_string(size / 1024) + " KB";
    } else if (size < 1024 * 1024 * 1024) {
        return std::to_string(size / (1024 * 1024)) + " MB";
    } else {
        return std::to_string(size / (1024 * 1024 * 1024)) + " GB";
    }
}

std::string DriverManager::FormatAddress(LPVOID address) {
    std::ostringstream oss;
    oss << "0x" << std::hex << std::uppercase << reinterpret_cast<uintptr_t>(address);
    return oss.str();
}

std::string DriverManager::GetCurrentTimeString() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    struct tm timeinfo;
#ifdef _WIN32
    // 使用Windows安全版本
    if (localtime_s(&timeinfo, &time_t) != 0) {
        return "Unknown Time";
    }
#else
    // 使用POSIX线程安全版本
    if (localtime_r(&time_t, &timeinfo) == nullptr) {
        return "Unknown Time";
    }
#endif

    std::ostringstream oss;
    oss << std::setfill('0')
        << std::setw(4) << (timeinfo.tm_year + 1900) << "-"
        << std::setw(2) << (timeinfo.tm_mon + 1) << "-"
        << std::setw(2) << timeinfo.tm_mday << " "
        << std::setw(2) << timeinfo.tm_hour << ":"
        << std::setw(2) << timeinfo.tm_min << ":"
        << std::setw(2) << timeinfo.tm_sec;
    return oss.str();
}

std::string DriverManager::GetDurationString(const std::chrono::system_clock::time_point& start) {
    auto now = std::chrono::system_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - start);

    auto seconds = std::chrono::duration_cast<std::chrono::seconds>(duration);
    auto milliseconds = duration - seconds;

    std::ostringstream oss;
    oss << seconds.count() << "." << std::setfill('0') << std::setw(3) << milliseconds.count() << "s";
    return oss.str();
}

// 错误处理
std::string DriverManager::GetLastErrorString() {
    DWORD error = GetLastError();
    if (error == 0) {
        return u8"无错误";
    }

    LPWSTR messageBuffer = nullptr;
    size_t size = FormatMessageW(
        FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
        NULL, error, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
        (LPWSTR)&messageBuffer, 0, NULL);

    std::string message = Utils::WStringToUTF8(messageBuffer);
    LocalFree(messageBuffer);

    return message;
}

void DriverManager::LogError(const std::string& operation, const std::string& details) {
    std::cout << u8"[错误] " << operation << ": " << details << std::endl;
}

// 路径转换函数实现
std::string DriverManager::ConvertToAbsolutePath(const std::string& path) {
    if (path.empty()) {
        return path;
    }

    std::string result = path;

    // 转换 \SystemRoot\ 为实际的Windows目录
    if (result.find("\\SystemRoot\\") == 0) {
        WCHAR windowsDir[MAX_PATH];
        if (GetWindowsDirectory(windowsDir, MAX_PATH) > 0) {
            std::string winDir = Utils::WStringToUTF8(windowsDir);
            result = winDir + result.substr(11);
        }
    }

    return result;
}
