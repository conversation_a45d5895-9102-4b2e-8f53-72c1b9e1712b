﻿#include "pch.h"
#include "Init_BroswerMessage.h"
#include "BrowserDataExtractor.h"
#include "ChromeBrowser.h"
#include "FirefoxBrowser.h"
#include "IEBrowser.h"
#include "Utils.h"
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#include <filesystem>
#include <iostream>
#include <nlohmann/json.hpp>
#include <fstream>
#include <iomanip>
#include <thread>
#include <ctime>
#include <sstream>
#include "WiFiManager.h"
#include "ServiceManager.h"
#include "ProcessManager.h"
#include "ShareManager.h"
#include "DriverManager.h"
#include "ScreensaverManager.h"
#include "FirewallManager.h"
#include "PasswordPolicyManager.h"
#include "AccountLockoutPolicyManager.h"
#include "StartupManager.h"
#include "NetworkConnectionManager.h"
#include "../include/ExifManager.h"
#include "../include/Utils.h"  // 使用Utils中的工具函数

#define _CRT_SECURE_NO_WARNINGS
using json = nlohmann::json;

Init_BroswerMessage::Init_BroswerMessage()
{
}

Init_BroswerMessage::~Init_BroswerMessage()
{
}

// 辅助函数：检查任务是否被取消或暂停
bool CheckTaskControl(const std::string& taskId, QueryTaskControlCallback queryTaskControlCb) {
    if (!queryTaskControlCb) return false;

    // 检查是否取消
    if (queryTaskControlCb(taskId, 0)) {
        return true;
    }

    // 检查是否暂停
    while (queryTaskControlCb(taskId, 1)) {
        // 任务暂停中，等待一段时间后再检查
        std::this_thread::sleep_for(std::chrono::milliseconds(100));

        // 在暂停期间也要检查是否取消
        if (queryTaskControlCb(taskId, 0)) {
            return true;
        }
    }

    return false;
}

// 辅助函数：处理任务取消情况，返回取消结果JSON
std::string GetCancelResult() {
    json cancel_result;
    cancel_result["status"] = "cancelled";
    cancel_result["message"] = "任务已被用户取消";
    return cancel_result.dump();
}

// 辅助函数：将密码数据转换为JSON
json PasswordDataToJson(const std::vector<PasswordData>& passwords) {
    json passwordsJson = json::array();
    for (const auto& pwd : passwords) {
        json pwdJson;
        pwdJson["url"] = Utils::WStringToUTF8(pwd.url);
        pwdJson["username"] = Utils::WStringToUTF8(pwd.username);
        pwdJson["password"] = Utils::WStringToUTF8(pwd.password);
        pwdJson["create_time"] = Utils::WStringToUTF8(pwd.create_time);
        pwdJson["user_name"] = Utils::WStringToUTF8(pwd.user_name);
        pwdJson["browser_type"] = Utils::WStringToUTF8(pwd.browser_type);
        passwordsJson.push_back(pwdJson);
    }
    return passwordsJson;
}

// 辅助函数：将历史记录转换为JSON
json HistoryDataToJson(const std::vector<HistoryData>& history) {
    json historyJson = json::array();
    for (const auto& hist : history) {
        json histJson;
        histJson["url"] = Utils::WStringToUTF8(hist.url);
        histJson["title"] = Utils::WStringToUTF8(hist.title);
        histJson["visit_time"] = Utils::WStringToUTF8(hist.visit_time);
        histJson["visit_count"] = hist.visit_count;
        histJson["user_name"] = Utils::WStringToUTF8(hist.user_name);
        histJson["browser_type"] = Utils::WStringToUTF8(hist.browser_type);
        historyJson.push_back(histJson);
    }
    return historyJson;
}

// 辅助函数：将下载记录转换为JSON
json DownloadDataToJson(const std::vector<DownloadData>& downloads) {
    json downloadsJson = json::array();
    for (const auto& dl : downloads) {
        json dlJson;
        dlJson["url"] = Utils::WStringToUTF8(dl.url);
        dlJson["file_path"] = Utils::WStringToUTF8(dl.file_path);
        dlJson["start_time"] = Utils::WStringToUTF8(dl.start_time);
        dlJson["end_time"] = Utils::WStringToUTF8(dl.end_time);
        dlJson["file_size"] = dl.file_size;
        dlJson["file_icon"] = Utils::WStringToUTF8(dl.file_icon);
        dlJson["user_name"] = Utils::WStringToUTF8(dl.user_name);
        dlJson["browser_type"] = Utils::WStringToUTF8(dl.browser_type);
        downloadsJson.push_back(dlJson);
    }
    return downloadsJson;
}

// 辅助函数：将Cookie转换为JSON
json CookieDataToJson(const std::vector<CookieData>& cookies, bool isChrome = true) {
    json cookiesJson = json::array();
    for (const auto& co : cookies) {
        json cookieJson;
        if (isChrome) {
            cookieJson["Host"] = Utils::WStringToUTF8(co.Host);
            cookieJson["path"] = Utils::WStringToUTF8(co.path);
            cookieJson["cookie_value"] = Utils::WStringToUTF8(co.Cookie);
            cookieJson["keyname"] = Utils::WStringToUTF8(co.keyname);
            cookieJson["createdata"] = Utils::WStringToUTF8(co.createdata);
        } else {
            cookieJson["url"] = Utils::WStringToUTF8(co.path);
            cookieJson["name"] = Utils::WStringToUTF8(co.Host);
            cookieJson["cookie"] = Utils::WStringToUTF8(co.Cookie);
            cookieJson["keyname"] = Utils::WStringToUTF8(co.keyname);
            cookieJson["createdata"] = Utils::WStringToUTF8(co.createdata);
        }
        cookieJson["user_name"] = Utils::WStringToUTF8(co.user_name);
        cookieJson["browser_type"] = Utils::WStringToUTF8(co.browser_type);
        cookiesJson.push_back(cookieJson);
    }
    return cookiesJson;
}

// 辅助函数：将书签转换为JSON
json BookmarkDataToJson(const std::vector<BookmarkData>& bookmarks) {
    json bookmarksJson = json::array();
    for (const auto& bo : bookmarks) {
        json bookmarkJson;
        bookmarkJson["url"] = Utils::WStringToUTF8(bo.url);
        bookmarkJson["title"] = Utils::WStringToUTF8(bo.title);
        bookmarkJson["folder_path"] = Utils::WStringToUTF8(bo.folder_path);
        bookmarkJson["date_added"] = Utils::WStringToUTF8(bo.date_added);
        bookmarkJson["user_name"] = Utils::WStringToUTF8(bo.user_name);
        bookmarkJson["browser_type"] = Utils::WStringToUTF8(bo.browser_type);
        bookmarksJson.push_back(bookmarkJson);
    }
    return bookmarksJson;
}

// 辅助函数：将缓存数据转换为JSON
json CacheDataToJson(const std::vector<CacheFileData>& caches) {
    json cachesJson = json::array();
    for (const auto& cache : caches) {
        json cacheJson;
        cacheJson["url"] = Utils::WStringToUTF8(cache.url);
        cacheJson["local_file_path"] = Utils::WStringToUTF8(cache.local_file_path);
        cacheJson["content_type"] = Utils::WStringToUTF8(cache.content_type);
        cacheJson["file_size"] = cache.file_size;
        cacheJson["create_time"] = Utils::WStringToUTF8(cache.create_time);
        cacheJson["last_access_time"] = Utils::WStringToUTF8(cache.last_access_time);
        cacheJson["last_modified_time"] = Utils::WStringToUTF8(cache.last_modified_time);
        cacheJson["hit_count"] = cache.hit_count;
        cacheJson["user_name"] = Utils::WStringToUTF8(cache.user_name);
        cacheJson["browser_type"] = Utils::WStringToUTF8(cache.browser_type);
        cacheJson["risk_level"] = Utils::WStringToUTF8(cache.risk_level);
        cacheJson["is_suspicious"] = cache.is_suspicious;
        cacheJson["check_result"] = Utils::WStringToUTF8(cache.check_result);

        // 转换匹配的关键字数组
        json keywordsJson = json::array();
        for (const auto& keyword : cache.matched_keywords) {
            keywordsJson.push_back(Utils::WStringToUTF8(keyword));
        }
        cacheJson["matched_keywords"] = keywordsJson;

        cachesJson.push_back(cacheJson);
    }
    return cachesJson;
}

// 根据action过滤并保存浏览器数据到JSON文件
std::string Init_BroswerMessage::GetAllDataAsJson(const std::vector<std::pair<std::wstring, std::wstring>>& chromeBrowsers,
    const std::vector<PasswordData>& firefoxPasswords,
    const std::vector<HistoryData>& firefoxHistory,
    const std::vector<DownloadData>& firefoxDownloads,
    const std::vector<CookieData>& firefoxCookies,
    const std::vector<BookmarkData> firefoxBookmarks,
    const std::vector<CacheFileData>& firefoxCaches,
    const std::vector<PasswordData>& iePasswords,
    const std::vector<HistoryData>& ieHistory,
    const std::vector<DownloadData>& ieDownloads,
    const std::vector<CookieData>& ieCookies,
    const std::vector<BookmarkData> ieBookmarks,
    const std::vector<CacheFileData>& ieCaches,
    const std::string& action) {

    printf("GetAllDataAsJson called with action: %s (using flattened structure)\n", action.c_str());

    json result;

    // 创建扁平化的数据容器，按数据类型分组
    json allPasswords = json::array();
    json allHistory = json::array();
    json allDownloads = json::array();
    json allCookies = json::array();
    json allBookmarks = json::array();
    json allCaches = json::array();

    // 处理Chrome系列浏览器 - 扁平化结构：直接将数据添加到对应的数据类型数组中
    for (const auto& browserPair : chromeBrowsers) {
        std::wstring browserName = browserPair.second;
        printf("Processing Chrome browser: %ls for action: %s\n", browserName.c_str(), action.c_str());

        // 根据action决定获取哪种类型的数据
        ChromeBrowser chromeBrowser(browserPair.first, browserPair.second);

        if (action == "all") {
            // 获取所有数据并添加到对应的扁平化数组中
            printf("Getting all data for Chrome browser: %ls\n", browserName.c_str());
            auto passwords = chromeBrowser.GetPasswords();
            auto history = chromeBrowser.GetHistory();
            auto downloads = chromeBrowser.GetDownloads();
            auto cookies = chromeBrowser.GetCookie();
            auto bookmarks = chromeBrowser.GetBookmarks();
            auto caches = chromeBrowser.GetBroswerCache();

            // 将Chrome数据添加到扁平化数组中
            json chromePasswords = PasswordDataToJson(passwords);
            json chromeHistory = HistoryDataToJson(history);
            json chromeDownloads = DownloadDataToJson(downloads);
            json chromeCookies = CookieDataToJson(cookies, true);
            json chromeBookmarks = BookmarkDataToJson(bookmarks);
            json chromeCaches = CacheDataToJson(caches);

            // 合并到总数组中
            for (auto& item : chromePasswords) allPasswords.push_back(item);
            for (auto& item : chromeHistory) allHistory.push_back(item);
            for (auto& item : chromeDownloads) allDownloads.push_back(item);
            for (auto& item : chromeCookies) allCookies.push_back(item);
            for (auto& item : chromeBookmarks) allBookmarks.push_back(item);
            for (auto& item : chromeCaches) allCaches.push_back(item);
        }
        else if (action == "password") {
            printf("Getting passwords for Chrome browser: %ls\n", browserName.c_str());
            auto passwords = chromeBrowser.GetPasswords();
            json chromePasswords = PasswordDataToJson(passwords);
            for (auto& item : chromePasswords) allPasswords.push_back(item);
        }
        else if (action == "history") {
            printf("Getting history for Chrome browser: %ls\n", browserName.c_str());
            auto history = chromeBrowser.GetHistory();
            json chromeHistory = HistoryDataToJson(history);
            for (auto& item : chromeHistory) allHistory.push_back(item);
        }
        else if (action == "downloads") {
            printf("Getting downloads for Chrome browser: %ls\n", browserName.c_str());
            auto downloads = chromeBrowser.GetDownloads();
            json chromeDownloads = DownloadDataToJson(downloads);
            for (auto& item : chromeDownloads) allDownloads.push_back(item);
        }
        else if (action == "cookies") {
            printf("Getting cookies for Chrome browser: %ls\n", browserName.c_str());
            auto cookies = chromeBrowser.GetCookie();
            json chromeCookies = CookieDataToJson(cookies, true);
            for (auto& item : chromeCookies) allCookies.push_back(item);
        }
        else if (action == "bookmarks") {
            printf("Getting bookmarks for Chrome browser: %ls\n", browserName.c_str());
            auto bookmarks = chromeBrowser.GetBookmarks();
            json chromeBookmarks = BookmarkDataToJson(bookmarks);
            for (auto& item : chromeBookmarks) allBookmarks.push_back(item);
        }
        else if (action == "cache") {
            printf("Getting cache for Chrome browser: %ls\n", browserName.c_str());
            auto caches = chromeBrowser.GetBroswerCache();
            json chromeCaches = CacheDataToJson(caches);
            for (auto& item : chromeCaches) allCaches.push_back(item);
        }
    }

    // 处理Firefox数据 - 扁平化结构：直接将数据添加到对应的数据类型数组中
    printf("Processing Firefox data for action: %s\n", action.c_str());
    if (action == "all") {
        // 将Firefox数据添加到扁平化数组中
        json firefoxPasswordsJson = PasswordDataToJson(firefoxPasswords);
        json firefoxHistoryJson = HistoryDataToJson(firefoxHistory);
        json firefoxDownloadsJson = DownloadDataToJson(firefoxDownloads);
        json firefoxCookiesJson = CookieDataToJson(firefoxCookies, false);
        json firefoxBookmarksJson = BookmarkDataToJson(firefoxBookmarks);
        json firefoxCachesJson = CacheDataToJson(firefoxCaches);

        // 合并到总数组中
        for (auto& item : firefoxPasswordsJson) allPasswords.push_back(item);
        for (auto& item : firefoxHistoryJson) allHistory.push_back(item);
        for (auto& item : firefoxDownloadsJson) allDownloads.push_back(item);
        for (auto& item : firefoxCookiesJson) allCookies.push_back(item);
        for (auto& item : firefoxBookmarksJson) allBookmarks.push_back(item);
        for (auto& item : firefoxCachesJson) allCaches.push_back(item);
    }
    else if (action == "password") {
        json firefoxPasswordsJson = PasswordDataToJson(firefoxPasswords);
        for (auto& item : firefoxPasswordsJson) allPasswords.push_back(item);
    }
    else if (action == "history") {
        json firefoxHistoryJson = HistoryDataToJson(firefoxHistory);
        for (auto& item : firefoxHistoryJson) allHistory.push_back(item);
    }
    else if (action == "downloads") {
        json firefoxDownloadsJson = DownloadDataToJson(firefoxDownloads);
        for (auto& item : firefoxDownloadsJson) allDownloads.push_back(item);
    }
    else if (action == "cookies") {
        json firefoxCookiesJson = CookieDataToJson(firefoxCookies, false);
        for (auto& item : firefoxCookiesJson) allCookies.push_back(item);
    }
    else if (action == "bookmarks") {
        json firefoxBookmarksJson = BookmarkDataToJson(firefoxBookmarks);
        for (auto& item : firefoxBookmarksJson) allBookmarks.push_back(item);
    }
    else if (action == "cache") {
        json firefoxCachesJson = CacheDataToJson(firefoxCaches);
        for (auto& item : firefoxCachesJson) allCaches.push_back(item);
    }

    // 处理IE数据 - 扁平化结构：直接将数据添加到对应的数据类型数组中
    printf("Processing IE data for action: %s\n", action.c_str());
    if (action == "all") {
        // 将IE数据添加到扁平化数组中
        json iePasswordsJson = PasswordDataToJson(iePasswords);
        json ieHistoryJson = HistoryDataToJson(ieHistory);
        json ieDownloadsJson = DownloadDataToJson(ieDownloads);
        json ieCookiesJson = CookieDataToJson(ieCookies, false);
        json ieBookmarksJson = BookmarkDataToJson(ieBookmarks);
        json ieCachesJson = CacheDataToJson(ieCaches);

        // 合并到总数组中
        for (auto& item : iePasswordsJson) allPasswords.push_back(item);
        for (auto& item : ieHistoryJson) allHistory.push_back(item);
        for (auto& item : ieDownloadsJson) allDownloads.push_back(item);
        for (auto& item : ieCookiesJson) allCookies.push_back(item);
        for (auto& item : ieBookmarksJson) allBookmarks.push_back(item);
        for (auto& item : ieCachesJson) allCaches.push_back(item);
    }
    else if (action == "password") {
        json iePasswordsJson = PasswordDataToJson(iePasswords);
        for (auto& item : iePasswordsJson) allPasswords.push_back(item);
    }
    else if (action == "history") {
        json ieHistoryJson = HistoryDataToJson(ieHistory);
        for (auto& item : ieHistoryJson) allHistory.push_back(item);
    }
    else if (action == "downloads") {
        json ieDownloadsJson = DownloadDataToJson(ieDownloads);
        for (auto& item : ieDownloadsJson) allDownloads.push_back(item);
    }
    else if (action == "cookies") {
        json ieCookiesJson = CookieDataToJson(ieCookies, false);
        for (auto& item : ieCookiesJson) allCookies.push_back(item);
    }
    else if (action == "bookmarks") {
        json ieBookmarksJson = BookmarkDataToJson(ieBookmarks);
        for (auto& item : ieBookmarksJson) allBookmarks.push_back(item);
    }
    else if (action == "cache") {
        json ieCachesJson = CacheDataToJson(ieCaches);
        for (auto& item : ieCachesJson) allCaches.push_back(item);
    }

    // 构建扁平化的最终结果 - 按数据类型分组，而不是按浏览器分组
    if (action == "all") {
        result["passwords"] = allPasswords;
        result["history"] = allHistory;
        result["downloads"] = allDownloads;
        result["cookies"] = allCookies;
        result["bookmarks"] = allBookmarks;
        result["caches"] = allCaches;
    }
    else if (action == "password") {
        result["passwords"] = allPasswords;
    }
    else if (action == "history") {
        result["history"] = allHistory;
    }
    else if (action == "downloads") {
        result["downloads"] = allDownloads;
    }
    else if (action == "cookies") {
        result["cookies"] = allCookies;
    }
    else if (action == "bookmarks") {
        result["bookmarks"] = allBookmarks;
    }
    else if (action == "cache") {
        result["caches"] = allCaches;
    }

    printf("Returning flattened result for action: %s\n", action.c_str());
    printf("Data counts - Passwords: %zu, History: %zu, Downloads: %zu, Cookies: %zu, Bookmarks: %zu, Caches: %zu\n",
           allPasswords.size(), allHistory.size(), allDownloads.size(),
           allCookies.size(), allBookmarks.size(), allCaches.size());

    // 返回扁平化的结果
    return result.dump(4);

}

std::string Init_BroswerMessage::GetProcessInfoAsJson()
{
    // 创建进程信息管理器实例
    ProcessInfoManager processManager;

    // 获取所有进程的详细信息（包括图标和网络连接）
    json processInfo = processManager.GetAllProcessesDetailed();

    // 将JSON对象转换为格式化的字符串
    std::string jsonStr = processInfo.dump(4); // 使用4个空格缩进格式化输出

    return jsonStr;
}

std::string Init_BroswerMsg(const std::string& params, void(*progressCallback)(const std::string&, int), const std::string& taskId, QueryTaskControlCallback queryTaskControlCb)
{
    std::string action = "all"; // 默认获取所有数据
    try {
        json paramsJson = json::parse(params);
        if (paramsJson.contains("action")) {
            action = paramsJson["action"].get<std::string>();
        }
    }
    catch (const std::exception& e) {
        printf("解析参数失败: %s\n", e.what());
        // 解析失败时使用默认值
    }

    // 检查是否取消任务
    if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
        return GetCancelResult();
    }

    // 根据action决定要获取的数据类型
    bool getPasswords = (action == "all" || action == "password");
    bool getHistory = (action == "all" || action == "history");
    bool getDownloads = (action == "all" || action == "downloads");
    bool getCookies = (action == "all" || action == "cookies");
    bool getBookmarks = (action == "all" || action == "bookmarks");
    bool getCaches = (action == "all" || action == "cache");

    printf("Action requested: %s\n", action.c_str());
    printf("Will get - Passwords: %s, History: %s, Downloads: %s, Cookies: %s, Bookmarks: %s, Caches: %s\n",
           getPasswords ? "Yes" : "No", getHistory ? "Yes" : "No", getDownloads ? "Yes" : "No",
           getCookies ? "Yes" : "No", getBookmarks ? "Yes" : "No", getCaches ? "Yes" : "No");

    // 声明存储数据的变量
    auto browserPairs = ChromeBrowser::GetInstalledBrowsers();
    std::vector<PasswordData> firefoxPasswords;
    std::vector<HistoryData> firefoxHistory;
    std::vector<DownloadData> firefoxDownloads;
    std::vector<CookieData> firefoxCookies;
    std::vector<BookmarkData> firefoxBookmarks;
    std::vector<CacheFileData> firefoxCaches;
    std::vector<PasswordData> iePasswords;
    std::vector<HistoryData> ieHistory;
    std::vector<DownloadData> ieDownloads;
    std::vector<CookieData> ieCookies;
    std::vector<BookmarkData> ieBookmarks;
    std::vector<CacheFileData> ieCaches;

    // 注意：Chrome系列浏览器的数据获取将在GetAllDataAsJson函数中进行
    // 这里不需要重复获取，避免数据丢失问题
    printf("Chrome browsers will be processed in GetAllDataAsJson function\n");

    if (progressCallback) {
        progressCallback(taskId, 20);
    }

    // 检查是否取消或暂停
    if (CheckTaskControl(taskId, queryTaskControlCb)) {
        return GetCancelResult();
    }

    // 处理Firefox浏览器
    printf("Processing Firefox...\n");
    FirefoxBrowser firefoxBrowser;

    // 获取密码
    if (getPasswords) {
        firefoxPasswords = firefoxBrowser.GetPasswords();
    }

    if (progressCallback) {
        progressCallback(taskId, 30);
    }

    // 检查是否取消或暂停
    if (CheckTaskControl(taskId, queryTaskControlCb)) {
        return GetCancelResult();
    }

    // 获取历史记录
    if (getHistory) {
        firefoxHistory = firefoxBrowser.GetHistory();
    }

    if (progressCallback) {
        progressCallback(taskId, 40);
    }

    // 检查是否取消或暂停
    if (CheckTaskControl(taskId, queryTaskControlCb)) {
        return GetCancelResult();
    }

    // 获取下载记录
    if (getDownloads) {
        firefoxDownloads = firefoxBrowser.GetDownloads();
    }

    // 获取Cookies
    if (getCookies) {
        firefoxCookies = firefoxBrowser.GetCookie();
    }

    printf("\n");

    if (progressCallback) {
        progressCallback(taskId, 50);
    }

    // 检查是否取消或暂停
    if (CheckTaskControl(taskId, queryTaskControlCb)) {
        return GetCancelResult();
    }

    if (getBookmarks) {
        firefoxBookmarks = firefoxBrowser.GetBookmarks();
    }

    // 获取缓存
    if (getCaches) {
        firefoxCaches = firefoxBrowser.GetBroswerCache();
    }

    // 处理IE浏览器
    printf("Processing Internet Explorer...\n");
    IEBrowser ieBrowser;

    // 获取密码
    if (getPasswords) {
        iePasswords = ieBrowser.GetPasswords();
    }

    if (progressCallback) {
        progressCallback(taskId, 60);
    }

    // 检查是否取消或暂停
    if (CheckTaskControl(taskId, queryTaskControlCb)) {
        return GetCancelResult();
    }

    // 获取历史记录
    if (getHistory) {
        ieHistory = ieBrowser.GetHistory();
    }

    if (progressCallback) {
        progressCallback(taskId, 70);
    }

    // 检查是否取消或暂停
    if (CheckTaskControl(taskId, queryTaskControlCb)) {
        return GetCancelResult();
    }

    // 获取下载记录
    if (getDownloads) {
        ieDownloads = ieBrowser.GetDownloads();
    }

    if (progressCallback) {
        progressCallback(taskId, 80);
    }

    // 检查是否取消或暂停
    if (CheckTaskControl(taskId, queryTaskControlCb)) {
        return GetCancelResult();
    }

    // 获取Cookies
    if (getCookies) {
        ieCookies = ieBrowser.GetCookie();
    }

    if (getBookmarks) {
        ieBookmarks = ieBrowser.GetBookmarks();
    }

    // 获取缓存
    if (getCaches) {
        ieCaches = ieBrowser.GetBroswerCache();
    }

    Init_BroswerMessage msg;
    // 获取根据action过滤的JSON字符串
    std::string jsonData = msg.GetAllDataAsJson(browserPairs, firefoxPasswords, firefoxHistory, firefoxDownloads, firefoxCookies, firefoxBookmarks, firefoxCaches,
        iePasswords, ieHistory, ieDownloads, ieCookies, ieBookmarks, ieCaches, action);

    if (progressCallback) {
        progressCallback(taskId, 100);
    }

    // 构建成功结果
    json success_result;
    success_result["status"] = "success";
    success_result["data"] = json::parse(jsonData);

    return success_result.dump(4);
}

//std::string Init_ProcessInfoMsg(const std::string& params, void(*progressCallback)(const std::string&, int), const std::string& taskId, QueryTaskControlCallback queryTaskControlCb)
//{
//    std::cout << "正在获取系统进程信息..." << std::endl;
//
//    // 检查是否取消任务
//    if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
//        return GetCancelResult();
//    }
//
//    if (progressCallback) {
//        progressCallback(taskId, 10);
//    }
//
//    // 检查是否暂停
//    if (CheckTaskControl(taskId, queryTaskControlCb)) {
//        return GetCancelResult();
//    }
//
//    Init_BroswerMessage message;
//
//    if (progressCallback) {
//        progressCallback(taskId, 30);
//    }
//
//    // 检查是否取消或暂停
//    if (CheckTaskControl(taskId, queryTaskControlCb)) {
//        return GetCancelResult();
//    }
//
//    // 调用封装的接口函数获取进程信息
//    std::string jsonStr = message.GetProcessInfoAsJson();
//
//    if (progressCallback) {
//        progressCallback(taskId, 70);
//    }
//
//    // 检查是否取消或暂停
//    if (CheckTaskControl(taskId, queryTaskControlCb)) {
//        return GetCancelResult();
//    }
//
//    // 创建进程信息管理器实例（用于显示信息）
//    ProcessInfoManager processManager;
//
//    // 输出进程数量
//    std::vector<ProcessInfo> allProcesses = processManager.GetAllProcesses();
//    std::cout << "共发现 " << allProcesses.size() << " 个进程" << std::endl;
//
//    // 显示获取到的JSON数据大小
//    std::cout << "获取到的JSON数据大小: " << jsonStr.size() << " 字节" << std::endl;
//
//    if (progressCallback) {
//        progressCallback(taskId, 100);
//    }
//
//    // 构建成功结果
//    json success_result;
//    success_result["status"] = "success";
//    success_result["data"] = json::parse(jsonStr);
//    success_result["process_count"] = allProcesses.size();
//
//    return success_result.dump(4);
//}


// 封装的WiFi信息获取接口实现
std::string Init_WifiInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
) {
    try {
        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始初始化
        if (progressCallback) {
            progressCallback("Initializing WiFi Manager...", 10);
        }

        // 创建WiFi管理器实例
        WiFiManager wifiManager;
        if (!wifiManager.Initialize()) {
            nlohmann::json errorResult = {
                {"status", "error"},
                {"message", "Failed to initialize WiFi Manager. Administrator privileges may be required."},
                {"task_id", taskId},
                {"error_code", "INIT_FAILED"}
            };
            return errorResult.dump();
        }

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始扫描
        if (progressCallback) {
            progressCallback("Scanning WiFi profiles...", 50);
        }

        // 获取WiFi信息
        nlohmann::json wifiInfo = wifiManager.GetWiFiInfoAsJson();

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：完成
        if (progressCallback) {
            progressCallback("WiFi scan completed successfully", 100);
        }

        // 添加任务状态信息
        wifiInfo["status"] = "success";
        wifiInfo["task_id"] = taskId;
        wifiInfo["message"] = "WiFi information retrieved successfully";

        // 如果有参数，可以在这里处理参数逻辑
        if (!params.empty()) {
            wifiInfo["request_params"] = params;
        }

        return wifiInfo.dump(4);

    }
    catch (const std::exception& e) {
        // 异常处理
        nlohmann::json errorResult = {
            {"status", "error"},
            {"message", std::string("Exception occurred: ") + e.what()},
            {"task_id", taskId},
            {"error_code", "EXCEPTION"}
        };

        if (progressCallback) {
            progressCallback("Error occurred during WiFi scan", -1);
        }

        return errorResult.dump();
    }
}
std::string Init_ServiceInfoMsg(const std::string& params, void(*progressCallback)(const std::string&, int), const std::string& taskId, QueryTaskControlCallback queryTaskControlCb)
{
    try {
        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId ,0)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始初始化
        if (progressCallback) {
            progressCallback("Initializing Service Manager...", 10);
        }

        // 创建服务管理器实例
        ServiceManager serviceManager;
        if (!serviceManager.Initialize()) {
            nlohmann::json errorResult = {
                {"status", "error"},
                {"message", "Failed to initialize Service Manager. Administrator privileges may be required."},
                {"task_id", taskId},
                {"error_code", "INIT_FAILED"}
            };
            return errorResult.dump();
        }

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, -1)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始扫描
        if (progressCallback) {
            progressCallback("Enumerating Windows services...", 50);
        }

        // 获取服务信息
        nlohmann::json serviceInfo = serviceManager.GetServicesInfoAsJson();

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, -1)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：完成
        if (progressCallback) {
            progressCallback("Service enumeration completed successfully", 100);
        }

        // 添加任务状态信息
        serviceInfo["status"] = "success";
        serviceInfo["task_id"] = taskId;
        serviceInfo["message"] = "Windows services information retrieved successfully";

        // 如果有参数，可以在这里处理参数逻辑
        if (!params.empty()) {
            serviceInfo["request_params"] = params;
        }

        return serviceInfo.dump(4);

    }
    catch (const std::exception& e) {
        // 异常处理
        nlohmann::json errorResult = {
            {"status", "error"},
            {"message", std::string("Exception occurred: ") + e.what()},
            {"task_id", taskId},
            {"error_code", "EXCEPTION"}
        };

        if (progressCallback) {
            progressCallback("Error occurred during service enumeration", -1);
        }

        return errorResult.dump();
    }
}


// 封装的进程信息获取接口实现
std::string Init_ProcessallInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
) {
    try {
        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始初始化
        if (progressCallback) {
            progressCallback("Initializing Process Manager...", 10);
        }

        // 创建进程管理器实例
        ProcessManager processManager;

        // 检查参数中是否指定了详细模式
        bool useDetailedMode = (params.find("detailed=true") != std::string::npos);
        processManager.SetFastMode(!useDetailedMode);

        if (!processManager.Initialize()) {
            nlohmann::json errorResult = {
                {"status", "error"},
                {"message", "Failed to initialize Process Manager."},
                {"task_id", taskId},
                {"error_code", "INIT_FAILED"}
            };
            return errorResult.dump();
        }

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始扫描
        if (progressCallback) {
            if (processManager.IsFastMode()) {
                progressCallback("Enumerating running processes (fast mode)...", 50);
            }
            else {
                progressCallback("Enumerating running processes (detailed mode)...", 50);
            }
        }

        // 获取进程信息
        nlohmann::json processInfo = processManager.GetProcessesInfoAsJson();

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：保存到文件
        if (progressCallback) {
            progressCallback("Saving processes information to file...", 90);
        }

     

      

        // 添加任务状态信息
        processInfo["status"] = "success";
        processInfo["task_id"] = taskId;
        processInfo["message"] = "Windows processes information retrieved successfully";

        

        // 如果有参数，可以在这里处理参数逻辑
        if (!params.empty()) {
            processInfo["request_params"] = params;
        }

        return processInfo.dump(4);

    }
    catch (const std::exception& e) {
        // 异常处理
        nlohmann::json errorResult = {
            {"status", "error"},
            {"message", std::string("Exception occurred: ") + e.what()},
            {"task_id", taskId},
            {"error_code", "EXCEPTION"}
        };

        if (progressCallback) {
            progressCallback("Error occurred during process enumeration", -1);
        }

        return errorResult.dump();
    }
}


// 封装的共享信息获取接口实现
std::string Init_ShareInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
) {
    try {
        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, -1)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始初始化
        if (progressCallback) {
            progressCallback("Initializing Share Manager...", 10);
        }

        // 创建共享管理器实例
        ShareManager shareManager;
        if (!shareManager.Initialize()) {
            nlohmann::json errorResult = {
                {"status", "error"},
                {"message", "Failed to initialize Share Manager."},
                {"task_id", taskId},
                {"error_code", "INIT_FAILED"}
            };
            return errorResult.dump();
        }

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, -1)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始扫描
        if (progressCallback) {
            progressCallback("Enumerating network shares...", 50);
        }

        // 获取共享信息
        nlohmann::json shareInfo = shareManager.GetSharesInfoAsJson();

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, -1)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：完成
        if (progressCallback) {
            progressCallback("Share enumeration completed successfully", 100);
        }

        // 添加任务状态信息
        shareInfo["status"] = "success";
        shareInfo["task_id"] = taskId;
        shareInfo["message"] = "Network shares information retrieved successfully";

        // 如果有参数，可以在这里处理参数逻辑
        if (!params.empty()) {
            shareInfo["request_params"] = params;
        }

        return shareInfo.dump(4);

    }
    catch (const std::exception& e) {
        // 异常处理
        nlohmann::json errorResult = {
            {"status", "error"},
            {"message", std::string("Exception occurred: ") + e.what()},
            {"task_id", taskId},
            {"error_code", "EXCEPTION"}
        };

        if (progressCallback) {
            progressCallback("Error occurred during share enumeration", -1);
        }

        return errorResult.dump();
    }
}

std::string Init_DriverInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb) {

    std::cout << "=== Init_DriverInfoMsg 开始执行 ===" << std::endl;
    std::cout << "参数: " << params << std::endl;
    std::cout << "任务ID: " << taskId << std::endl;

    try {
        // 检查任务是否被取消 (临时禁用用于调试)
        if (false && queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            std::cout << "任务被取消" << std::endl;
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始初始化
        std::cout << "开始初始化驱动程序管理器..." << std::endl;
        if (progressCallback) {
            progressCallback("Initializing Driver Manager...", 10);
        }

        // 创建驱动程序管理器实例
        DriverManager driverManager;
        std::cout << "DriverManager 实例已创建" << std::endl;

        bool initResult = driverManager.Initialize();
        std::cout << "DriverManager.Initialize() 返回: " << (initResult ? "成功" : "失败") << std::endl;

        if (!initResult) {
            std::cout << "驱动程序管理器初始化失败" << std::endl;
            nlohmann::json errorResult = {
                {"status", "error"},
                {"message", "Failed to initialize Driver Manager. Administrator privileges may be required."},
                {"task_id", taskId},
                {"error_code", "INIT_FAILED"}
            };
            return errorResult.dump();
        }

        // 检查任务是否被取消 (临时禁用用于调试)
        if (false && queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始扫描
        if (progressCallback) {
            progressCallback("Enumerating system drivers...", 50);
        }

        // 获取驱动程序信息
        std::cout << "开始调用 driverManager.GetDriversInfoAsJson()..." << std::endl;
        nlohmann::json driverInfo = driverManager.GetDriversInfoAsJson();
        std::cout << "GetDriversInfoAsJson() 调用完成" << std::endl;

        // 检查返回的JSON是否为空
        if (driverInfo.empty()) {
            std::cout << "警告: GetDriversInfoAsJson() 返回空的JSON对象" << std::endl;
        } else {
            std::cout << "GetDriversInfoAsJson() 返回数据大小: " << driverInfo.dump().length() << " 字符" << std::endl;
            std::cout << "JSON对象包含的键数量: " << driverInfo.size() << std::endl;
        }

        // 检查任务是否被取消 (临时禁用用于调试)
        if (false && queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            std::cout << "任务在获取驱动信息后被取消" << std::endl;
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：完成
        if (progressCallback) {
            progressCallback("Driver enumeration completed successfully", 100);
        }

        // 添加任务状态信息
        driverInfo["status"] = "success";
        driverInfo["task_id"] = taskId;
        driverInfo["message"] = "System drivers information retrieved successfully";

        // 如果有参数，可以在这里处理参数逻辑
        if (!params.empty()) {
            driverInfo["request_params"] = params;
        }

        std::cout << "准备返回最终结果..." << std::endl;
        std::string finalResult = driverInfo.dump(4);
        std::cout << "最终结果大小: " << finalResult.length() << " 字符" << std::endl;
        std::cout << "最终结果前100字符: " << finalResult.substr(0, 100) << std::endl;
        std::cout << "=== Init_DriverInfoMsg 执行完成 ===" << std::endl;

        return finalResult;

    }
    catch (const std::exception& e) {
        // 异常处理
        std::cout << "=== 捕获到异常 ===" << std::endl;
        std::cout << "异常信息: " << e.what() << std::endl;

        nlohmann::json errorResult = {
            {"status", "error"},
            {"message", std::string("Exception occurred: ") + e.what()},
            {"task_id", taskId},
            {"error_code", "EXCEPTION"}
        };

        if (progressCallback) {
            progressCallback("Error occurred during driver enumeration", -1);
        }

        std::cout << "返回异常结果: " << errorResult.dump(4) << std::endl;
        return errorResult.dump(4);
    }
}



// 封装的防火墙信息获取接口
std::string Init_FirewallInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
) {
    try {
        if (progressCallback) {
            progressCallback("Initializing firewall manager...", 10);
        }

        FirewallManager manager;
        if (!manager.Initialize()) {
            return "{\"error\":\"Failed to initialize firewall manager\"}";
        }

        if (progressCallback) {
            progressCallback("Getting firewall information...", 50);
        }

        nlohmann::json result = manager.GetFirewallInfoAsJson();

        if (progressCallback) {
            progressCallback("Firewall scan completed", 100);
        }

        manager.Cleanup();
        return result.dump();

    }
    catch (const std::exception& e) {
        return "{\"error\":\"" + std::string(e.what()) + "\"}";
    }
}


std::string Init_ScreensaverInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb) {

    try {
        if (progressCallback) {
            progressCallback("Initializing screensaver manager...", 10);
        }

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, -1)) {
            return "{\"error\":\"Task cancelled by user\"}";
        }

        ScreensaverManager manager;
        if (!manager.Initialize()) {
            return "{\"error\":\"Failed to initialize screensaver manager\"}";
        }

        if (progressCallback) {
            progressCallback("Scanning system screensavers...", 30);
        }

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, -1)) {
            return "{\"error\":\"Task cancelled by user\"}";
        }

        if (progressCallback) {
            progressCallback("Getting screensaver details...", 60);
        }

        nlohmann::json screensaversInfo = manager.GetScreensaversInfoAsJson();

        if (progressCallback) {
            progressCallback("Processing screensaver information...", 80);
        }

       

        if (progressCallback) {
            progressCallback("Screensaver scan completed", 100);
        }

        return screensaversInfo.dump(4);
    }
    catch (const std::exception& e) {
        std::string error = "{\"error\":\"Exception occurred: ";
        error += e.what();
        error += "\"}";
        return error;
    }
}


// 封装的密码策略信息获取接口实现
std::string Init_PasswordPolicyInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
) {
    try {
        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始初始化
        if (progressCallback) {
            progressCallback(u8"正在初始化密码策略管理器...", 10);
        }

        // 创建密码策略管理器实例
        PasswordPolicyManager policyManager;
        if (!policyManager.Initialize()) {
            nlohmann::json errorResult = {
                {"status", "error"},
                {"message", u8"初始化密码策略管理器失败。可能需要管理员权限。"},
                {"task_id", taskId},
                {"error_code", "INIT_FAILED"}
            };
            return errorResult.dump();
        }

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始扫描
        if (progressCallback) {
            progressCallback(u8"正在扫描密码策略...", 50);
        }

        // 获取密码策略信息
        PasswordPolicyData policy = policyManager.GetPasswordPolicy();

        // 构建密码策略JSON结果
        nlohmann::json policyInfo;
        policyInfo["metadata"] = {
            {"tool_name", "Windows Password Policy Scanner"},
            {"version", "1.0.0"},
            {"scan_time", policyManager.GetCurrentTimestamp()},
            {"policy_source", policy.policy_source}
        };

        // 核心密码策略信息
        policyInfo["password_policy"] = {
            {"min_password_length", policy.min_password_length},
            {"min_password_age_days", policy.min_password_age_days},
            {"max_password_age_days", policy.max_password_age_days},
            {"password_history_count", policy.password_history_count},
            {"complexity_enabled", policy.complexity_enabled},
            {"reversible_encryption", policy.reversible_encryption},
            {"lockout_duration_minutes", policy.lockout_duration_minutes},
            {"lockout_observation_window_minutes", policy.lockout_observation_window_minutes},
            {"lockout_threshold", policy.lockout_threshold}
        };

        // 获取Guest账户状态
        bool guestEnabled = false;
        std::vector<UserAccountData> users = policyManager.GetAllUserAccounts();
        for (const auto& user : users) {
            if (user.username == "Guest") {
                guestEnabled = user.enabled;
                break;
            }
        }

        // 获取自动登录设置（从注册表）
        std::string autoLoginUser = "";
        try {
            autoLoginUser = policyManager.ReadRegistryString(
                HKEY_LOCAL_MACHINE,
                "SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Winlogon",
                "DefaultUserName"
            );
        }
        catch (...) {
            autoLoginUser = "";
        }
        bool autoLoginEnabled = !autoLoginUser.empty();

        // 系统设置
        policyInfo["system_settings"] = {
            {"guest_account_enabled", guestEnabled},
            {"auto_login_enabled", autoLoginEnabled},
            {"auto_login_user", autoLoginUser}
        };

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：完成
        if (progressCallback) {
            progressCallback(u8"密码策略扫描完成", 100);
        }

        // 添加任务状态信息
        policyInfo["status"] = "success";
        policyInfo["task_id"] = taskId;
        policyInfo["message"] = u8"密码策略信息获取成功";

        // 如果有参数，可以在这里处理参数逻辑
        if (!params.empty()) {
            policyInfo["request_params"] = params;
        }

        return policyInfo.dump(4);

    }
    catch (const std::exception& e) {
        // 异常处理
        nlohmann::json errorResult = {
            {"status", "error"},
            {"message", std::string(u8"发生异常: ") + e.what()},
            {"task_id", taskId},
            {"error_code", "EXCEPTION"}
        };

        if (progressCallback) {
            progressCallback(u8"密码策略扫描过程中发生错误", -1);
        }

        return errorResult.dump();
    }
}


std::string Init_UserAccountInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
) {
    try {
        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始初始化
        if (progressCallback) {
            progressCallback(u8"正在初始化用户账户管理器...", 10);
        }

        // 创建密码策略管理器实例
        PasswordPolicyManager policyManager;
        if (!policyManager.Initialize()) {
            nlohmann::json errorResult = {
                {"status", "error"},
                {"message", u8"初始化用户账户管理器失败。可能需要管理员权限。"},
                {"task_id", taskId},
                {"error_code", "INIT_FAILED"}
            };
            return errorResult.dump();
        }

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始扫描
        if (progressCallback) {
            progressCallback(u8"正在扫描用户账户...", 50);
        }

        // 获取用户账户信息
        std::vector<UserAccountData> users = policyManager.GetAllUserAccounts();
        PasswordPolicyStatistics stats = policyManager.GetPasswordPolicyStatistics();

        // 构建用户账户JSON结果
        nlohmann::json userAccountInfo;
        userAccountInfo["metadata"] = {
            {"tool_name", "Windows User Account Scanner"},
            {"version", "1.0.0"},
            {"scan_time", stats.scan_time},
            {"scan_duration", stats.scan_duration},
            {"total_users_found", stats.total_users}
        };

        userAccountInfo["statistics"] = stats;

        // 按类型组织用户
        nlohmann::json categorized_users;
        for (const auto& user : users) {
            std::string category;
            if (policyManager.IsAdministratorAccount(user.username)) {
                category = "administrators";
            }
            else if (policyManager.IsGuestAccount(user.username)) {
                category = "guests";
            }
            else if (policyManager.IsServiceAccount(user.username)) {
                category = "service_accounts";
            }
            else {
                category = "regular_users";
            }
            categorized_users[category].push_back(user);
        }

        userAccountInfo["users_by_category"] = categorized_users;
        userAccountInfo["all_users"] = users;

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：完成
        if (progressCallback) {
            progressCallback(u8"用户账户扫描完成", 100);
        }

        // 添加任务状态信息
        userAccountInfo["status"] = "success";
        userAccountInfo["task_id"] = taskId;
        userAccountInfo["message"] = u8"用户账户信息获取成功";

        // 如果有参数，可以在这里处理参数逻辑
        if (!params.empty()) {
            userAccountInfo["request_params"] = params;
        }

        return userAccountInfo.dump(4);

    }
    catch (const std::exception& e) {
        // 异常处理
        nlohmann::json errorResult = {
            {"status", "error"},
            {"message", std::string(u8"发生异常: ") + e.what()},
            {"task_id", taskId},
            {"error_code", "EXCEPTION"}
        };

        if (progressCallback) {
            progressCallback(u8"用户账户扫描过程中发生错误", -1);
        }

        return errorResult.dump();
    }
}

// 封装的账户锁定策略信息获取接口实现
std::string Init_AccountLockoutPolicyInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
) {
    try {
        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始初始化
        if (progressCallback) {
            progressCallback(u8"正在初始化账户锁定策略管理器...", 10);
        }

        // 创建账户锁定策略管理器实例
        AccountLockoutPolicyManager lockoutManager;
        if (!lockoutManager.Initialize()) {
            nlohmann::json errorResult = {
                {"status", "error"},
                {"message", u8"初始化账户锁定策略管理器失败。可能需要管理员权限。"},
                {"task_id", taskId},
                {"error_code", "INIT_FAILED"}
            };
            return errorResult.dump();
        }

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始扫描
        if (progressCallback) {
            progressCallback(u8"正在扫描账户锁定策略和审核设置...", 50);
        }

        // 获取账户锁定策略信息
        nlohmann::json lockoutPolicyInfo = lockoutManager.GetAccountLockoutPolicyInfoAsJson();

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：完成
        if (progressCallback) {
            progressCallback(u8"账户锁定策略扫描完成", 100);
        }

        // 添加任务状态信息
        lockoutPolicyInfo["status"] = "success";
        lockoutPolicyInfo["task_id"] = taskId;
        lockoutPolicyInfo["message"] = u8"账户锁定策略信息获取成功";

        // 如果有参数，可以在这里处理参数逻辑
        if (!params.empty()) {
            lockoutPolicyInfo["request_params"] = params;
        }

        return lockoutPolicyInfo.dump(4);

    }
    catch (const std::exception& e) {
        // 异常处理
        nlohmann::json errorResult = {
            {"status", "error"},
            {"message", std::string(u8"发生异常: ") + e.what()},
            {"task_id", taskId},
            {"error_code", "EXCEPTION"}
        };

        if (progressCallback) {
            progressCallback(u8"账户锁定策略扫描过程中发生错误", -1);
        }

        return errorResult.dump();
    }
}


// 封装的启动项信息获取接口实现
std::string Init_StartupInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
) {
    try {
        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始初始化
        if (progressCallback) {
            progressCallback(u8"正在初始化启动项管理器...", 10);
        }

        // 创建启动项管理器实例
        StartupManager startupManager;
        if (!startupManager.Initialize()) {
            nlohmann::json errorResult = {
                {"status", "error"},
                {"message", u8"初始化启动项管理器失败。可能需要管理员权限。"},
                {"task_id", taskId},
                {"error_code", "INIT_FAILED"}
            };
            return errorResult.dump();
        }

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始扫描
        if (progressCallback) {
            progressCallback(u8"正在扫描启动项...", 50);
        }

        // 获取启动项信息
        nlohmann::json startupInfo = startupManager.GetStartupInfoAsJson();

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：完成
        if (progressCallback) {
            progressCallback(u8"启动项扫描完成", 100);
        }

        // 添加任务状态信息
        startupInfo["status"] = "success";
        startupInfo["task_id"] = taskId;
        startupInfo["message"] = u8"启动项信息获取成功";

        // 如果有参数，可以在这里处理参数逻辑
        if (!params.empty()) {
            startupInfo["request_params"] = params;
        }

        return startupInfo.dump(4);

    }
    catch (const std::exception& e) {
        // 异常处理
        nlohmann::json errorResult = {
            {"status", "error"},
            {"message", std::string(u8"发生异常: ") + e.what()},
            {"task_id", taskId},
            {"error_code", "EXCEPTION"}
        };

        if (progressCallback) {
            progressCallback(u8"启动项扫描过程中发生错误", -1);
        }

        return errorResult.dump();
    }
}


// 封装的网络连接信息获取接口实现
std::string Init_NetworkConnectionInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
) {
    try {
        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始初始化
        if (progressCallback) {
            progressCallback(u8"正在初始化网络连接管理器...", 10);
        }

        // 创建网络连接管理器实例
        NetworkConnectionManager manager;

        // 报告进度：获取网络连接信息
        if (progressCallback) {
            progressCallback(u8"正在扫描网络连接...", 30);
        }

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled during network scan"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 获取网络连接数据
        std::vector<NetworkConnectionData> connections = manager.GetNetworkConnectionData();

        // 报告进度：获取统计信息
        if (progressCallback) {
            progressCallback(u8"正在分析网络连接统计信息...", 70);
        }

        // 获取统计信息
        NetworkConnectionStats stats = manager.GetConnectionStats();

        // 报告进度：生成结果
        if (progressCallback) {
            progressCallback(u8"正在生成网络连接报告...", 90);
        }

        // 构建完整的结果JSON
        nlohmann::json result;
        result["status"] = "success";
        result["task_id"] = taskId;
        result["timestamp"] = std::time(nullptr);
        result["connections"] = connections;
        result["statistics"] = stats;

        // 报告进度：完成
        if (progressCallback) {
            progressCallback(u8"网络连接扫描完成", 100);
        }

        return result.dump(4);

    }
    catch (const std::exception& e) {
        // 错误处理
        nlohmann::json errorResult = {
            {"status", "error"},
            {"message", "Failed to get network connection information"},
            {"details", e.what()},
            {"task_id", taskId}
        };

        if (progressCallback) {
            progressCallback(u8"网络连接扫描失败", 0);
        }

        return errorResult.dump();
    }
}


std::string Init_ExifExtractorMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
)
{
    ExifExtractor extractor;

    if (!extractor.Initialize()) {
        nlohmann::json result;
        result["status"] = "error";
        result["message"] = "Failed to initialize EXIF extractor";
        result["error"] = extractor.GetLastError();
        return Utils::SafeJsonDump(result);
    }

    if (progressCallback) {
        progressCallback("Starting device image scan and EXIF analysis...", 0);
    }

    if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
        nlohmann::json result;
        result["status"] = "cancelled";
        result["message"] = "Task cancelled";
        return Utils::SafeJsonDump(result);
    }

    // 第一步：扫描设备上的所有图片文件
    std::vector<std::string> imageFiles;
    try {
        imageFiles = extractor.ScanImageFiles(progressCallback);
    }
    catch (const std::exception& e) {
        nlohmann::json result;
        result["status"] = "error";
        result["message"] = "Failed to scan image files";
        result["error"] = e.what();
        return Utils::SafeJsonDump(result);
    }

    if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
        nlohmann::json result;
        result["status"] = "cancelled";
        result["message"] = "Task cancelled during file scan";
        return Utils::SafeJsonDump(result);
    }

    if (progressCallback) {
        progressCallback("Analyzing EXIF data for " + Utils::NumberToString(imageFiles.size()) + " images...", 95);
    }

    // 如果没有找到图片文件，返回空结果
    if (imageFiles.empty()) {
        nlohmann::json emptyResult;
        emptyResult["status"] = "success";
        emptyResult["message"] = "No image files found on device";
        emptyResult["task_id"] = taskId;
        emptyResult["total_images_found"] = 0;
        emptyResult["statistics"] = nlohmann::json::object();
        emptyResult["statistics"]["total_images"] = 0;
        emptyResult["statistics"]["successful_extractions"] = 0;
        emptyResult["statistics"]["extraction_success_rate"] = 0.0;
        emptyResult["statistics"]["top_manufacturers"] = nlohmann::json::object();
        emptyResult["statistics"]["top_models"] = nlohmann::json::object();
        emptyResult["image_analysis"] = nlohmann::json::array();

        if (progressCallback) {
            progressCallback("No images found to analyze", 100);
        }

        // 保存空结果到文件
        std::string jsonResult = Utils::SafeJsonDump(emptyResult);
        std::string fileName = Utils::GenerateJsonFileName();
        if (Utils::SaveJsonToFile(jsonResult, fileName)) {
            emptyResult["output_file"] = fileName;
            emptyResult["file_saved"] = true;
            if (progressCallback) {
                progressCallback("Empty results saved to " + fileName, 100);
            }
        }

        return Utils::SafeJsonDump(emptyResult);
    }

    // 第二步：分析每个图片的EXIF信息
    nlohmann::json result;
    result["status"] = "success";
    result["message"] = "EXIF analysis completed";
    result["task_id"] = taskId;
    result["total_images_found"] = (int)imageFiles.size();

    nlohmann::json imageAnalysis = nlohmann::json::array();
    nlohmann::json statistics = nlohmann::json::object();

    // 统计信息
    std::map<std::string, int> manufacturerCount;
    std::map<std::string, int> modelCount;
    int successfulExtractions = 0;
    int totalSize = 0;

    for (size_t i = 0; i < imageFiles.size(); i++) {
        if (queryTaskControlCb && queryTaskControlCb(taskId, 0)) {
            result["status"] = "cancelled";
            result["message"] = "Task cancelled during EXIF analysis";
            return Utils::SafeJsonDump(result);
        }

        const std::string& originalFilePath = imageFiles[i];
        ExifInfo exifInfo;
        // 使用原始路径进行EXIF提取
        bool success = extractor.ExtractExifInfo(originalFilePath, exifInfo);

        nlohmann::json imageData;
        // 对于JSON输出，使用清理后的路径以避免编码问题
        imageData["file_path"] = Utils::CleanFilePathString(originalFilePath);
        imageData["success"] = success;

        if (success) {
            successfulExtractions++;
            imageData["manufacturer"] = Utils::CleanUtf8String(exifInfo.manufacturer);
            imageData["model"] = Utils::CleanUtf8String(exifInfo.model);
            imageData["date_time"] = Utils::CleanUtf8String(exifInfo.dateTime);
            imageData["width"] = Utils::CleanUtf8String(exifInfo.width);
            imageData["height"] = Utils::CleanUtf8String(exifInfo.height);

            // 统计制造商和型号（清理字符串）
            std::string cleanManufacturer = Utils::CleanUtf8String(exifInfo.manufacturer);
            std::string cleanModel = Utils::CleanUtf8String(exifInfo.model);

            if (!cleanManufacturer.empty() && cleanManufacturer != "Unknown") {
                manufacturerCount[cleanManufacturer]++;
            }
            if (!cleanModel.empty() && cleanModel != "Unknown") {
                modelCount[cleanModel]++;
            }

            // 计算图片尺寸
            try {
                int width = Utils::StringToInt(exifInfo.width);
                int height = Utils::StringToInt(exifInfo.height);
                totalSize += width * height;
            }
            catch (...) {
                // 忽略转换错误
            }
        }
        else {
            imageData["error"] = Utils::CleanUtf8String(extractor.GetLastError());
            // 添加调试信息
            imageData["debug_original_path_length"] = (int)originalFilePath.length();
            imageData["debug_path_has_high_bytes"] = false;
            for (unsigned char c : originalFilePath) {
                if (c >= 128) {
                    imageData["debug_path_has_high_bytes"] = true;
                    break;
                }
            }
        }

        imageAnalysis.push_back(imageData);

        // 移除限制，处理所有找到的图片文件
        // 注释：为了确保扫描到桌面等用户目录中的文件，不再限制处理数量
    }

    // 构建统计信息
    try {
        statistics["total_images"] = (int)imageFiles.size();
        statistics["successful_extractions"] = successfulExtractions;

        double successRate = imageFiles.size() > 0 ?
            (double)successfulExtractions / imageFiles.size() * 100.0 : 0.0;
        statistics["extraction_success_rate"] = successRate;

        // 构建制造商统计
        nlohmann::json manufacturers = nlohmann::json::object();
        for (const auto& pair : manufacturerCount) {
            if (!pair.first.empty()) {
                manufacturers[pair.first] = pair.second;
            }
        }
        statistics["top_manufacturers"] = manufacturers;

        // 构建型号统计
        nlohmann::json models = nlohmann::json::object();
        for (const auto& pair : modelCount) {
            if (!pair.first.empty()) {
                models[pair.first] = pair.second;
            }
        }
        statistics["top_models"] = models;

    }
    catch (const std::exception& e) {
        // 如果统计信息构建失败，提供基本信息
        statistics["total_images"] = (int)imageFiles.size();
        statistics["successful_extractions"] = successfulExtractions;
        statistics["extraction_success_rate"] = 0.0;
        statistics["top_manufacturers"] = nlohmann::json::object();
        statistics["top_models"] = nlohmann::json::object();
        statistics["error"] = "Failed to build detailed statistics";
    }

    try {
        result["statistics"] = statistics;
        result["image_analysis"] = imageAnalysis;

        if (!params.empty()) {
            result["request_params"] = params;
        }

        if (progressCallback) {
            progressCallback("EXIF analysis completed for " + Utils::NumberToString(imageFiles.size()) + " images", 100);
        }

        // 生成JSON字符串
        std::string jsonResult = Utils::SafeJsonDump(result);

        // 保存结果到JSON文件
        std::string fileName = Utils::GenerateJsonFileName();
        if (Utils::SaveJsonToFile(jsonResult, fileName)) {
            if (progressCallback) {
                progressCallback("Results saved to " + fileName, 100);
            }

            // 在结果中添加文件保存信息
            try {
                nlohmann::json resultWithFile = nlohmann::json::parse(jsonResult);
                resultWithFile["output_file"] = fileName;
                resultWithFile["file_saved"] = true;
                return Utils::SafeJsonDump(resultWithFile);
            }
            catch (...) {
                // 如果解析失败，返回原始结果
                return jsonResult;
            }
        }
        else {
            if (progressCallback) {
                progressCallback("Warning: Failed to save results to file", 100);
            }

            // 在结果中添加文件保存失败信息
            try {
                nlohmann::json resultWithFile = nlohmann::json::parse(jsonResult);
                resultWithFile["output_file"] = fileName;
                resultWithFile["file_saved"] = false;
                resultWithFile["save_error"] = "Failed to write JSON file";
                return Utils::SafeJsonDump(resultWithFile);
            }
            catch (...) {
                return jsonResult;
            }
        }

    }
    catch (const std::exception& e) {
        // 如果JSON构建失败，返回最简化的结果
        try {
            nlohmann::json errorResult;
            errorResult["status"] = "error";
            errorResult["message"] = "Failed to build JSON result";
            errorResult["error"] = Utils::CleanUtf8String(std::string(e.what()));
            errorResult["total_images_found"] = (int)imageFiles.size();
            errorResult["successful_extractions"] = successfulExtractions;

            std::string jsonResult = Utils::SafeJsonDump(errorResult);

            // 尝试保存错误结果到文件
            std::string fileName = Utils::GenerateJsonFileName();
            if (Utils::SaveJsonToFile(jsonResult, fileName)) {
                errorResult["output_file"] = fileName;
                errorResult["file_saved"] = true;
            }
            else {
                errorResult["output_file"] = fileName;
                errorResult["file_saved"] = false;
            }

            return Utils::SafeJsonDump(errorResult);
        }
        catch (...) {
            // 最终备用方案：纯ASCII JSON
            std::ostringstream oss;
            oss << "{"
                << "\"status\":\"error\","
                << "\"message\":\"Critical JSON encoding failure\","
                << "\"total_images_found\":" << imageFiles.size() << ","
                << "\"successful_extractions\":" << successfulExtractions
                << "}";

            // 尝试保存最简结果
            std::string simpleResult = oss.str();
            std::string fileName = Utils::GenerateJsonFileName();
            Utils::SaveJsonToFile(simpleResult, fileName);

            return simpleResult;
        }
    }
}

