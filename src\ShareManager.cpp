﻿#include "pch.h"
#include "ShareManager.h"
#include <iostream>
#include <sstream>
#include <memory>
#include <ctime>
#include <iomanip>
#include <fstream>

#pragma comment(lib, "netapi32.lib")

ShareManager::ShareManager() : m_initialized(false) {
}

ShareManager::~ShareManager() {
    Cleanup();
}

bool ShareManager::Initialize() {
    if (m_initialized) {
        return true;
    }

    m_initialized = true;
    return true;
}

void ShareManager::Cleanup() {
    m_initialized = false;
}

std::vector<ShareData> ShareManager::GetAllShares() {
    std::vector<ShareData> allShares;

    if (!m_initialized) {
        return allShares;
    }

    PSHARE_INFO_502 shareInfo = nullptr;
    DWORD entriesRead = 0;
    DWORD totalEntries = 0;
    DWORD resumeHandle = 0;

    // 枚举网络共享
    NET_API_STATUS status = NetShareEnum(
        nullptr,                    // 本地计算机
        502,                        // 信息级别
        (LPBYTE*)&shareInfo,        // 数据缓冲区
        MAX_PREFERRED_LENGTH,       // 缓冲区大小
        &entriesRead,               // 读取的条目数
        &totalEntries,              // 总条目数
        &resumeHandle               // 恢复句柄
    );

    if (status != NERR_Success) {
        std::cout << "Failed to enumerate network shares: " << status << std::endl;
        return allShares;
    }

    // 遍历共享列表
    for (DWORD i = 0; i < entriesRead; i++) {
        ShareData shareData;

        // 基本信息
        shareData.share_name = Utils::WStringToUTF8(shareInfo[i].shi502_netname);
        shareData.share_path = Utils::WStringToUTF8(shareInfo[i].shi502_path ? shareInfo[i].shi502_path : L"");
        shareData.share_type = GetShareTypeString(shareInfo[i].shi502_type);
        shareData.description = Utils::WStringToUTF8(shareInfo[i].shi502_remark ? shareInfo[i].shi502_remark : L"");
        shareData.current_uses = shareInfo[i].shi502_current_uses;
        shareData.max_uses = shareInfo[i].shi502_max_uses;
        shareData.is_hidden = IsHiddenShare(shareInfo[i].shi502_netname);
        shareData.share_flags = shareInfo[i].shi502_type;

        // 获取权限信息
        shareData.permissions = GetSharePermissions(shareInfo[i].shi502_netname);

        // 安全描述符信息（简化处理）
        if (shareInfo[i].shi502_security_descriptor) {
            shareData.security_descriptor = "Present";
        } else {
            shareData.security_descriptor = "None";
        }

        allShares.push_back(shareData);
    }

    // 释放内存
    if (shareInfo) {
        NetApiBufferFree(shareInfo);
    }

    return allShares;
}

nlohmann::json ShareManager::GetSharesInfoAsJson() {
    nlohmann::json result;

    // 获取所有共享
    std::vector<ShareData> shares = GetAllShares();
    result["shares"] = shares;

    // 统计信息
    int diskShares = 0;
    int printShares = 0;
    int ipcShares = 0;
    int hiddenShares = 0;
    int activeConnections = 0;

    for (const auto& share : shares) {
        if (share.share_type == "Disk") {
            diskShares++;
        } else if (share.share_type == "Print") {
            printShares++;
        } else if (share.share_type == "IPC") {
            ipcShares++;
        }

        if (share.is_hidden) {
            hiddenShares++;
        }

        activeConnections += share.current_uses;
    }

    // 添加元数据
    result["metadata"] = {
        {"total_shares", shares.size()},
        {"disk_shares", diskShares},
        {"print_shares", printShares},
        {"ipc_shares", ipcShares},
        {"hidden_shares", hiddenShares},
        {"active_connections", activeConnections},
        {"scan_time", std::time(nullptr)},
        {"version", "1.0"}
    };

    return result;
}

bool ShareManager::SaveSharesInfoToFile(const std::string& filename) {
    try {
        // 获取共享信息的JSON数据
        nlohmann::json shareInfo = GetSharesInfoAsJson();

        // 打开文件进行写入
        std::ofstream file(filename, std::ios::out | std::ios::trunc);
        if (!file.is_open()) {
            std::cout << "Failed to open file for writing: " << filename << std::endl;
            return false;
        }

        // 将JSON数据写入文件（格式化输出，缩进为4个空格）
        file << shareInfo.dump(4);
        file.close();

        std::cout << "Shares information saved to: " << filename << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cout << "Error saving shares information to file: " << e.what() << std::endl;
        return false;
    }
}


// 注意：字符串转换函数已移至Utils类中，使用以下方法替代：
// Utils::WStringToUTF8() 替代 ConvertToString()
// Utils::UTF8ToWString() 替代 ConvertToWString()

std::string ShareManager::GetShareTypeString(DWORD shareType) {
    // 移除特殊标志位
    DWORD baseType = shareType & 0xFF;

    switch (baseType) {
        case STYPE_DISKTREE: return "Disk";
        case STYPE_PRINTQ: return "Print";
        case STYPE_DEVICE: return "Device";
        case STYPE_IPC: return "IPC";
        default: return "Unknown";
    }
}

bool ShareManager::IsHiddenShare(const std::wstring& shareName) {
    // 隐藏共享通常以$结尾
    if (!shareName.empty() && shareName.back() == L'$') {
        return true;
    }
    return false;
}

std::string ShareManager::GetSharePermissions(const std::wstring& shareName) {
    // 这里可以实现更详细的权限检查
    // 暂时返回基本信息
    if (IsHiddenShare(shareName)) {
        return "Administrative";
    }
    return "Standard";
}

ShareData ShareManager::GetShareDetails(const std::wstring& shareName) {
    ShareData shareData;

    PSHARE_INFO_502 shareInfo = nullptr;
    // 创建非const副本以满足API要求
    std::wstring shareNameCopy = shareName;
    NET_API_STATUS status = NetShareGetInfo(
        nullptr,                    // 本地计算机
        const_cast<LPWSTR>(shareNameCopy.c_str()), // 共享名称
        502,                        // 信息级别
        (LPBYTE*)&shareInfo         // 数据缓冲区
    );

    if (status == NERR_Success && shareInfo) {
        shareData.share_name = Utils::WStringToUTF8(shareInfo->shi502_netname);
        shareData.share_path = Utils::WStringToUTF8(shareInfo->shi502_path ? shareInfo->shi502_path : L"");
        shareData.share_type = GetShareTypeString(shareInfo->shi502_type);
        shareData.description = Utils::WStringToUTF8(shareInfo->shi502_remark ? shareInfo->shi502_remark : L"");
        shareData.current_uses = shareInfo->shi502_current_uses;
        shareData.max_uses = shareInfo->shi502_max_uses;
        shareData.is_hidden = IsHiddenShare(shareName);
        shareData.share_flags = shareInfo->shi502_type;
        shareData.permissions = GetSharePermissions(shareName);

        if (shareInfo->shi502_security_descriptor) {
            shareData.security_descriptor = "Present";
        } else {
            shareData.security_descriptor = "None";
        }

        NetApiBufferFree(shareInfo);
    }

    return shareData;
}
