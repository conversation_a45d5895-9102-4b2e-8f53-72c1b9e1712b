# BrowserDataExtractor DLL API 接口文档

## 版本信息
- **版本**: 1.0.0
- **更新日期**: 2024年12月
- **兼容性**: Windows XP SP3 及以上版本

---

## 快速开始

### 1. 加载DLL
```cpp
HMODULE hDll = LoadLibraryW(L"BrowserDataExtractor.dll");
if (!hDll) {
    // 处理加载失败
    return false;
}
```

### 2. 获取函数指针
```cpp
typedef std::string (*InitBroswerMsgFunc)(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    bool(*queryTaskControlCb)(const std::string&, int)
);

InitBroswerMsgFunc InitBroswerMsg = 
    (InitBroswerMsgFunc)GetProcAddress(hDll, "Init_BroswerMsg");
```

### 3. 调用函数
```cpp
std::string params = R"({"action": "all"})";
std::string result = InitBroswerMsg(params, nullptr, "task1", nullptr);
```

---

## 核心API函数

### Init_BroswerMsg - 浏览器数据提取
**功能**: 提取Chrome、Firefox、IE浏览器的各类数据

**函数签名**:
```cpp
std::string Init_BroswerMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
);
```

**参数详解**:
- `params`: JSON参数，支持action字段指定提取类型
- `progressCallback`: 进度回调函数（可为nullptr）
- `taskId`: 任务唯一标识符
- `queryTaskControlCb`: 任务控制回调（可为nullptr）

**支持的action值**:
```json
{
  "action": "all"        // 提取所有数据（默认）
  "action": "password"   // 仅提取密码
  "action": "history"    // 仅提取历史记录
  "action": "downloads"  // 仅提取下载记录
  "action": "cookies"    // 仅提取Cookie
  "action": "bookmarks"  // 仅提取书签
  "action": "cache"      // 仅提取缓存文件
}
```

**返回值示例**:
```json
{
  "status": "success",
  "data": {
    "chrome_browsers": [
      {
        "browser_name": "Chrome",
        "profile_path": "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default",
        "passwords": [...],
        "history": [...],
        "downloads": [...],
        "cookies": [...],
        "bookmarks": [...],
        "caches": [...]
      }
    ],
    "firefox": {
      "passwords": [...],
      "history": [...],
      "downloads": [...],
      "cookies": [...],
      "bookmarks": [...],
      "caches": [...]
    },
    "ie": {
      "passwords": [...],
      "history": [...],
      "downloads": [...],
      "cookies": [...],
      "bookmarks": [...],
      "caches": [...]
    }
  },
  "statistics": {
    "total_passwords": 25,
    "total_history": 1500,
    "total_downloads": 50,
    "total_cookies": 800,
    "total_bookmarks": 100,
    "total_caches": 300,
    "scan_time": "2024-12-20 10:30:00",
    "scan_duration": "5.2秒"
  }
}
```

---

## 系统信息API

### Init_ProcessInfoMsg - 进程信息
**功能**: 获取系统进程信息

```cpp
std::string Init_ProcessInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
);
```

### Init_DriverInfoMsg - 驱动信息
**功能**: 获取系统驱动程序信息

```cpp
std::string Init_DriverInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
);
```

### Init_WifiInfoMsg - WiFi信息
**功能**: 获取WiFi配置和密码信息

```cpp
std::string Init_WifiInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
);
```

### Init_ServiceInfoMsg - 服务信息
**功能**: 获取Windows系统服务信息

```cpp
std::string Init_ServiceInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
);
```

### Init_ShareInfoMsg - 共享信息
**功能**: 获取系统共享文件夹信息

```cpp
std::string Init_ShareInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
);
```

---

## 数据结构说明

### 浏览器数据类型

#### 密码数据 (PasswordData)
```json
{
  "url": "https://example.com",
  "username": "<EMAIL>",
  "password": "decrypted_password",
  "create_time": "2024-12-20 10:30:00",
  "user_name": "张三",
  "browser_type": "Chrome"
}
```

#### 历史记录 (HistoryData)
```json
{
  "url": "https://example.com",
  "title": "Example Website",
  "visit_time": "2024-12-20 10:30:00",
  "visit_count": 5,
  "user_name": "张三",
  "browser_type": "Chrome"
}
```

#### 下载记录 (DownloadData)
```json
{
  "url": "https://example.com/file.zip",
  "file_path": "C:\\Users\\<USER>\\Downloads\\file.zip",
  "start_time": "2024-12-20 10:30:00",
  "end_time": "2024-12-20 10:35:00",
  "file_size": 1048576,
  "user_name": "张三",
  "browser_type": "Chrome"
}
```

#### Cookie数据 (CookieData)
```json
{
  "Cookie": "session_id=abc123",
  "Host": "example.com",
  "path": "/",
  "keyname": "session_id",
  "createdata": "2024-12-20 10:30:00",
  "user_name": "张三",
  "browser_type": "Chrome"
}
```

#### 书签数据 (BookmarkData)
```json
{
  "url": "https://example.com",
  "title": "Example Website",
  "date_added": "2024-12-20 10:30:00",
  "folder_path": "书签栏/工作",
  "user_name": "张三",
  "browser_type": "Chrome"
}
```

#### 缓存文件数据 (CacheFileData)
```json
{
  "url": "https://example.com/image.jpg",
  "browser_type": "Chrome",
  "local_file_path": "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Cache\\Cache_Data\\f_000001",
  "user_name": "张三",
  "file_size": 204800,
  "hit_count": 3,
  "create_time": "2024-12-20 10:30:00",
  "last_modified_time": "2024-12-20 10:30:00",
  "last_access_time": "2024-12-20 10:35:00",
  "content_type": "image/jpeg",
  "risk_level": "低",
  "matched_keywords": [],
  "is_suspicious": false,
  "check_result": "正常缓存文件"
}
```

---

## 回调函数

### 进度回调函数
```cpp
void ProgressCallback(const std::string& message, int progress) {
    // message: 当前操作描述
    // progress: 进度百分比 (0-100)
    printf("进度: %d%% - %s\n", progress, message.c_str());
}
```

### 任务控制回调函数
```cpp
bool TaskControlCallback(const std::string& taskId, int controlType) {
    // controlType: 0=查询是否取消, 1=查询是否暂停
    // 返回值: true=继续执行, false=停止/暂停
    
    if (controlType == 0) {
        // 检查是否需要取消任务
        return !shouldCancel;
    } else if (controlType == 1) {
        // 检查是否需要暂停任务
        return !shouldPause;
    }
    return true;
}
```

---

## 错误处理

### 常见错误代码
- `BROWSER_NOT_FOUND`: 未找到浏览器
- `DATABASE_ACCESS_ERROR`: 数据库访问失败
- `DECRYPTION_FAILED`: 解密失败
- `INSUFFICIENT_PRIVILEGES`: 权限不足
- `INVALID_PARAMETERS`: 参数无效
- `TASK_CANCELLED`: 任务被取消

### 错误返回格式
```json
{
  "status": "error",
  "error_code": "BROWSER_NOT_FOUND",
  "message": "未找到支持的浏览器",
  "details": "系统中未检测到Chrome、Firefox或IE浏览器"
}
```

---

## 使用注意事项

1. **权限要求**: 建议以管理员身份运行
2. **浏览器状态**: 建议在浏览器关闭状态下运行
3. **内存使用**: 大型缓存扫描可能消耗较多内存
4. **安全合规**: 仅用于合法的安全研究和数据恢复
5. **多用户支持**: 自动扫描系统中所有用户的数据

---

## 技术支持

如有技术问题或功能建议，请参考项目README.md文档或联系开发团队。
