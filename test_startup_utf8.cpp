#include "pch.h"
#include "include/StartupManager.h"
#include <iostream>
#include <string>

int main() {
    std::cout << u8"测试启动项功能的UTF-8编码修复..." << std::endl;

    try {
        // 创建启动项管理器实例
        StartupManager startupManager;

        if (!startupManager.Initialize()) {
            std::cerr << u8"初始化启动项管理器失败" << std::endl;
            return 1;
        }

        std::cout << u8"正在获取启动项信息..." << std::endl;

        // 先测试单个启动项获取，避免一次性处理太多数据
        auto registryItems = startupManager.GetRegistryStartupItems();
        std::cout << u8"注册表启动项数量: " << registryItems.size() << std::endl;

        if (!registryItems.empty()) {
            std::cout << u8"\n第一个注册表启动项信息:" << std::endl;
            const auto& item = registryItems[0];
            std::cout << u8"  名称: " << item.name << std::endl;
            std::cout << u8"  命令: " << item.command << std::endl;
            std::cout << u8"  位置: " << item.location << std::endl;
            std::cout << u8"  描述: " << item.description << std::endl;
            std::cout << u8"  公司: " << item.company << std::endl;
            std::cout << u8"  版本: " << item.version << std::endl;
        }

        // 测试文件夹启动项
        auto folderItems = startupManager.GetStartupFolderItems();
        std::cout << u8"\n启动文件夹项数量: " << folderItems.size() << std::endl;

        if (!folderItems.empty()) {
            std::cout << u8"\n第一个文件夹启动项信息:" << std::endl;
            const auto& item = folderItems[0];
            std::cout << u8"  名称: " << item.name << std::endl;
            std::cout << u8"  文件路径: " << item.file_path << std::endl;
            std::cout << u8"  位置: " << item.location << std::endl;
        }

        // 测试JSON序列化（这里最容易出现UTF-8错误）
        std::cout << u8"\n正在测试JSON序列化..." << std::endl;

        // 创建一个简单的测试JSON对象
        nlohmann::json testJson;
        testJson["test_message"] = u8"这是一个中文测试消息";
        testJson["registry_items_count"] = registryItems.size();
        testJson["folder_items_count"] = folderItems.size();

        if (!registryItems.empty()) {
            nlohmann::json firstItem;
            const auto& item = registryItems[0];
            firstItem["name"] = item.name;
            firstItem["command"] = item.command;
            firstItem["location"] = item.location;
            firstItem["description"] = item.description;
            firstItem["company"] = item.company;
            firstItem["version"] = item.version;
            testJson["first_registry_item"] = firstItem;
        }

        std::string jsonStr = testJson.dump(2);
        std::cout << u8"JSON序列化成功，数据大小: " << jsonStr.size() << u8" 字节" << std::endl;

        // 显示JSON数据
        std::cout << u8"\nJSON数据:" << std::endl;
        std::cout << jsonStr << std::endl;

        std::cout << u8"\n测试完成！UTF-8编码修复验证成功。" << std::endl;

        // 清理资源
        startupManager.Cleanup();

    } catch (const nlohmann::json::exception& e) {
        std::cerr << u8"JSON处理错误: " << e.what() << std::endl;
        std::cerr << u8"错误ID: " << e.id << std::endl;
        return 1;
    } catch (const std::exception& e) {
        std::cerr << u8"测试过程中发生错误: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
