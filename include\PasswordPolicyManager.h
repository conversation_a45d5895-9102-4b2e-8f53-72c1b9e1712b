﻿#pragma once
#include "PasswordPolicyData.h"
#include <vector>
#include <string>
#include <functional>
#include <windows.h>
#include <lm.h>
#include <ntsecapi.h>
#include <nlohmann/json.hpp>

#pragma comment(lib, "netapi32.lib")
#pragma comment(lib, "advapi32.lib")


class PasswordPolicyManager {
public:
    PasswordPolicyManager();
    ~PasswordPolicyManager();

    // 初始化密码策略管理器
    bool Initialize();

    // 清理资源
    void Cleanup();

    // 获取密码策略信息
    PasswordPolicyData GetPasswordPolicy();

    // 获取所有用户账户信息
    std::vector<UserAccountData> GetAllUserAccounts();

    // 获取密码策略统计信息
    PasswordPolicyStatistics GetPasswordPolicyStatistics();

    // 获取完整的密码策略信息并返回JSON格式
    nlohmann::json GetPasswordPolicyInfoAsJson();

    // 保存密码策略信息到JSON文件
    bool SavePasswordPolicyInfoToFile(const std::string& filename);


private:
    bool m_initialized;
    std::chrono::system_clock::time_point m_startTime;
    LSA_HANDLE m_lsaHandle;

    // 辅助函数
    std::string ConvertToString(const std::wstring& wstr);
    std::string ConvertToString(LPCWSTR wstr);
    std::wstring ConvertToWString(const std::string& str);
    std::string ConvertLsaUnicodeStringToString(const LSA_UNICODE_STRING& lsaString);

    // LSA策略操作
    bool InitializeLSA();
    void CleanupLSA();
    bool GetLSAPolicy(POLICY_INFORMATION_CLASS infoClass, PVOID* buffer);

    // 密码策略获取
    PasswordPolicyData GetLocalPasswordPolicy();
    PasswordPolicyData GetDomainPasswordPolicy();
    bool GetPasswordPolicyFromRegistry(PasswordPolicyData& policy);
    bool GetPasswordPolicyFromLSA(PasswordPolicyData& policy);
    bool GetPasswordPolicyFromNetAPI(PasswordPolicyData& policy);
    void SetDefaultPolicyValues(PasswordPolicyData& policy);

    // 账户锁定策略
    bool GetAccountLockoutPolicy(PasswordPolicyData& policy);
    bool GetKerberosPolicy(PasswordPolicyData& policy);

    // 用户账户信息获取
    std::vector<UserAccountData> GetLocalUserAccounts();
    std::vector<UserAccountData> GetDomainUserAccounts();
    UserAccountData GetUserAccountDetails(const std::string& username);
    bool GetUserAccountInfo(const std::string& username, UserAccountData& userData);

    // 用户权限和组信息
    std::vector<std::string> GetUserGroups(const std::string& username);
    std::vector<std::string> GetUserPrivileges(const std::string& username);
    std::string GetUserAccountType(DWORD userType);

    // 审核策略
    bool GetAuditPolicy(PasswordPolicyData& policy);
    bool GetAuditCategorySettings(PasswordPolicyData& policy);

    // 安全选项
    bool GetSecurityOptions(PasswordPolicyData& policy);
    bool GetLMCompatibilityLevel(PasswordPolicyData& policy);
    bool GetNTLMSettings(PasswordPolicyData& policy);

    // 用户权限分配
    bool GetUserRightsAssignment(PasswordPolicyData& policy);
    std::vector<std::string> GetUsersWithRight(const std::wstring& rightName);

    // 密码复杂性分析
    bool AnalyzePasswordComplexity(PasswordPolicyData& policy);
    std::vector<std::string> GetBannedPasswords();

    // 多因素认证检测
    bool DetectMFASettings(PasswordPolicyData& policy);
    std::vector<std::string> GetMFAMethods();
    bool IsBiometricEnabled();
    bool IsPINEnabled();

    // 域信息获取
    bool GetDomainInfo(PasswordPolicyData& policy);
    std::string GetDomainName();
    std::string GetDomainController();
    std::string GetAppliedGPO();

    // 安全分析
    std::vector<std::string> AnalyzePasswordPolicySecurity(const PasswordPolicyData& policy);
    std::string EvaluatePolicyStrength(const PasswordPolicyData& policy);
    double CalculateComplianceScore(const PasswordPolicyData& policy);
    std::vector<std::string> GenerateSecurityRecommendations(const PasswordPolicyData& policy);

    // 时间转换
    std::string ConvertFileTimeToString(const FILETIME& fileTime);
    std::string ConvertLargeIntegerToString(const LARGE_INTEGER& largeInt);

    // 错误处理
    std::string GetLastErrorString();
    std::string GetNTStatusString(NTSTATUS status);
    void LogError(const std::string& error);

    // WMI相关
    bool InitializeWMI();
    void CleanupWMI();
    std::string ExecuteWMIQuery(const std::string& query);

    // 网络API辅助
    bool IsComputerInDomain();
    std::string GetComputerDomain();
    bool GetNetUserInfo(const std::string& username, UserAccountData& userData);

    // 安全描述符和SID处理
    std::string ConvertSIDToString(PSID sid);
    bool GetAccountSID(const std::string& accountName, std::string& sidString);

    // 组策略信息
    std::vector<std::string> GetAppliedGPOs();
    std::string GetGPOSource(const std::string& settingName);

    // 密码历史和过期检查
    bool CheckPasswordExpiration(const std::string& username, UserAccountData& userData);
    int GetPasswordAge(const std::string& username);
    bool IsPasswordExpired(const std::string& username);

public:
    // 特殊账户检测 - 移到公共接口
    bool IsServiceAccount(const std::string& username);
    bool IsBuiltinAccount(const std::string& username);
    bool IsAdministratorAccount(const std::string& username);
    bool IsGuestAccount(const std::string& username);

    // 注册表操作 - 移到公共接口
    std::string ReadRegistryString(HKEY hKey, const std::string& subKey, const std::string& valueName);
    DWORD ReadRegistryDWORD(HKEY hKey, const std::string& subKey, const std::string& valueName);
    bool ReadRegistryBool(HKEY hKey, const std::string& subKey, const std::string& valueName);

    // 时间转换 - 移到公共接口
    std::string GetCurrentTimestamp();

private:

    // 登录限制和时间
    std::vector<std::string> GetLogonHours(const std::string& username);
    std::vector<std::string> GetLogonWorkstations(const std::string& username);

    // 密码策略合规性检查
    bool CheckPasswordPolicyCompliance(const UserAccountData& user, const PasswordPolicyData& policy);
    std::vector<std::string> GetNonCompliantUsers(const std::vector<UserAccountData>& users, const PasswordPolicyData& policy);
};
