#!/usr/bin/env python3
# 批量替换ScreensaverManager中的函数调用

import re

def fix_screensaver_calls(file_path):
    """批量替换ScreensaverManager中的函数调用"""
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换ConvertToString调用
    content = re.sub(r'ConvertToString\(', 'Utils::WStringToUTF8(', content)
    
    # 替换ConvertToWString调用
    content = re.sub(r'ConvertToWString\(', 'Utils::UTF8ToWString(', content)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已完成 {file_path} 的函数调用替换")

if __name__ == "__main__":
    fix_screensaver_calls("src/ScreensaverManager.cpp")
