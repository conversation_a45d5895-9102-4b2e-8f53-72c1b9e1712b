#include "pch.h"
#include "include/NetworkConnectionManager.h"
#include <iostream>
#include <string>
#include <nlohmann/json.hpp>

using json = nlohmann::json;

int main() {
    std::cout << u8"=== Testing Network Connection UTF-8 Encoding Fix ===" << std::endl;
    
    try {
        // 创建网络连接管理器实例
        NetworkConnectionManager networkManager;
        
        std::cout << u8"\nStep 1: Testing unified interface JSON output..." << std::endl;

        // Test unified interface
        std::string unifiedJsonString = networkManager.GetNetworkConnectionDataAsJsonString();
        std::cout << u8"Unified interface JSON data size: " << unifiedJsonString.size() << u8" bytes" << std::endl;
        
        if (unifiedJsonString.empty()) {
            std::cout << u8"⚠ 统一接口返回空数据" << std::endl;
        } else if (unifiedJsonString == "[]") {
            std::cout << u8"⚠ 统一接口返回空数组" << std::endl;
        } else {
            try {
                // 尝试解析JSON
                json unifiedData = json::parse(unifiedJsonString);
                std::cout << u8"✓ 统一接口JSON解析成功！" << std::endl;
                std::cout << u8"找到 " << unifiedData.size() << u8" 个网络连接记录" << std::endl;
                
                if (!unifiedData.empty()) {
                    const auto& firstRecord = unifiedData[0];
                    std::cout << u8"\n--- 第一条记录字段检查 ---" << std::endl;
                    
                    // 检查关键字段
                    std::vector<std::string> requiredFields = {
                        "protocol", "local_address", "local_port", 
                        "process_name", "process_path", "process_icon", "create_time"
                    };
                    
                    for (const auto& field : requiredFields) {
                        if (firstRecord.contains(field)) {
                            std::cout << u8"✓ " << field << u8": 存在" << std::endl;
                        } else {
                            std::cout << u8"✗ " << field << u8": 缺失" << std::endl;
                        }
                    }
                }
                
            } catch (const json::parse_error& e) {
                std::cout << u8"✗ 统一接口JSON解析失败: " << e.what() << std::endl;
                std::cout << u8"JSON前100字符: " << unifiedJsonString.substr(0, 100) << std::endl;
            }
        }
        
        std::cout << u8"\n步骤2: 测试传统接口JSON输出..." << std::endl;
        
        // 测试传统接口
        std::string traditionalJsonString = networkManager.GetAllNetworkConnectionsAsJsonString();
        std::cout << u8"传统接口JSON数据大小: " << traditionalJsonString.size() << u8" 字节" << std::endl;
        
        if (traditionalJsonString.empty()) {
            std::cout << u8"⚠ 传统接口返回空数据" << std::endl;
        } else if (traditionalJsonString == "[]") {
            std::cout << u8"⚠ 传统接口返回空数组" << std::endl;
        } else {
            try {
                // 尝试解析JSON
                json traditionalData = json::parse(traditionalJsonString);
                std::cout << u8"✓ 传统接口JSON解析成功！" << std::endl;
                std::cout << u8"找到 " << traditionalData.size() << u8" 个网络连接记录" << std::endl;
                
                if (!traditionalData.empty()) {
                    const auto& firstRecord = traditionalData[0];
                    std::cout << u8"\n--- 传统接口字段检查 ---" << std::endl;
                    
                    // 检查关键字段
                    std::vector<std::string> traditionalFields = {
                        "protocol", "localAddress", "localPort", 
                        "processName", "processPath", "iconBase64", 
                        "processCreateTime", "connectionCreateTime"
                    };
                    
                    for (const auto& field : traditionalFields) {
                        if (firstRecord.contains(field)) {
                            std::cout << u8"✓ " << field << u8": 存在" << std::endl;
                        } else {
                            std::cout << u8"✗ " << field << u8": 缺失" << std::endl;
                        }
                    }
                }
                
            } catch (const json::parse_error& e) {
                std::cout << u8"✗ 传统接口JSON解析失败: " << e.what() << std::endl;
                std::cout << u8"JSON前100字符: " << traditionalJsonString.substr(0, 100) << std::endl;
            }
        }
        
        std::cout << u8"\n步骤3: 测试直接数据获取..." << std::endl;
        
        // 直接获取网络连接数据
        std::vector<NetworkConnectionData> connections = networkManager.GetNetworkConnectionData();
        std::cout << u8"直接获取到 " << connections.size() << u8" 个网络连接" << std::endl;
        
        if (!connections.empty()) {
            const auto& firstConn = connections[0];
            std::cout << u8"\n--- 第一个连接详细信息 ---" << std::endl;
            std::cout << u8"协议: " << firstConn.protocol << std::endl;
            std::cout << u8"本地地址: " << firstConn.local_address << u8":" << firstConn.local_port << std::endl;
            std::cout << u8"进程名: " << firstConn.process_name << std::endl;
            std::cout << u8"进程路径: " << firstConn.process_path << std::endl;
            std::cout << u8"连接创建时间: " << firstConn.create_time << std::endl;
            std::cout << u8"图标数据长度: " << firstConn.process_icon.length() << u8" 字符" << std::endl;
        }
        
        std::cout << u8"\n=== UTF-8编码修复测试完成 ===" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << u8"测试过程中发生错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
