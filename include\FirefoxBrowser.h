#pragma once
#include "BrowserDataExtractor.h"
#include <functional>
#include <filesystem>
//#include <expected>
#include <optional>

// NSS���Ͷ���
typedef enum {
    siBuffer = 0,
    siClearDataBuffer = 1,
    siCipherDataBuffer = 2,
    siDERCertBuffer = 3,
    siEncodedCertBuffer = 4,
    siDERNameBuffer = 5,
    siEncodedNameBuffer = 6,
    siAsciiNameString = 7,
    siAsciiString = 8,
    siDEROID = 9,
    siUnsignedInteger = 10,
    siUTCTime = 11,
    siGeneralizedTime = 12,
    siVisibleString = 13,
    siUTF8String = 14,
    siBMPString = 15
} SECItemType;

typedef struct {
    SECItemType type;
    unsigned char* data;
    unsigned int len;
} SECItem;

typedef enum {
    SECWouldBlock = -2,
    SECFailure = -1,
    SECSuccess = 0
} SECStatus;

typedef int PRIntn;
typedef PRIntn PRBool;
typedef unsigned int PRUint32;

#define SECMOD_DB "secmod.db"
#define NSS_INIT_READONLY 0x1
#define PR_TRUE 1
#define PR_FALSE 0

// NSS�������Ͷ���
using NSS_Initialize = SECStatus(const char*, const char*, const char*, const char*, PRUint32);
using NSS_Shutdown = SECStatus(void);
using SECITEM_AllocItem = SECItem * (void*, SECItem*, unsigned int);
using SECITEM_ZfreeItem = void(SECItem*, PRBool);
using PK11SDR_Decrypt = SECStatus(SECItem*, SECItem*, void*);

class FirefoxBrowser : public IBrowser {
public:
    typedef BOOL(WINAPI* SetDllDirectoryWPtr)(LPCWSTR);

    FirefoxBrowser();
    virtual ~FirefoxBrowser();

     std::vector<PasswordData> GetPasswords() override;
     std::vector<HistoryData> GetHistory() override;
     std::vector<DownloadData> GetDownloads() override;
     std::vector<CookieData> GetCookie() override;
     std::vector<BookmarkData> GetBookmarks() override;
     std::vector<CacheFileData> GetBroswerCache() override;

protected:
    virtual std::wstring GetProfilePath() override;

private:
    std::wstring m_profilePath;
    HMODULE m_nssModule;
    SetDllDirectoryWPtr m_pSetDllDirectoryW;

    // NSS����ָ��
    std::function<NSS_Initialize> NSS_InitializePtr;
    std::function<NSS_Shutdown> NSS_ShutdownPtr;
    std::function<SECITEM_AllocItem> SECITEM_AllocItemPtr;
    std::function<SECITEM_ZfreeItem> SECITEM_ZfreeItemPtr;
    std::function<PK11SDR_Decrypt> PK11SDR_DecryptPtr;

    // ����֧�ֵ�Firefox�汾����
    static const std::vector<std::wstring> firefox_versions;

    // ����NSS��
    std::optional<std::filesystem::path> FindNssLibrary();

    // ��ʼ��NSS��
    bool InitializeNss(const std::filesystem::path& profile_path);

    // ��������
    std::string DecryptData(const std::string& ciphertext_b64);

    // 缓存相关的私有方法
    void ScanFirefoxCacheDirectory(const std::wstring& cachePath, std::vector<CacheFileData>& caches);
    std::wstring GetContentTypeFromUrl(const std::wstring& url);
    std::wstring AnalyzeFirefoxRiskLevel(const std::wstring& url, const std::wstring& filePath);
    std::vector<std::wstring> CheckFirefoxSensitiveKeywords(const std::wstring& url, const std::wstring& filePath);

    // 多用户支持方法
    std::vector<std::wstring> GetAllFirefoxUserProfiles();
    void ScanUserFirefoxCacheDirectory(const std::wstring& userPath, const std::wstring& userName, std::vector<CacheFileData>& caches);
    void GetUserPasswords(const std::wstring& userPath, const std::wstring& userName, std::vector<PasswordData>& passwords);
    void GetUserHistory(const std::wstring& userPath, const std::wstring& userName, std::vector<HistoryData>& history);
    void GetUserDownloads(const std::wstring& userPath, const std::wstring& userName, std::vector<DownloadData>& downloads);
    void GetUserCookies(const std::wstring& userPath, const std::wstring& userName, std::vector<CookieData>& cookies);
    void GetUserBookmarks(const std::wstring& userPath, const std::wstring& userName, std::vector<BookmarkData>& bookmarks);
};
