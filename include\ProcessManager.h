﻿#pragma once
#include "ProcessData.h"
#include <vector>
#include <string>
#include <functional>
#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <tlhelp32.h>
#include <psapi.h>
#include <nlohmann/json.hpp>


class ProcessManager {
public:
    ProcessManager();
    ~ProcessManager();

    // 初始化进程管理器
    bool Initialize();

    // 清理资源
    void Cleanup();

    // 获取所有运行中的进程（默认使用快速版本）
    std::vector<ProcessData> GetAllProcesses();

    // 快速版本：只获取基本信息（PID、名称、内存）
    std::vector<ProcessData> GetAllProcessesFast();

    // 详细版本：获取所有信息（包括用户、CPU等，较慢）
    std::vector<ProcessData> GetAllProcessesDetailed();

    // 获取完整的进程信息并返回JSON格式
    nlohmann::json GetProcessesInfoAsJson();

    // 获取快速进程信息并返回JSON格式
    nlohmann::json GetProcessesInfoAsJsonFast();

    // 保存进程信息到JSON文件
    bool SaveProcessesInfoToFile(const std::string& filename);

    // 设置是否使用快速模式（默认true）
    void SetFastMode(bool useFastMode) { m_useFastMode = useFastMode; }
    bool IsFastMode() const { return m_useFastMode; }


private:
    bool m_initialized;
    bool m_useFastMode;                   // 是否使用快速模式

    // 辅助函数
    std::string ConvertToString(const std::wstring& wstr);
    std::wstring ConvertToWString(const std::string& str);

    // 获取进程详细信息
    ProcessData GetProcessDetails(DWORD processId, const std::string& processName);

    // 格式化内存大小为MB字符串
    std::string FormatMemorySize(SIZE_T bytes);

    // 新增的进程信息获取方法
    std::string GetProcessPath(HANDLE processHandle);
    std::string GetProcessCreateTime(HANDLE processHandle);
    std::string GetProcessIcon(const std::string& processPath);

    // 图标处理相关方法（注意：部分方法已移至Utils类中）
    std::string ExtractProcessIconAsBase64(const std::string& processPath, int iconSize = 32);
    // ExtractIconToBMP - 已移至Utils类，使用Utils::ExtractIconToBMP
    std::string ExtractIconInfo(const std::string& processPath);
    std::string ExtractIconAsBase64(const std::string& processPath);
    std::string GetIconFilePath(const std::string& processPath);
    std::string GetIconTypeByPath(const std::string& processPath);
    std::string ConvertIconToBase64(HICON hIcon);
    // EncodeBase64 - 已移至Utils类，使用Utils::Base64Encode

    // 保留的旧方法（用于详细模式）
    std::string GetProcessUserName(HANDLE processHandle);
    std::string CalculateProcessCpuUsage(HANDLE processHandle);
    std::string GetProcessDiskUsage(DWORD processId);
    std::string GetProcessNetworkUsage(DWORD processId);

    // 辅助函数：将FILETIME转换为64位整数
    ULONGLONG FileTimeToULongLong(const FILETIME& ft);
};
