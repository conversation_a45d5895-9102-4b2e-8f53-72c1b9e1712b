#pragma once
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <wincrypt.h>
#include <shlobj.h>
#include <ctime>

// 浏览器配置结构体
struct BrowserConfig {
    std::wstring name;
    std::wstring profile_path;
};

// 定义数据结构
struct PasswordData {
    std::wstring url;
    std::wstring username;
    std::wstring password;
    std::wstring create_time;
    std::wstring user_name;      // 所属用户
    std::wstring browser_type;   // 浏览器类型
};

struct HistoryData {
    std::wstring url;
    std::wstring title;
    std::wstring visit_time;
    int visit_count;
    std::wstring user_name;      // 所属用户
    std::wstring browser_type;   // 浏览器类型
};

struct DownloadData {
    std::wstring url;
    std::wstring file_path;
    std::wstring start_time;
    std::wstring end_time;
    __int64 file_size;
    std::wstring file_icon;      // 文件图标的Base64编码
    std::wstring user_name;      // 所属用户
    std::wstring browser_type;   // 浏览器类型
};

struct CookieData {
    std::wstring Cookie;
    std::wstring Host;
    std::wstring path;
    std::wstring keyname;        // Cookie名称/键名
    std::wstring createdata;     // 创建时间
    std::wstring user_name;      // 所属用户
    std::wstring browser_type;   // 浏览器类型
};

struct BookmarkData {
    std::wstring url;
    std::wstring title;
    std::wstring date_added;
    std::wstring folder_path; // 书签所在文件夹路径
    std::wstring user_name;      // 所属用户
    std::wstring browser_type;   // 浏览器类型
};

// 缓存文件数据结构
struct CacheFileData {
    std::wstring url;                // 缓存文件对应的URL
    std::wstring browser_type;       // 浏览器类型
    std::wstring local_file_path;    // 缓存文件路径
    std::wstring user_name;          // 所属用户
    __int64 file_size = 0;           // 文件大小
    int hit_count = 0;               // 命中数/访问次数
    std::wstring create_time;        // 创建时间
    std::wstring last_modified_time; // 最后修改时间
    std::wstring last_access_time;   // 最后访问时间
    std::wstring content_type;       // 内容类型
    std::wstring risk_level;         // 风险级别：低、中、高
    std::vector<std::wstring> matched_keywords; // 匹配到的敏感关键字
    bool is_suspicious = false;      // 是否可疑
    std::wstring check_result;       // 检查结论
};

// 浏览器基类
class IBrowser {
public:
    virtual ~IBrowser() = default;
    virtual std::vector<PasswordData> GetPasswords() = 0;
    virtual std::vector<HistoryData> GetHistory() = 0;
    virtual std::vector<DownloadData> GetDownloads() = 0;
    virtual std::wstring GetProfilePath() = 0;
    virtual std::vector<CookieData> GetCookie() = 0;
    virtual std::vector<BookmarkData> GetBookmarks() = 0;
    virtual std::vector<CacheFileData> GetBroswerCache() = 0;
};