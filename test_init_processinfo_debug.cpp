#include "pch.h"
#include "include/Init_BroswerMessage.h"
#include "include/ProcessInfoManager.h"
#include <iostream>
#include <string>
#include <windows.h>

// 进度回调函数 - 详细版本
void DetailedProgressCallback(const std::string& message, int progress) {
    std::cout << u8"[" << GetTickCount() << u8"] 进度 [" << progress << "%]: " << message << std::endl;
    std::cout.flush(); // 强制刷新输出缓冲区
}

// 任务控制回调函数
bool QueryTaskControlCallback(const std::string& taskId, int checkType) {
    return false; // 不取消任务
}

// 分步测试函数
void TestProcessInfoStepByStep() {
    std::cout << u8"\n=== 分步测试 ProcessInfoManager 组件 ===" << std::endl;

    try {
        std::cout << u8"步骤1: 创建 ProcessInfoManager 实例..." << std::endl;
        ProcessInfoManager processManager;
        std::cout << u8"✓ ProcessInfoManager 创建成功" << std::endl;

        std::cout << u8"\n步骤2: 测试 GetAllProcesses()..." << std::endl;
        std::vector<ProcessInfo> processes = processManager.GetAllProcesses();
        std::cout << u8"✓ GetAllProcesses 成功，发现 " << processes.size() << u8" 个进程" << std::endl;

        std::cout << u8"\n步骤3: 显示前5个进程信息..." << std::endl;
        for (size_t i = 0; i < std::min(processes.size(), size_t(5)); ++i) {
            const auto& proc = processes[i];
            std::wcout << u8"  进程 " << (i+1) << u8": PID=" << proc.pid 
                      << u8", 名称=" << proc.processName.c_str() << std::endl;
        }

        std::cout << u8"\n步骤4: 测试 GetAllProcessesDetailed()..." << std::endl;
        json detailedInfo = processManager.GetAllProcessesDetailed();
        std::cout << u8"✓ GetAllProcessesDetailed 成功，返回 " << detailedInfo.size() << u8" 条记录" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << u8"✗ 分步测试失败: " << e.what() << std::endl;
    } catch (...) {
        std::cerr << u8"✗ 分步测试失败: 未知异常" << std::endl;
    }
}

int main() {
    std::cout << u8"=== Init_ProcessInfoMsg Windows XP 调试测试 ===" << std::endl;
    std::cout << u8"当前时间戳: " << GetTickCount() << std::endl;
    
    // 显示系统信息
    OSVERSIONINFO osvi;
    ZeroMemory(&osvi, sizeof(OSVERSIONINFO));
    osvi.dwOSVersionInfoSize = sizeof(OSVERSIONINFO);
    if (GetVersionEx(&osvi)) {
        std::cout << u8"操作系统版本: " << osvi.dwMajorVersion << "." << osvi.dwMinorVersion 
                  << u8" Build " << osvi.dwBuildNumber << std::endl;
    }

    // 先进行分步测试
    TestProcessInfoStepByStep();

    std::cout << u8"\n=== 开始完整接口测试 ===" << std::endl;

    try {
        std::string params = "{}";
        std::string taskId = "debug_test_001";

        std::cout << u8"调用 Init_ProcessInfoMsg..." << std::endl;
        std::cout << u8"参数: " << params << std::endl;
        std::cout << u8"任务ID: " << taskId << std::endl;

        // 设置异常处理
        __try {
            std::string result = Init_ProcessInfoMsg(
                params,
                DetailedProgressCallback,
                taskId,
                QueryTaskControlCallback
            );

            std::cout << u8"\n✓ Init_ProcessInfoMsg 调用成功！" << std::endl;
            std::cout << u8"返回数据大小: " << result.size() << u8" 字节" << std::endl;

            // 检查返回的JSON是否有效
            if (result.find("\"status\"") != std::string::npos) {
                std::cout << u8"✓ 返回数据包含状态字段" << std::endl;
            }
            if (result.find("\"data\"") != std::string::npos) {
                std::cout << u8"✓ 返回数据包含数据字段" << std::endl;
            }

        } __except(EXCEPTION_EXECUTE_HANDLER) {
            DWORD exceptionCode = GetExceptionCode();
            std::cerr << u8"✗ 发生系统异常，异常代码: 0x" << std::hex << exceptionCode << std::endl;
            
            switch (exceptionCode) {
                case EXCEPTION_ACCESS_VIOLATION:
                    std::cerr << u8"  -> 访问违规异常 (可能是内存访问错误)" << std::endl;
                    break;
                case EXCEPTION_STACK_OVERFLOW:
                    std::cerr << u8"  -> 栈溢出异常" << std::endl;
                    break;
                case EXCEPTION_INT_DIVIDE_BY_ZERO:
                    std::cerr << u8"  -> 除零异常" << std::endl;
                    break;
                default:
                    std::cerr << u8"  -> 其他系统异常" << std::endl;
                    break;
            }
            return 1;
        }

    } catch (const std::exception& e) {
        std::cerr << u8"✗ C++ 异常: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << u8"✗ 未知 C++ 异常" << std::endl;
        return 1;
    }

    std::cout << u8"\n=== 测试完成 ===" << std::endl;
    std::cout << u8"按任意键退出..." << std::endl;
    system("pause");
    return 0;
}
