<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BMP Base64图标测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .icon-display {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 10px 0;
        }
        .icon-img {
            border: 2px solid #ccc;
            padding: 5px;
            background: white;
        }
        .icon-info {
            flex: 1;
        }
        .base64-input {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .test-button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #2980b9;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ BMP Base64图标测试工具</h1>
        
        <div class="test-section">
            <div class="test-title">📋 测试您的Base64数据</div>
            <textarea id="base64Input" class="base64-input" placeholder="请粘贴您的Base64数据...">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</textarea>
            <button class="test-button" onclick="testBase64()">🔍 测试Base64数据</button>
            <button class="test-button" onclick="clearInput()">🗑️ 清空</button>
            <div id="testResult"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🖼️ 图标显示测试</div>
            <div id="iconDisplay"></div>
        </div>

        <div class="test-section">
            <div class="test-title">📊 数据分析</div>
            <div id="dataAnalysis"></div>
        </div>

        <div class="test-section">
            <div class="test-title">💡 使用说明</div>
            <div class="info">
                <p><strong>功能说明：</strong></p>
                <ul>
                    <li>将您的Base64数据粘贴到上方文本框中</li>
                    <li>点击"测试Base64数据"按钮进行验证</li>
                    <li>系统会自动检测数据格式并尝试显示图标</li>
                    <li>支持BMP、PNG、JPEG等常见图片格式</li>
                </ul>
                <p><strong>注意事项：</strong></p>
                <ul>
                    <li>确保Base64数据完整且格式正确</li>
                    <li>BMP格式应以"Qk"开头（Base64编码后）</li>
                    <li>如果显示失败，请检查数据是否包含完整的文件头</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function testBase64() {
            const base64Data = document.getElementById('base64Input').value.trim();
            const resultDiv = document.getElementById('testResult');
            const iconDiv = document.getElementById('iconDisplay');
            const analysisDiv = document.getElementById('dataAnalysis');
            
            if (!base64Data) {
                resultDiv.innerHTML = '<div class="error">❌ 请输入Base64数据</div>';
                return;
            }
            
            try {
                // 分析Base64数据
                analyzeBase64Data(base64Data, analysisDiv);
                
                // 尝试显示图标
                displayIcon(base64Data, iconDiv);
                
                resultDiv.innerHTML = '<div class="success">✅ Base64数据解析成功</div>';
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 解析失败: ${error.message}</div>`;
                iconDiv.innerHTML = '';
                analysisDiv.innerHTML = '';
            }
        }
        
        function analyzeBase64Data(base64Data, container) {
            const dataLength = base64Data.length;
            const estimatedSize = Math.floor(dataLength * 3 / 4);
            
            // 解码前几个字节来分析格式
            const binaryString = atob(base64Data.substring(0, Math.min(base64Data.length, 100)));
            const firstBytes = [];
            for (let i = 0; i < Math.min(binaryString.length, 16); i++) {
                firstBytes.push(binaryString.charCodeAt(i).toString(16).padStart(2, '0').toUpperCase());
            }
            
            let formatInfo = '未知格式';
            if (firstBytes.length >= 2) {
                if (firstBytes[0] === '42' && firstBytes[1] === '4D') {
                    formatInfo = '✅ BMP格式 (标准Windows位图)';
                } else if (firstBytes.length >= 4 && firstBytes[0] === '89' && firstBytes[1] === '50' && firstBytes[2] === '4E' && firstBytes[3] === '47') {
                    formatInfo = '✅ PNG格式';
                } else if (firstBytes.length >= 3 && firstBytes[0] === 'FF' && firstBytes[1] === 'D8' && firstBytes[2] === 'FF') {
                    formatInfo = '✅ JPEG格式';
                } else {
                    formatInfo = '⚠️ 未识别的格式或原始数据';
                }
            }
            
            container.innerHTML = `
                <div class="info">
                    <h4>📊 数据分析结果</h4>
                    <p><strong>Base64长度:</strong> ${dataLength} 字符</p>
                    <p><strong>估计文件大小:</strong> ${estimatedSize} 字节</p>
                    <p><strong>文件格式:</strong> ${formatInfo}</p>
                    <p><strong>前16字节(十六进制):</strong> ${firstBytes.join(' ')}</p>
                </div>
            `;
        }
        
        function displayIcon(base64Data, container) {
            // 尝试不同的MIME类型
            const mimeTypes = ['image/bmp', 'image/png', 'image/jpeg', 'image/gif'];
            
            container.innerHTML = '';
            
            mimeTypes.forEach(mimeType => {
                const dataUrl = `data:${mimeType};base64,${base64Data}`;
                const iconDiv = document.createElement('div');
                iconDiv.className = 'icon-display';
                
                const img = document.createElement('img');
                img.className = 'icon-img';
                img.style.width = '64px';
                img.style.height = '64px';
                img.src = dataUrl;
                
                const info = document.createElement('div');
                info.className = 'icon-info';
                info.innerHTML = `
                    <strong>格式:</strong> ${mimeType}<br>
                    <strong>状态:</strong> <span id="status-${mimeType.replace('/', '-')}">加载中...</span>
                `;
                
                img.onload = function() {
                    document.getElementById(`status-${mimeType.replace('/', '-')}`).innerHTML = 
                        `<span style="color: green;">✅ 加载成功 (${img.naturalWidth}x${img.naturalHeight})</span>`;
                };
                
                img.onerror = function() {
                    document.getElementById(`status-${mimeType.replace('/', '-')}`).innerHTML = 
                        '<span style="color: red;">❌ 加载失败</span>';
                    img.style.display = 'none';
                };
                
                iconDiv.appendChild(img);
                iconDiv.appendChild(info);
                container.appendChild(iconDiv);
            });
        }
        
        function clearInput() {
            document.getElementById('base64Input').value = '';
            document.getElementById('testResult').innerHTML = '';
            document.getElementById('iconDisplay').innerHTML = '';
            document.getElementById('dataAnalysis').innerHTML = '';
        }
        
        // 页面加载时自动测试默认数据
        window.onload = function() {
            testBase64();
        };
    </script>
</body>
</html>
