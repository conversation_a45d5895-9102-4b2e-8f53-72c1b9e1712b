#define WINVER 0x0501
#define _WIN32_WINNT 0x0501
#define _WIN32_IE 0x0600
#define NTDDI_VERSION 0x05010000

#include "pch.h"
#include "IEBrowser.h"
#include "ChromeBrowser.h"
#include "Utils.h"
#include <windows.h>
#include <wincred.h>
#include <shlobj.h>
#include <esent.h>
#include <filesystem>
#include <fstream>
#include "sqlite3.h"
#include <wininet.h>
#include <iostream>
#include <vector>
#include <string>
#include <sstream>
#include <cstdlib>   // malloc/free
#include <functional>

#pragma comment(lib, "Wininet.lib")

#ifndef CACHE_ENTRY_TYPE_COOKIE
#define CACHE_ENTRY_TYPE_COOKIE 0x00000020
#endif

namespace fs = std::filesystem;

IEBrowser::IEBrowser() {
    WCHAR localAppData[MAX_PATH];
    if (SUCCEEDED(SHGetFolderPathW(NULL, CSIDL_LOCAL_APPDATA, NULL, 0, localAppData))) {
        m_profilePath = std::wstring(localAppData);
    }
}

IEBrowser::~IEBrowser() {
}

std::wstring IEBrowser::GetProfilePath() {
    return m_profilePath;
}

std::vector<PasswordData> IEBrowser::GetPasswords() {
    std::vector<PasswordData> passwords;

    printf("Starting IE password extraction for all users...\n");

    try {
        // 获取所有用户配置文件路径
        std::vector<std::wstring> userProfiles = GetAllIEUserProfiles();

        if (userProfiles.empty()) {
            printf("No user profiles found, falling back to current user\n");
            // 回退到当前用户
            std::wstring currentUser = GetCurrentUserName();
            passwords = GetCredentialPasswords();
            // 为当前用户的密码设置用户名和浏览器类型
            for (auto& pwd : passwords) {
                pwd.user_name = currentUser;
                pwd.browser_type = L"Internet Explorer";
            }
        } else {
            // 扫描所有用户的IE密码
            for (const auto& userPath : userProfiles) {
                std::wstring userName = fs::path(userPath).filename().wstring();
                printf("Scanning IE passwords for user: %ls\n", userName.c_str());
                GetUserPasswords(userPath, userName, passwords);
            }
        }

        printf("Successfully retrieved %zu IE password entries from all users\n", passwords.size());
    }
    catch (const std::exception& e) {
        printf("Error getting IE passwords: %s\n", e.what());
    }
    catch (...) {
        printf("Unknown error occurred while getting IE passwords\n");
    }

    return passwords;
}

std::vector<PasswordData> IEBrowser::GetCredentialPasswords() {
    std::vector<PasswordData> passwords;

    DWORD count;
    PCREDENTIALW* credentials;

    // ���Զ�����ܵ�ƾ��ǰ׺
    const wchar_t* prefixes[] = {
        L"Microsoft_WinInet_*",  // ��׼IEǰ׺
        L"IE:*",                 // �ɰ�IEǰ׺
        L"*"                     // ����ƾ��
    };

    for (const wchar_t* prefix : prefixes) {
        if (CredEnumerateW(prefix, 0, &count, &credentials)) {
            for (DWORD i = 0; i < count; i++) {
                PCREDENTIALW cred = credentials[i];
                if (cred->Type == CRED_TYPE_GENERIC || cred->Type == CRED_TYPE_DOMAIN_PASSWORD) {
                    std::wstring targetName = cred->TargetName;

                    // ����Ƿ���IE��ص�ƾ��
                    if (targetName.find(L"Microsoft_WinInet_") == 0 ||
                        targetName.find(L"IE:") == 0 ||
                        targetName.find(L"http://") == 0 ||
                        targetName.find(L"https://") == 0) {

                        PasswordData pwd;

                        // ������ͬǰ׺��URL
                        if (targetName.find(L"Microsoft_WinInet_") == 0) {
                            pwd.url = targetName.substr(18);
                        }
                        else if (targetName.find(L"IE:") == 0) {
                            pwd.url = targetName.substr(3);
                        }
                        else {
                            pwd.url = targetName;
                        }

                        // ��ȡ�û���
                        pwd.username = cred->UserName ? std::wstring(cred->UserName) : L"";

                        // ��ȡ����
                        if (cred->CredentialBlobSize > 0) {
                            if (cred->Type == CRED_TYPE_GENERIC) {
                                pwd.password = std::wstring((wchar_t*)cred->CredentialBlob,
                                    cred->CredentialBlobSize / sizeof(wchar_t));
                            }
                            else {
                                // ����DOMAIN_PASSWORD���ͣ�CredentialBlob��ANSI�ַ���
                                std::string ansiPass((char*)cred->CredentialBlob, cred->CredentialBlobSize);
                                pwd.password = Utils::UTF8ToWString(ansiPass);
                            }
                        }

                        // ��ȡ����ʱ��
                        SYSTEMTIME st;
                        FileTimeToSystemTime(&cred->LastWritten, &st);
                        wchar_t timeStr[32];
                        swprintf_s(timeStr, L"%04d-%02d-%02d %02d:%02d:%02d",
                            st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
                        pwd.create_time = timeStr;

                        passwords.push_back(pwd);
                    }
                }
            }
            CredFree(credentials);
        }
    }

    printf("Found %zu IE passwords\n", passwords.size());
    return passwords;
}

std::vector<HistoryData> IEBrowser::GetHistory() {
    std::vector<HistoryData> history;

    printf("Starting IE history extraction for all users...\n");

    try {
        // 获取所有用户配置文件路径
        std::vector<std::wstring> userProfiles = GetAllIEUserProfiles();

        if (userProfiles.empty()) {
            printf("No user profiles found, falling back to current user\n");
            // 回退到当前用户
            std::wstring currentUser = GetCurrentUserName();
            history = GetRegistryHistory();
            // 为当前用户的历史记录设置用户名和浏览器类型
            for (auto& hist : history) {
                hist.user_name = currentUser;
                hist.browser_type = L"Internet Explorer";
            }
        } else {
            // 扫描所有用户的IE历史记录
            for (const auto& userPath : userProfiles) {
                std::wstring userName = fs::path(userPath).filename().wstring();
                printf("Scanning IE history for user: %ls\n", userName.c_str());
                GetUserHistory(userPath, userName, history);
            }
        }

        printf("Successfully retrieved %zu IE history entries from all users\n", history.size());
    }
    catch (const std::exception& e) {
        printf("Error getting IE history: %s\n", e.what());
    }
    catch (...) {
        printf("Unknown error occurred while getting IE history\n");
    }

    return history;
}

std::vector<HistoryData> IEBrowser::GetRegistryHistory() {
    std::vector<HistoryData> history;

    // ��TypedURLs��ȡ��ʷ��¼
    GetTypedURLs(history);

    printf("Found %zu IE history records\n", history.size());
    return history;
}

void IEBrowser::GetTypedURLs(std::vector<HistoryData>& history) {
    HKEY hKey;
    if (RegOpenKeyExW(HKEY_CURRENT_USER,
        L"Software\\Microsoft\\Internet Explorer\\TypedURLs",
        0, KEY_READ, &hKey) != ERROR_SUCCESS) {
        printf("Failed to open IE TypedURLs registry key\n");
        return;
    }

    DWORD index = 0;
    while (true) {
        WCHAR valueName[32];
        DWORD valueNameSize = 32;
        WCHAR valueData[MAX_PATH];
        DWORD valueDataSize = sizeof(valueData);
        DWORD type;

        LONG result = RegEnumValueW(hKey, index++, valueName, &valueNameSize,
            NULL, &type, (LPBYTE)valueData, &valueDataSize);

        if (result == ERROR_NO_MORE_ITEMS) break;
        if (result != ERROR_SUCCESS) continue;

        if (type == REG_SZ) {
            HistoryData data;
            data.url = std::wstring(valueData);
            data.title = L""; // TypedURLs����������

            // ��ȡ������ʱ��
            FILETIME ft = GetLastVisitTime(data.url);
            SYSTEMTIME st;
            FileTimeToSystemTime(&ft, &st);
            wchar_t timeStr[32];
            swprintf_s(timeStr, L"%04d-%02d-%02d %02d:%02d:%02d",
                st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
            data.visit_time = timeStr;

            data.visit_count = 1; // TypedURLs���������ʴ���

            history.push_back(data);
        }
    }

    RegCloseKey(hKey);
}

FILETIME IEBrowser::GetLastVisitTime(const std::wstring& url) {
    FILETIME ft = { 0 };

    HKEY hKey;
    std::wstring keyPath = L"Software\\Microsoft\\Internet Explorer\\TypedURLsTime";
    if (RegOpenKeyExW(HKEY_CURRENT_USER, keyPath.c_str(), 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        DWORD dataSize = sizeof(FILETIME);
        RegQueryValueExW(hKey, url.c_str(), NULL, NULL, (LPBYTE)&ft, &dataSize);
        RegCloseKey(hKey);
    }

    return ft;
}

std::vector<DownloadData> IEBrowser::GetDownloads() {
    std::vector<DownloadData> downloads;

    printf("Starting IE downloads extraction for all users...\n");

    try {
        // 获取所有用户配置文件路径
        std::vector<std::wstring> userProfiles = GetAllIEUserProfiles();

        if (userProfiles.empty()) {
            printf("No user profiles found, falling back to current user\n");
            // 回退到当前用户
            std::wstring currentUser = GetCurrentUserName();
            downloads = GetRegistryDownloads();
            // 为当前用户的下载记录设置用户名和浏览器类型
            for (auto& dl : downloads) {
                dl.user_name = currentUser;
                dl.browser_type = L"Internet Explorer";
            }
        } else {
            // 扫描所有用户的IE下载记录
            for (const auto& userPath : userProfiles) {
                std::wstring userName = fs::path(userPath).filename().wstring();
                printf("Scanning IE downloads for user: %ls\n", userName.c_str());
                GetUserDownloads(userPath, userName, downloads);
            }
        }

        printf("Successfully retrieved %zu IE download entries from all users\n", downloads.size());
    }
    catch (const std::exception& e) {
        printf("Error getting IE downloads: %s\n", e.what());
    }
    catch (...) {
        printf("Unknown error occurred while getting IE downloads\n");
    }

    return downloads;
}

std::vector<CookieData> IEBrowser::GetCookie()
{
    std::vector<CookieData> cookies;

    printf("Starting IE cookies extraction for all users...\n");

    try {
        // 获取所有用户配置文件路径
        std::vector<std::wstring> userProfiles = GetAllIEUserProfiles();

        if (userProfiles.empty()) {
            printf("No user profiles found, falling back to current user\n");
            // 回退到当前用户
            std::wstring currentUser = GetCurrentUserName();

            // 获取所有 URL 列表
            auto urls = EnumerateCookieUrls();
            if (urls.empty()) {
                printf("No cookie entries found for current user.\n");
                return cookies;
            }

            for (auto& u : urls) {
                auto hdr = GetCookiesForUrl(u);
                if (hdr.empty()) continue;

                // 拆分并提取 name=value
                std::wstringstream ss(hdr);
                std::wstring token;
                while (std::getline(ss, token, L';')) {
                    auto l = token.find_first_not_of(L" \t");
                    auto r = token.find_last_not_of(L" \t");
                    if (l == std::wstring::npos) continue;
                    std::wstring pair = token.substr(l, r - l + 1);
                    auto pos = pair.find(L'=');
                    if (pos != std::wstring::npos) {
                        CookieData data;
                        data.path = u;
                        data.Host = pair.substr(0, pos);
                        data.Cookie = pair.substr(pos + 1);
                        data.user_name = currentUser;
                        data.browser_type = L"Internet Explorer";
                        cookies.push_back(data);
                    }
                }
            }
        } else {
            // 扫描所有用户的IE Cookie
            for (const auto& userPath : userProfiles) {
                std::wstring userName = fs::path(userPath).filename().wstring();
                printf("Scanning IE cookies for user: %ls\n", userName.c_str());
                GetUserCookies(userPath, userName, cookies);
            }
        }

        printf("Successfully retrieved %zu IE cookie entries from all users\n", cookies.size());
    }
    catch (const std::exception& e) {
        printf("Error getting IE cookies: %s\n", e.what());
    }
    catch (...) {
        printf("Unknown error occurred while getting IE cookies\n");
    }

    return cookies;
}

std::wstring IEBrowser::GetIEDownloadFolder() {
    // 1. ���ȳ��Դ�ע�����ȡ����Ŀ¼
    HKEY hKey;
    std::wstring downloadDir;

    const wchar_t* registryPaths[] = {
        L"Software\\Microsoft\\Internet Explorer\\Main",
        L"Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\User Shell Folders",
        L"Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Shell Folders"
    };

    for (const wchar_t* regPath : registryPaths) {
        if (RegOpenKeyExW(HKEY_CURRENT_USER, regPath, 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
            WCHAR buffer[MAX_PATH];
            DWORD bufferSize = sizeof(buffer);

            // ���Բ�ͬ��ֵ����
            const wchar_t* valueNames[] = {
                L"Default Download Directory",
                L"{374DE290-123F-4565-9164-39C4925E467B}",
                L"Downloads",
                L"Personal"
            };

            for (const wchar_t* valueName : valueNames) {
                if (RegQueryValueExW(hKey, valueName, NULL, NULL, (LPBYTE)buffer, &bufferSize) == ERROR_SUCCESS) {
                    downloadDir = buffer;

                    // ����ǻ�������·��,չ����
                    if (downloadDir.find(L"%") != std::wstring::npos) {
                        WCHAR expanded[MAX_PATH];
                        if (ExpandEnvironmentStringsW(downloadDir.c_str(), expanded, MAX_PATH)) {
                            downloadDir = expanded;
                        }
                    }

                    RegCloseKey(hKey);
                    return downloadDir;
                }
            }
            RegCloseKey(hKey);
        }
    }

    // 2. ���ע�����û���ҵ�,����Ĭ��λ��
    WCHAR defaultPaths[3][MAX_PATH];
    if (SUCCEEDED(SHGetFolderPathW(NULL, CSIDL_PERSONAL, NULL, 0, defaultPaths[0]))) {
        std::wstring downloads = std::wstring(defaultPaths[0]) + L"\\Downloads";
        if (fs::exists(downloads)) {
            return downloads;
        }
    }

    if (SUCCEEDED(SHGetFolderPathW(NULL, CSIDL_PROFILE, NULL, 0, defaultPaths[1]))) {
        std::wstring downloads = std::wstring(defaultPaths[1]) + L"\\Downloads";
        if (fs::exists(downloads)) {
            return downloads;
        }
    }

    if (SUCCEEDED(SHGetFolderPathW(NULL, CSIDL_DESKTOP, NULL, 0, defaultPaths[2]))) {
        return defaultPaths[2];
    }

    return L"";
}

std::vector<DownloadData> IEBrowser::GetRegistryDownloads() {
    std::vector<DownloadData> downloads;

    // 1. ��ȡIE����Ŀ¼
    WCHAR cachePath[MAX_PATH];
    if (SUCCEEDED(SHGetFolderPathW(NULL, CSIDL_INTERNET_CACHE, NULL, 0, cachePath))) {
        printf("IE cache directory: %ls\n", cachePath);

        // ����Content.IE5Ŀ¼
        fs::path contentPath = fs::path(cachePath) / L"Content.IE5";
        if (fs::exists(contentPath)) {
            try {
                // ����Content.IE5Ŀ¼�µ�������Ŀ¼
                for (const auto& entry : fs::recursive_directory_iterator(contentPath)) {
                    if (entry.is_regular_file()) {
                        std::wstring filename = entry.path().filename().wstring();
                        std::wstring extension = entry.path().extension().wstring();

                        // �ų�index.dat����ʱ�ļ�
                        if (_wcsicmp(filename.c_str(), L"index.dat") != 0 &&
                            extension != L".tmp" && extension != L".dat") {

                            DownloadData data;
                            data.file_path = entry.path().wstring();

                            // ��ȡ�ļ���Ϣ
                            WIN32_FILE_ATTRIBUTE_DATA fileAttr;
                            if (GetFileAttributesExW(data.file_path.c_str(), GetFileExInfoStandard, &fileAttr)) {
                                data.file_size = ((ULONGLONG)fileAttr.nFileSizeHigh << 32) | fileAttr.nFileSizeLow;

                                SYSTEMTIME st;
                                FileTimeToSystemTime(&fileAttr.ftCreationTime, &st);
                                wchar_t timeStr[32];
                                swprintf_s(timeStr, L"%04d-%02d-%02d %02d:%02d:%02d",
                                    st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
                                data.start_time = timeStr;

                                FileTimeToSystemTime(&fileAttr.ftLastWriteTime, &st);
                                swprintf_s(timeStr, L"%04d-%02d-%02d %02d:%02d:%02d",
                                    st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
                                data.end_time = timeStr;

                                // ���Դ�ע�����ȡURL
                                data.url = GetUrlFromCache(data.file_path);

                                downloads.push_back(data);
                            }
                        }
                    }
                }
            }
            catch (const std::exception& e) {
                printf("Error scanning Content.IE5 directory: %s\n", e.what());
            }
        }
    }

    // 2. ������Ŀ¼��ȡ��¼
    std::wstring downloadDir = GetIEDownloadFolder();
    if (!downloadDir.empty() && fs::exists(downloadDir)) {
        printf("Scanning download directory: %ls\n", downloadDir.c_str());
        try {
            for (const auto& entry : fs::directory_iterator(downloadDir)) {
                if (entry.is_regular_file()) {
                    DownloadData data;
                    data.file_path = entry.path().wstring();

                    // ��ȡ�ļ���Ϣ
                    WIN32_FILE_ATTRIBUTE_DATA fileAttr;
                    if (GetFileAttributesExW(data.file_path.c_str(), GetFileExInfoStandard, &fileAttr)) {
                        data.file_size = ((ULONGLONG)fileAttr.nFileSizeHigh << 32) | fileAttr.nFileSizeLow;

                        SYSTEMTIME st;
                        FileTimeToSystemTime(&fileAttr.ftCreationTime, &st);
                        wchar_t timeStr[32];
                        swprintf_s(timeStr, L"%04d-%02d-%02d %02d:%02d:%02d",
                            st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
                        data.start_time = timeStr;

                        FileTimeToSystemTime(&fileAttr.ftLastWriteTime, &st);
                        swprintf_s(timeStr, L"%04d-%02d-%02d %02d:%02d:%02d",
                            st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
                        data.end_time = timeStr;

                        // ���Դ�ע�����ȡURL
                        data.url = GetUrlFromCache(data.file_path);

                        downloads.push_back(data);
                    }
                }
            }
        }
        catch (const std::exception& e) {
            printf("Error scanning download directory: %s\n", e.what());
        }
    }

    printf("Found %zu IE downloads\n", downloads.size());
    return downloads;
}

std::vector<std::wstring> IEBrowser::EnumerateCookieUrls()
{
    std::vector<std::wstring> urls;
    DWORD bufSize = 0;

    if (!FindFirstUrlCacheEntryExW(
        nullptr,
        0,
        CACHE_ENTRY_TYPE_COOKIE,
        0,
        nullptr, &bufSize,
        nullptr, nullptr, nullptr
    ) && GetLastError() != ERROR_INSUFFICIENT_BUFFER)
    {
        std::wcerr << L"No cookie entries or unexpected error: " << GetLastError() << std::endl;
        return urls;
    }

    auto entry = (INTERNET_CACHE_ENTRY_INFOW*)std::malloc(bufSize);
    HANDLE hEnum = FindFirstUrlCacheEntryExW(
        nullptr, 0, CACHE_ENTRY_TYPE_COOKIE, 0,
        entry, &bufSize,
        nullptr, nullptr, nullptr
    );
    if (!hEnum) {
        std::wcerr << L"FindFirstUrlCacheEntryExW failed: " << GetLastError() << std::endl;
        std::free(entry);
        return urls;
    }

    while (true) {
        if (entry->lpszSourceUrlName)
            urls.push_back(entry->lpszSourceUrlName);

        bufSize = entry->dwStructSize;
        if (!FindNextUrlCacheEntryExW(
            hEnum,
            entry, &bufSize,
            nullptr, nullptr, nullptr
        ))
        {
            DWORD err = GetLastError();
            if (err == ERROR_NO_MORE_ITEMS) {
                break;
            }
            if (err == ERROR_INSUFFICIENT_BUFFER) {
                auto newEntry = (INTERNET_CACHE_ENTRY_INFOW*)std::malloc(bufSize);
                if (!newEntry) {
                    break;
                }
                std::free(entry);
                entry = newEntry;
                if (!FindNextUrlCacheEntryExW(
                    hEnum,
                    entry, &bufSize,
                    nullptr, nullptr, nullptr
                ))
                {
                    break;
                }
            }
            else {
                break;
            }
        }
    }

    FindCloseUrlCache(hEnum);
    std::free(entry);
    return urls;
}

std::wstring IEBrowser::GetCookiesForUrl(const std::wstring& url)
{
    DWORD len = 0;
    InternetGetCookieExW(
        url.c_str(), nullptr, nullptr, &len,
        INTERNET_COOKIE_HTTPONLY, nullptr
    );
    if (len == 0) return {};

    std::vector<wchar_t> buf(len);
    if (!InternetGetCookieExW(
        url.c_str(), nullptr,
        buf.data(), &len,
        INTERNET_COOKIE_HTTPONLY, nullptr
    ))
    {
        return {};
    }
    return std::wstring(buf.data());
}

std::wstring IEBrowser::GetUrlFromCache(const std::wstring& filePath) {
    // 1. ��Internet Settings\Cache\Contentע������ȡURL
    HKEY hKey;
    std::wstring url;

    const wchar_t* registryPaths[] = {
        L"Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings\\5.0\\Cache\\Content",
        L"Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings\\Cache\\Paths"
    };

    std::wstring filename = fs::path(filePath).filename().wstring();

    for (const wchar_t* regPath : registryPaths) {
        if (RegOpenKeyExW(HKEY_CURRENT_USER, regPath, 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
            DWORD index = 0;
            while (true) {
                WCHAR valueName[MAX_PATH];
                DWORD valueNameSize = MAX_PATH;
                WCHAR valueData[MAX_PATH];
                DWORD valueDataSize = sizeof(valueData);
                DWORD type;

                LONG result = RegEnumValueW(hKey, index++, valueName, &valueNameSize,
                    NULL, &type, (LPBYTE)valueData, &valueDataSize);

                if (result == ERROR_NO_MORE_ITEMS) break;
                if (result != ERROR_SUCCESS) continue;

                if (type == REG_BINARY || type == REG_SZ) {
                    std::wstring value = std::wstring(valueData, valueDataSize / sizeof(WCHAR));

                    // ���ֵ�Ƿ�����ļ���
                    if (value.find(filename) != std::wstring::npos) {
                        // ������ȡURL
                        size_t httpPos = value.find(L"http://");
                        if (httpPos == std::wstring::npos) {
                            httpPos = value.find(L"https://");
                        }

                        if (httpPos != std::wstring::npos) {
                            size_t endPos = value.find(L'\0', httpPos);
                            if (endPos != std::wstring::npos) {
                                url = value.substr(httpPos, endPos - httpPos);
                                RegCloseKey(hKey);
                                return url;
                            }
                        }
                    }
                }
            }
            RegCloseKey(hKey);
        }
    }

    // 2. ���û���ҵ�URL,���ؿ��ַ���
    return L"";
}



std::vector<BookmarkData> IEBrowser::GetBookmarks()
{
    std::vector<BookmarkData> bookmarks;

    printf("Starting IE bookmarks extraction for all users...\n");

    try {
        // 获取所有用户配置文件路径
        std::vector<std::wstring> userProfiles = GetAllIEUserProfiles();

        if (userProfiles.empty()) {
            printf("No user profiles found, falling back to current user\n");
            // 回退到当前用户
            std::wstring currentUser = GetCurrentUserName();

            // 获取收藏夹路径
            WCHAR favoritesPath[MAX_PATH];
            if (FAILED(SHGetFolderPathW(NULL, CSIDL_FAVORITES, NULL, 0, favoritesPath))) {
                printf("获取IE收藏夹文件夹失败\n");
                return bookmarks;
            }

            printf("IE收藏夹文件夹: %ls\n", favoritesPath);

            try {
                // 递归函数处理收藏夹文件夹
                std::function<void(const fs::path&, const std::wstring&)> processFolder =
                    [&bookmarks, &currentUser, &processFolder](const fs::path& folderPath, const std::wstring& folderName) {
                    try {
                        for (const auto& entry : fs::directory_iterator(folderPath)) {
                            if (entry.is_directory()) {
                                // 处理子文件夹
                                std::wstring subFolderName = folderName;
                                if (!subFolderName.empty()) subFolderName += L"/";
                                subFolderName += entry.path().filename().wstring();

                                processFolder(entry.path(), subFolderName);
                            }
                            else if (entry.is_regular_file() && entry.path().extension() == L".url") {
                                // 处理URL文件
                                BookmarkData bookmark;
                                bookmark.title = entry.path().stem().wstring();
                                bookmark.folder_path = folderName;

                                // 获取文件创建时间
                                WIN32_FILE_ATTRIBUTE_DATA fileAttr;
                                if (GetFileAttributesExW(entry.path().c_str(), GetFileExInfoStandard, &fileAttr)) {
                                    SYSTEMTIME st;
                                    FileTimeToSystemTime(&fileAttr.ftCreationTime, &st);
                                    wchar_t timeStr[32];
                                    swprintf_s(timeStr, L"%04d-%02d-%02d %02d:%02d:%02d",
                                        st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
                                    bookmark.date_added = timeStr;
                                }

                                // 从.url文件中读取URL
                                std::wifstream file(entry.path());
                                if (file.is_open()) {
                                    std::wstring line;
                                    while (std::getline(file, line)) {
                                        if (line.find(L"URL=") == 0) {
                                            bookmark.url = line.substr(4);
                                            break;
                                        }
                                    }
                                    file.close();
                                }

                                if (!bookmark.url.empty()) {
                                    bookmark.user_name = currentUser;
                                    bookmark.browser_type = L"Internet Explorer";
                                    bookmarks.push_back(bookmark);
                                    printf("找到IE书签: %ls\n", bookmark.title.c_str());
                                }
                            }
                        }
                    }
                    catch (const std::exception& e) {
                        printf("处理IE书签文件夹时出错: %s\n", e.what());
                    }
                    };

                // 从收藏夹根目录开始处理
                processFolder(favoritesPath, L"");

                printf("共找到 %zu 个IE书签\n", bookmarks.size());
            }
            catch (const std::exception& e) {
                printf("获取IE书签时出错: %s\n", e.what());
            }
        } else {
            // 扫描所有用户的IE书签
            for (const auto& userPath : userProfiles) {
                std::wstring userName = fs::path(userPath).filename().wstring();
                printf("Scanning IE bookmarks for user: %ls\n", userName.c_str());
                GetUserBookmarks(userPath, userName, bookmarks);
            }
        }

        printf("Successfully retrieved %zu IE bookmark entries from all users\n", bookmarks.size());
    }
    catch (const std::exception& e) {
        printf("Error getting IE bookmarks: %s\n", e.what());
    }
    catch (...) {
        printf("Unknown error occurred while getting IE bookmarks\n");
    }

    return bookmarks;
}

std::vector<CacheFileData> IEBrowser::GetBroswerCache()
{
    std::vector<CacheFileData> caches;

    printf("Starting IE cache file extraction for all users...\n");

    try {
        // 获取所有用户的IE配置文件路径
        std::vector<std::wstring> userProfiles = GetAllIEUserProfiles();

        if (userProfiles.empty()) {
            printf("No IE user profiles found, falling back to current user\n");
            // 回退到当前用户
            WCHAR cachePath[MAX_PATH];
            if (SUCCEEDED(SHGetFolderPathW(NULL, CSIDL_INTERNET_CACHE, NULL, 0, cachePath))) {
                ScanIECacheDirectory(cachePath, caches);
            }
            GetCacheInfoFromWinInet(caches);
        } else {
            // 扫描所有用户的IE缓存
            for (const auto& userPath : userProfiles) {
                std::wstring userName = fs::path(userPath).filename().wstring();
                printf("Scanning IE cache for user: %ls\n", userName.c_str());
                ScanUserIECacheDirectory(userPath, userName, caches);
            }
        }

        printf("Successfully retrieved %zu IE cache entries from all users\n", caches.size());
    }
    catch (const std::exception& e) {
        printf("Error getting IE cache: %s\n", e.what());
    }
    catch (...) {
        printf("Unknown error occurred while getting IE cache\n");
    }

    return caches;
}

// 扫描IE缓存目录获取缓存文件信息
void IEBrowser::ScanIECacheDirectory(const std::wstring& cachePath, std::vector<CacheFileData>& caches) {
    printf("Starting IE cache directory scan: %ls\n", cachePath.c_str());

    try {
        // 获取当前用户名
        wchar_t username[256];
        DWORD usernameLen = sizeof(username) / sizeof(username[0]);
        GetUserNameW(username, &usernameLen);

        int fileCount = 0;
        const int maxFiles = 600; // IE缓存文件数量限制

        // 扫描Content.IE5目录
        fs::path contentPath = fs::path(cachePath) / L"Content.IE5";
        if (fs::exists(contentPath)) {
            printf("Scanning Content.IE5 directory...\n");

            // 递归遍历Content.IE5目录
            for (const auto& entry : fs::recursive_directory_iterator(contentPath)) {
                if (fileCount >= maxFiles) {
                    printf("Reached maximum file scan limit (%d), stopping scan\n", maxFiles);
                    break;
                }

                if (entry.is_regular_file()) {
                    try {
                        std::wstring filePath = entry.path().wstring();
                        std::wstring fileName = entry.path().filename().wstring();

                        // 跳过index.dat和临时文件
                        if (_wcsicmp(fileName.c_str(), L"index.dat") == 0 ||
                            entry.path().extension() == L".tmp" ||
                            entry.path().extension() == L".dat") {
                            continue;
                        }

                        CacheFileData cacheData;
                        cacheData.browser_type = L"Internet Explorer";
                        cacheData.local_file_path = filePath;
                        cacheData.user_name = username;

                        // 获取文件时间信息
                        WIN32_FILE_ATTRIBUTE_DATA fileAttr;
                        if (GetFileAttributesExW(filePath.c_str(), GetFileExInfoStandard, &fileAttr)) {
                            SYSTEMTIME st;
                            wchar_t timeBuffer[100];

                            // 创建时间
                            FileTimeToSystemTime(&fileAttr.ftCreationTime, &st);
                            swprintf_s(timeBuffer, L"%04d-%02d-%02d %02d:%02d:%02d",
                                st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
                            cacheData.create_time = timeBuffer;

                            // 修改时间
                            FileTimeToSystemTime(&fileAttr.ftLastWriteTime, &st);
                            swprintf_s(timeBuffer, L"%04d-%02d-%02d %02d:%02d:%02d",
                                st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
                            cacheData.last_modified_time = timeBuffer;

                            // 访问时间
                            FileTimeToSystemTime(&fileAttr.ftLastAccessTime, &st);
                            swprintf_s(timeBuffer, L"%04d-%02d-%02d %02d:%02d:%02d",
                                st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
                            cacheData.last_access_time = timeBuffer;
                        }

                        // 尝试从缓存中获取URL
                        cacheData.url = GetUrlFromCache(filePath);
                        if (cacheData.url.empty()) {
                            cacheData.url = L"IE cache: " + fileName;
                        }

                        // 获取内容类型
                        cacheData.content_type = GetIEContentTypeFromExtension(filePath);

                        // 分析风险级别
                        cacheData.risk_level = AnalyzeIERiskLevel(cacheData.url, filePath);

                        // 检查敏感关键字
                        cacheData.matched_keywords = CheckIESensitiveKeywords(cacheData.url, filePath);
                        cacheData.is_suspicious = !cacheData.matched_keywords.empty();

                        // 设置检查结论
                        if (cacheData.is_suspicious) {
                            cacheData.check_result = L"Sensitive content found";
                        } else {
                            cacheData.check_result = L"Normal cache file";
                        }

                        caches.push_back(cacheData);
                        fileCount++;

                        if (fileCount % 50 == 0) {
                            printf("Scanned %d IE cache files...\n", fileCount);
                        }
                    }
                    catch (const std::exception& e) {
                        printf("Error processing IE cache file: %s\n", e.what());
                        continue;
                    }
                }
            }
        }

        printf("IE cache directory scan completed, processed %d files\n", fileCount);
    }
    catch (const std::exception& e) {
        printf("Error scanning IE cache directory: %s\n", e.what());
    }
}

// 通过WinInet API获取缓存信息
void IEBrowser::GetCacheInfoFromWinInet(std::vector<CacheFileData>& caches) {
    printf("Trying to get cache info from WinInet API...\n");

    try {
        DWORD bufSize = 0;

        // 首次调用获取所需缓冲区大小
        if (!FindFirstUrlCacheEntryExW(
            nullptr, 0, 0, 0,
            nullptr, &bufSize,
            nullptr, nullptr, nullptr
        ) && GetLastError() != ERROR_INSUFFICIENT_BUFFER) {
            printf("No cache entries found or unexpected error: %lu\n", GetLastError());
            return;
        }

        auto entry = (INTERNET_CACHE_ENTRY_INFOW*)std::malloc(bufSize);
        if (!entry) {
            printf("Failed to allocate memory for cache entry\n");
            return;
        }

        HANDLE hEnum = FindFirstUrlCacheEntryExW(
            nullptr, 0, 0, 0,
            entry, &bufSize,
            nullptr, nullptr, nullptr
        );

        if (!hEnum) {
            printf("FindFirstUrlCacheEntryExW failed: %lu\n", GetLastError());
            std::free(entry);
            return;
        }

        // 获取当前用户名
        wchar_t username[256];
        DWORD usernameLen = sizeof(username) / sizeof(username[0]);
        GetUserNameW(username, &usernameLen);

        int entryCount = 0;
        const int maxEntries = 200; // 限制WinInet API查询的条目数量

        do {
            if (entryCount >= maxEntries) {
                printf("Reached maximum WinInet entries limit (%d), stopping enumeration\n", maxEntries);
                break;
            }

            try {
                // 跳过Cookie条目，只处理普通缓存
                if (!(entry->CacheEntryType & CACHE_ENTRY_TYPE_COOKIE) && entry->lpszSourceUrlName) {
                    CacheFileData cacheData;
                    cacheData.browser_type = L"Internet Explorer";
                    cacheData.url = entry->lpszSourceUrlName;
                    cacheData.user_name = username;

                    // 获取本地文件路径
                    if (entry->lpszLocalFileName) {
                        cacheData.local_file_path = entry->lpszLocalFileName;
                    }

                    // 获取时间信息
                    SYSTEMTIME st;
                    wchar_t timeBuffer[100];

                    // 最后访问时间
                    FileTimeToSystemTime(&entry->LastAccessTime, &st);
                    swprintf_s(timeBuffer, L"%04d-%02d-%02d %02d:%02d:%02d",
                        st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
                    cacheData.last_access_time = timeBuffer;

                    // 最后修改时间
                    FileTimeToSystemTime(&entry->LastModifiedTime, &st);
                    swprintf_s(timeBuffer, L"%04d-%02d-%02d %02d:%02d:%02d",
                        st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
                    cacheData.last_modified_time = timeBuffer;
                    cacheData.create_time = cacheData.last_modified_time; // IE缓存没有单独的创建时间

                    // 访问次数
                    cacheData.hit_count = entry->dwHitRate;

                    // 获取内容类型
                    cacheData.content_type = GetIEContentTypeFromExtension(cacheData.local_file_path);

                    // 分析风险级别
                    cacheData.risk_level = AnalyzeIERiskLevel(cacheData.url, cacheData.local_file_path);

                    // 检查敏感关键字
                    cacheData.matched_keywords = CheckIESensitiveKeywords(cacheData.url, cacheData.local_file_path);
                    cacheData.is_suspicious = !cacheData.matched_keywords.empty();

                    // 设置检查结论
                    if (cacheData.is_suspicious) {
                        cacheData.check_result = L"URL contains sensitive content";
                    } else {
                        cacheData.check_result = L"Normal cache entry";
                    }

                    caches.push_back(cacheData);
                    entryCount++;
                }
            }
            catch (const std::exception& e) {
                printf("Error processing WinInet cache entry: %s\n", e.what());
            }

            // 获取下一个条目
            bufSize = entry->dwStructSize;
            if (!FindNextUrlCacheEntryExW(hEnum, entry, &bufSize, nullptr, nullptr, nullptr)) {
                DWORD err = GetLastError();
                if (err == ERROR_NO_MORE_ITEMS) {
                    break;
                }
                if (err == ERROR_INSUFFICIENT_BUFFER) {
                    auto newEntry = (INTERNET_CACHE_ENTRY_INFOW*)std::malloc(bufSize);
                    if (!newEntry) {
                        break;
                    }
                    std::free(entry);
                    entry = newEntry;
                    if (!FindNextUrlCacheEntryExW(hEnum, entry, &bufSize, nullptr, nullptr, nullptr)) {
                        break;
                    }
                } else {
                    break;
                }
            }
        } while (true);

        FindCloseUrlCache(hEnum);
        std::free(entry);

        printf("Retrieved %d cache entries from WinInet API\n", entryCount);
    }
    catch (const std::exception& e) {
        printf("Error getting cache info from WinInet API: %s\n", e.what());
    }
}

// 根据文件扩展名获取IE内容类型
std::wstring IEBrowser::GetIEContentTypeFromExtension(const std::wstring& filePath) {
    std::wstring extension = fs::path(filePath).extension().wstring();
    std::transform(extension.begin(), extension.end(), extension.begin(), ::towlower);

    if (extension == L".html" || extension == L".htm") {
        return L"text/html";
    } else if (extension == L".css") {
        return L"text/css";
    } else if (extension == L".js") {
        return L"application/javascript";
    } else if (extension == L".json") {
        return L"application/json";
    } else if (extension == L".xml") {
        return L"application/xml";
    } else if (extension == L".jpg" || extension == L".jpeg") {
        return L"image/jpeg";
    } else if (extension == L".png") {
        return L"image/png";
    } else if (extension == L".gif") {
        return L"image/gif";
    } else if (extension == L".bmp") {
        return L"image/bmp";
    } else if (extension == L".ico") {
        return L"image/x-icon";
    } else if (extension == L".svg") {
        return L"image/svg+xml";
    } else if (extension == L".pdf") {
        return L"application/pdf";
    } else if (extension == L".doc" || extension == L".docx") {
        return L"application/msword";
    } else if (extension == L".xls" || extension == L".xlsx") {
        return L"application/vnd.ms-excel";
    } else if (extension == L".ppt" || extension == L".pptx") {
        return L"application/vnd.ms-powerpoint";
    } else if (extension == L".zip") {
        return L"application/zip";
    } else if (extension == L".rar") {
        return L"application/x-rar-compressed";
    } else if (extension == L".exe") {
        return L"application/x-msdownload";
    } else if (extension == L".msi") {
        return L"application/x-msi";
    } else {
        return L"application/octet-stream";
    }
}

// 分析IE缓存的风险级别
std::wstring IEBrowser::AnalyzeIERiskLevel(const std::wstring& url, const std::wstring& filePath) {
    std::wstring urlLower = url;
    std::wstring pathLower = filePath;
    std::transform(urlLower.begin(), urlLower.end(), urlLower.begin(), ::towlower);
    std::transform(pathLower.begin(), pathLower.end(), pathLower.begin(), ::towlower);

    // 高风险关键字
    std::vector<std::wstring> highRiskKeywords = {
        L"password", L"login", L"signin", L"admin", L"administrator",
        L"bank", L"banking", L"credit", L"card", L"payment", L"paypal",
        L"alipay", L"wechat", L"qq", L"weibo", L"download", L"install",
        L".exe", L".bat", L".cmd", L".scr", L".vbs", L".msi", L".com"
    };

    // 中风险关键字
    std::vector<std::wstring> mediumRiskKeywords = {
        L"user", L"account", L"profile", L"personal", L"private",
        L"secure", L"auth", L"token", L"session", L"cookie", L"api",
        L"config", L"setting", L"preference"
    };

    // 检查高风险
    for (const auto& keyword : highRiskKeywords) {
        if (urlLower.find(keyword) != std::wstring::npos ||
            pathLower.find(keyword) != std::wstring::npos) {
            return L"High";
        }
    }

    // 检查中风险
    for (const auto& keyword : mediumRiskKeywords) {
        if (urlLower.find(keyword) != std::wstring::npos ||
            pathLower.find(keyword) != std::wstring::npos) {
            return L"Medium";
        }
    }

    return L"Low";
}

// 检查IE缓存的敏感关键字
std::vector<std::wstring> IEBrowser::CheckIESensitiveKeywords(const std::wstring& url, const std::wstring& filePath) {
    std::vector<std::wstring> matchedKeywords;
    std::wstring urlLower = url;
    std::wstring pathLower = filePath;
    std::transform(urlLower.begin(), urlLower.end(), urlLower.begin(), ::towlower);
    std::transform(pathLower.begin(), pathLower.end(), pathLower.begin(), ::towlower);

    // 敏感关键字列表
    std::vector<std::wstring> sensitiveKeywords = {
        L"password", L"login", L"signin", L"admin", L"administrator",
        L"bank", L"banking", L"credit", L"card", L"payment", L"paypal",
        L"alipay", L"wechat", L"qq", L"weibo", L"personal", L"private",
        L"secure", L"confidential", L"secret", L"download", L"install",
        L".exe", L".bat", L".cmd", L".scr", L".msi", L"api", L"token",
        L"config", L"setting", L"preference", L"registry"
    };

    for (const auto& keyword : sensitiveKeywords) {
        if (urlLower.find(keyword) != std::wstring::npos ||
            pathLower.find(keyword) != std::wstring::npos) {
            matchedKeywords.push_back(keyword);
        }
    }

    return matchedKeywords;
}

// 获取所有用户的IE配置文件路径
std::vector<std::wstring> IEBrowser::GetAllIEUserProfiles() {
    std::vector<std::wstring> userProfiles;

    try {
        // 获取系统盘路径
        WCHAR systemDrive[4];
        if (GetEnvironmentVariableW(L"SystemDrive", systemDrive, 4) == 0) {
            wcscpy_s(systemDrive, L"C:");
        }

        std::wstring usersPath = std::wstring(systemDrive) + L"\\Users";
        printf("Scanning users directory for IE profiles: %ls\n", usersPath.c_str());

        if (!fs::exists(usersPath)) {
            printf("Users directory not found: %ls\n", usersPath.c_str());
            return userProfiles;
        }

        // 遍历Users目录下的所有用户文件夹
        for (const auto& entry : fs::directory_iterator(usersPath)) {
            if (entry.is_directory()) {
                std::wstring userName = entry.path().filename().wstring();

                // 跳过系统用户和特殊文件夹
                if (userName == L"Public" || userName == L"Default" ||
                    userName == L"All Users" || userName == L"Default User") {
                    continue;
                }

                std::wstring userPath = entry.path().wstring();
                printf("Found user for IE scan: %ls\n", userName.c_str());
                userProfiles.push_back(userPath);
            }
        }

        printf("Found %zu user profiles for IE\n", userProfiles.size());
    }
    catch (const std::exception& e) {
        printf("Error scanning user profiles for IE: %s\n", e.what());
    }

    return userProfiles;
}

// 扫描指定用户的IE缓存目录
void IEBrowser::ScanUserIECacheDirectory(const std::wstring& userPath, const std::wstring& userName, std::vector<CacheFileData>& caches) {
    try {
        // IE缓存路径通常在用户的Local Settings下
        std::vector<std::wstring> ieCachePaths = {
            userPath + L"\\AppData\\Local\\Microsoft\\Windows\\INetCache",
            userPath + L"\\AppData\\Local\\Microsoft\\Windows\\Temporary Internet Files",
            userPath + L"\\Local Settings\\Temporary Internet Files"
        };

        for (const auto& cachePath : ieCachePaths) {
            if (fs::exists(cachePath)) {
                printf("Found IE cache directory for user %ls: %ls\n", userName.c_str(), cachePath.c_str());

                // 临时修改缓存数据的用户名
                size_t beforeCount = caches.size();
                ScanIECacheDirectory(cachePath, caches);

                // 更新新添加的缓存条目的用户名
                for (size_t i = beforeCount; i < caches.size(); ++i) {
                    caches[i].user_name = userName;
                    caches[i].browser_type = L"Internet Explorer";
                }

                // 对于IE，我们还需要尝试使用WinInet API获取该用户的缓存信息
                // 注意：WinInet API通常只能获取当前用户的缓存信息
                if (userName == GetCurrentUserName()) {
                    printf("Getting WinInet cache info for current user: %ls\n", userName.c_str());
                    size_t beforeWinInetCount = caches.size();
                    GetCacheInfoFromWinInet(caches);

                    // 更新WinInet获取的缓存条目的用户名
                    for (size_t i = beforeWinInetCount; i < caches.size(); ++i) {
                        caches[i].user_name = userName;
                    }
                }
            }
        }
    }
    catch (const std::exception& e) {
        printf("Error scanning IE cache for user %ls: %s\n", userName.c_str(), e.what());
    }
}

// 获取当前用户名的辅助函数
std::wstring IEBrowser::GetCurrentUserName() {
    wchar_t username[256];
    DWORD usernameLen = sizeof(username) / sizeof(username[0]);
    if (GetUserNameW(username, &usernameLen)) {
        return std::wstring(username);
    }
    return L"";
}

// 获取指定用户的IE密码
void IEBrowser::GetUserPasswords(const std::wstring& userPath, const std::wstring& userName, std::vector<PasswordData>& passwords) {
    try {
        printf("Getting IE passwords for user: %ls\n", userName.c_str());

        // IE密码通常存储在Windows凭据管理器中，这是系统级的
        // 对于多用户支持，我们需要尝试访问该用户的凭据存储
        // 注意：这通常需要管理员权限或者在该用户的上下文中运行

        // 由于IE密码存储在系统凭据管理器中，我们只能获取当前用户的密码
        if (userName == GetCurrentUserName()) {
            printf("Getting credentials for current user: %ls\n", userName.c_str());
            std::vector<PasswordData> userPasswords = GetCredentialPasswords();

            // 设置用户名和浏览器类型
            for (auto& pwd : userPasswords) {
                pwd.user_name = userName;
                pwd.browser_type = L"Internet Explorer";
                passwords.push_back(pwd);
            }
        } else {
            printf("Cannot access credentials for user %ls (not current user)\n", userName.c_str());
        }
    }
    catch (const std::exception& e) {
        printf("Error getting IE passwords for user %ls: %s\n", userName.c_str(), e.what());
    }
}

// 获取指定用户的IE历史记录
void IEBrowser::GetUserHistory(const std::wstring& userPath, const std::wstring& userName, std::vector<HistoryData>& history) {
    try {
        printf("Getting IE history for user: %ls\n", userName.c_str());

        // IE历史记录存储在注册表中，通常在HKEY_CURRENT_USER下
        // 对于其他用户，我们需要尝试访问他们的注册表配置单元

        // 由于IE历史记录存储在当前用户的注册表中，我们只能获取当前用户的历史记录
        if (userName == GetCurrentUserName()) {
            printf("Getting registry history for current user: %ls\n", userName.c_str());
            std::vector<HistoryData> userHistory = GetRegistryHistory();

            // 设置用户名和浏览器类型
            for (auto& hist : userHistory) {
                hist.user_name = userName;
                hist.browser_type = L"Internet Explorer";
                history.push_back(hist);
            }
        } else {
            printf("Cannot access registry history for user %ls (not current user)\n", userName.c_str());
        }
    }
    catch (const std::exception& e) {
        printf("Error getting IE history for user %ls: %s\n", userName.c_str(), e.what());
    }
}

// 获取指定用户的IE下载记录
void IEBrowser::GetUserDownloads(const std::wstring& userPath, const std::wstring& userName, std::vector<DownloadData>& downloads) {
    try {
        printf("Getting IE downloads for user: %ls\n", userName.c_str());

        // IE下载记录主要来自缓存目录和下载文件夹
        // 我们可以扫描用户特定的目录

        // 1. 扫描用户的IE缓存目录
        std::vector<std::wstring> ieCachePaths = {
            userPath + L"\\AppData\\Local\\Microsoft\\Windows\\INetCache",
            userPath + L"\\AppData\\Local\\Microsoft\\Windows\\Temporary Internet Files",
            userPath + L"\\Local Settings\\Temporary Internet Files"
        };

        for (const auto& cachePath : ieCachePaths) {
            if (fs::exists(cachePath)) {
                printf("Scanning IE cache for downloads for user %ls: %ls\n", userName.c_str(), cachePath.c_str());

                // 扫描Content.IE5目录
                fs::path contentPath = fs::path(cachePath) / L"Content.IE5";
                if (fs::exists(contentPath)) {
                    try {
                        for (const auto& entry : fs::recursive_directory_iterator(contentPath)) {
                            if (entry.is_regular_file()) {
                                std::wstring filename = entry.path().filename().wstring();
                                std::wstring extension = entry.path().extension().wstring();

                                // 排除index.dat和临时文件
                                if (_wcsicmp(filename.c_str(), L"index.dat") != 0 &&
                                    extension != L".tmp" && extension != L".dat") {

                                    DownloadData data;
                                    data.file_path = entry.path().wstring();

                                    // 获取文件信息
                                    WIN32_FILE_ATTRIBUTE_DATA fileAttr;
                                    if (GetFileAttributesExW(data.file_path.c_str(), GetFileExInfoStandard, &fileAttr)) {
                                        data.file_size = ((ULONGLONG)fileAttr.nFileSizeHigh << 32) | fileAttr.nFileSizeLow;

                                        SYSTEMTIME st;
                                        FileTimeToSystemTime(&fileAttr.ftCreationTime, &st);
                                        wchar_t timeStr[32];
                                        swprintf_s(timeStr, L"%04d-%02d-%02d %02d:%02d:%02d",
                                            st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
                                        data.start_time = timeStr;

                                        FileTimeToSystemTime(&fileAttr.ftLastWriteTime, &st);
                                        swprintf_s(timeStr, L"%04d-%02d-%02d %02d:%02d:%02d",
                                            st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
                                        data.end_time = timeStr;

                                        // 尝试从缓存获取URL
                                        data.url = GetUrlFromCache(data.file_path);

                                        // 设置用户名和浏览器类型
                                        data.user_name = userName;
                                        data.browser_type = L"Internet Explorer";

                                        // 提取文件图标
                                        if (!data.file_path.empty()) {
                                            printf("提取IE缓存文件图标: %ls\n", data.file_path.c_str());
                                            data.file_icon = Utils::GetFileIconAsBase64(data.file_path, 32);
                                            if (data.file_icon.empty()) {
                                                printf("IE缓存文件图标提取失败: %ls\n", data.file_path.c_str());
                                            } else {
                                                printf("IE缓存文件图标提取成功，Base64长度: %zu\n", data.file_icon.length());
                                            }
                                        }

                                        downloads.push_back(data);
                                    }
                                }
                            }
                        }
                    }
                    catch (const std::exception& e) {
                        printf("Error scanning Content.IE5 directory for user %ls: %s\n", userName.c_str(), e.what());
                    }
                }
            }
        }

        // 2. 扫描用户的下载目录
        std::wstring userDownloadDir = userPath + L"\\Downloads";
        if (fs::exists(userDownloadDir)) {
            printf("Scanning download directory for user %ls: %ls\n", userName.c_str(), userDownloadDir.c_str());
            try {
                for (const auto& entry : fs::directory_iterator(userDownloadDir)) {
                    if (entry.is_regular_file()) {
                        DownloadData data;
                        data.file_path = entry.path().wstring();

                        // 获取文件信息
                        WIN32_FILE_ATTRIBUTE_DATA fileAttr;
                        if (GetFileAttributesExW(data.file_path.c_str(), GetFileExInfoStandard, &fileAttr)) {
                            data.file_size = ((ULONGLONG)fileAttr.nFileSizeHigh << 32) | fileAttr.nFileSizeLow;

                            SYSTEMTIME st;
                            FileTimeToSystemTime(&fileAttr.ftCreationTime, &st);
                            wchar_t timeStr[32];
                            swprintf_s(timeStr, L"%04d-%02d-%02d %02d:%02d:%02d",
                                st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
                            data.start_time = timeStr;

                            FileTimeToSystemTime(&fileAttr.ftLastWriteTime, &st);
                            swprintf_s(timeStr, L"%04d-%02d-%02d %02d:%02d:%02d",
                                st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
                            data.end_time = timeStr;

                            // 设置用户名和浏览器类型
                            data.user_name = userName;
                            data.browser_type = L"Internet Explorer";

                            // 提取文件图标
                            if (!data.file_path.empty()) {
                                printf("提取IE下载文件图标: %ls\n", data.file_path.c_str());
                                data.file_icon = Utils::GetFileIconAsBase64(data.file_path, 32);
                                if (data.file_icon.empty()) {
                                    printf("IE下载文件图标提取失败: %ls\n", data.file_path.c_str());
                                } else {
                                    printf("IE下载文件图标提取成功，Base64长度: %zu\n", data.file_icon.length());
                                }
                            }

                            downloads.push_back(data);
                        }
                    }
                }
            }
            catch (const std::exception& e) {
                printf("Error scanning download directory for user %ls: %s\n", userName.c_str(), e.what());
            }
        }

        printf("Found %zu IE downloads for user %ls\n", downloads.size(), userName.c_str());
    }
    catch (const std::exception& e) {
        printf("Error getting IE downloads for user %ls: %s\n", userName.c_str(), e.what());
    }
}

// 获取指定用户的IE Cookie
void IEBrowser::GetUserCookies(const std::wstring& userPath, const std::wstring& userName, std::vector<CookieData>& cookies) {
    try {
        printf("Getting IE cookies for user: %ls\n", userName.c_str());

        // IE Cookie存储在系统的Internet缓存中
        // 对于其他用户，我们只能在当前用户上下文中获取Cookie

        if (userName == GetCurrentUserName()) {
            printf("Getting cookies for current user: %ls\n", userName.c_str());

            // 获取所有 URL 列表
            auto urls = EnumerateCookieUrls();
            if (urls.empty()) {
                printf("No cookie entries found for user %ls.\n", userName.c_str());
                return;
            }

            for (auto& u : urls) {
                auto hdr = GetCookiesForUrl(u);
                if (hdr.empty()) continue;

                // 拆分并提取 name=value
                std::wstringstream ss(hdr);
                std::wstring token;
                while (std::getline(ss, token, L';')) {
                    auto l = token.find_first_not_of(L" \t");
                    auto r = token.find_last_not_of(L" \t");
                    if (l == std::wstring::npos) continue;
                    std::wstring pair = token.substr(l, r - l + 1);
                    auto pos = pair.find(L'=');
                    if (pos != std::wstring::npos) {
                        CookieData data;
                        data.path = u;
                        data.Host = pair.substr(0, pos);
                        data.Cookie = pair.substr(pos + 1);
                        data.user_name = userName;
                        data.browser_type = L"Internet Explorer";
                        cookies.push_back(data);
                    }
                }
            }
        } else {
            printf("Cannot access cookies for user %ls (not current user)\n", userName.c_str());
        }

        printf("Found %zu IE cookies for user %ls\n", cookies.size(), userName.c_str());
    }
    catch (const std::exception& e) {
        printf("Error getting IE cookies for user %ls: %s\n", userName.c_str(), e.what());
    }
}

// 获取指定用户的IE书签
void IEBrowser::GetUserBookmarks(const std::wstring& userPath, const std::wstring& userName, std::vector<BookmarkData>& bookmarks) {
    try {
        printf("Getting IE bookmarks for user: %ls\n", userName.c_str());

        // IE书签存储在用户的收藏夹文件夹中
        std::wstring favoritesPath = userPath + L"\\Favorites";

        if (!fs::exists(favoritesPath)) {
            printf("Favorites folder not found for user %ls: %ls\n", userName.c_str(), favoritesPath.c_str());
            return;
        }

        printf("IE收藏夹文件夹 for user %ls: %ls\n", userName.c_str(), favoritesPath.c_str());

        try {
            // 递归函数处理收藏夹文件夹
            std::function<void(const fs::path&, const std::wstring&)> processFolder =
                [&bookmarks, &userName, &processFolder](const fs::path& folderPath, const std::wstring& folderName) {
                try {
                    for (const auto& entry : fs::directory_iterator(folderPath)) {
                        if (entry.is_directory()) {
                            // 处理子文件夹
                            std::wstring subFolderName = folderName;
                            if (!subFolderName.empty()) subFolderName += L"/";
                            subFolderName += entry.path().filename().wstring();

                            processFolder(entry.path(), subFolderName);
                        }
                        else if (entry.is_regular_file() && entry.path().extension() == L".url") {
                            // 处理URL文件
                            BookmarkData bookmark;
                            bookmark.title = entry.path().stem().wstring();
                            bookmark.folder_path = folderName;

                            // 获取文件创建时间
                            WIN32_FILE_ATTRIBUTE_DATA fileAttr;
                            if (GetFileAttributesExW(entry.path().c_str(), GetFileExInfoStandard, &fileAttr)) {
                                SYSTEMTIME st;
                                FileTimeToSystemTime(&fileAttr.ftCreationTime, &st);
                                wchar_t timeStr[32];
                                swprintf_s(timeStr, L"%04d-%02d-%02d %02d:%02d:%02d",
                                    st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
                                bookmark.date_added = timeStr;
                            }

                            // 从.url文件中读取URL
                            std::wifstream file(entry.path());
                            if (file.is_open()) {
                                std::wstring line;
                                while (std::getline(file, line)) {
                                    if (line.find(L"URL=") == 0) {
                                        bookmark.url = line.substr(4);
                                        break;
                                    }
                                }
                                file.close();
                            }

                            if (!bookmark.url.empty()) {
                                bookmark.user_name = userName;
                                bookmark.browser_type = L"Internet Explorer";
                                bookmarks.push_back(bookmark);
                                printf("找到IE书签 for user %ls: %ls\n", userName.c_str(), bookmark.title.c_str());
                            }
                        }
                    }
                }
                catch (const std::exception& e) {
                    printf("处理IE书签文件夹时出错 for user %ls: %s\n", userName.c_str(), e.what());
                }
                };

            // 从收藏夹根目录开始处理
            processFolder(favoritesPath, L"");

            printf("共找到 %zu 个IE书签 for user %ls\n", bookmarks.size(), userName.c_str());
        }
        catch (const std::exception& e) {
            printf("获取IE书签时出错 for user %ls: %s\n", userName.c_str(), e.what());
        }
    }
    catch (const std::exception& e) {
        printf("Error getting IE bookmarks for user %ls: %s\n", userName.c_str(), e.what());
    }
}