﻿C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v150\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  ExifManager.cpp
e:\vsproject\browserdataextractor\src\exifmanager.cpp(2): warning C4603: “_CRT_SECURE_NO_WARNINGS”: 未定义宏或在预编译头使用后定义发生改变
  e:\vsproject\browserdataextractor\src\exifmanager.cpp(2): note: 将宏添加到预编译头中，而不是在此处定义
  e:\vsproject\browserdataextractor\src\exifmanager.cpp(7): note: 使用预编译头
c:\program files (x86)\microsoft sdks\windows\v7.1a\include\shlobj.h(1151): warning C4091: “typedef ”: 没有声明变量时忽略“tagGPFIDL_FLAGS”的左侧
e:\vsproject\browserdataextractor\src\exifmanager.cpp(266): warning C4101: “e”: 未引用的局部变量
    正在创建库 E:\VsProject\BrowserDataExtractor\Release\BrowserDataExtractor.lib 和对象 E:\VsProject\BrowserDataExtractor\Release\BrowserDataExtractor.exp
Init_BroswerMessage.obj : error LNK2001: 无法解析的外部符号 "class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > __cdecl NumberToString(int)" (?NumberToString@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z)
E:\VsProject\BrowserDataExtractor\Release\BrowserDataExtractor.dll : fatal error LNK1120: 1 个无法解析的外部命令
