﻿C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v150\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  ChromeBrowser.cpp
e:\vsproject\browserdataextractor\src\chromebrowser.cpp(2): warning C4603: “WINVER”: 未定义宏或在预编译头使用后定义发生改变
  e:\vsproject\browserdataextractor\src\chromebrowser.cpp(2): note: 将宏添加到预编译头中，而不是在此处定义
  e:\vsproject\browserdataextractor\src\chromebrowser.cpp(7): note: 使用预编译头
e:\vsproject\browserdataextractor\src\chromebrowser.cpp(3): warning C4603: “_WIN32_WINNT”: 未定义宏或在预编译头使用后定义发生改变
  e:\vsproject\browserdataextractor\src\chromebrowser.cpp(3): note: 将宏添加到预编译头中，而不是在此处定义
  e:\vsproject\browserdataextractor\src\chromebrowser.cpp(7): note: 使用预编译头
e:\vsproject\browserdataextractor\src\chromebrowser.cpp(4): warning C4603: “_WIN32_IE”: 未定义宏或在预编译头使用后定义发生改变
  e:\vsproject\browserdataextractor\src\chromebrowser.cpp(4): note: 将宏添加到预编译头中，而不是在此处定义
  e:\vsproject\browserdataextractor\src\chromebrowser.cpp(7): note: 使用预编译头
e:\vsproject\browserdataextractor\src\chromebrowser.cpp(5): warning C4603: “NTDDI_VERSION”: 未定义宏或在预编译头使用后定义发生改变
  e:\vsproject\browserdataextractor\src\chromebrowser.cpp(5): note: 将宏添加到预编译头中，而不是在此处定义
  e:\vsproject\browserdataextractor\src\chromebrowser.cpp(7): note: 使用预编译头
e:\vsproject\browserdataextractor\include\chromebrowser.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\browserdataextractor.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\program files (x86)\microsoft sdks\windows\v7.1a\include\shlobj.h(1151): warning C4091: “typedef ”: 没有声明变量时忽略“tagGPFIDL_FLAGS”的左侧
e:\vsproject\browserdataextractor\include\utils.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\src\chromebrowser.cpp(472): warning C4018: “<”: 有符号/无符号不匹配
  DriverManager.cpp
e:\vsproject\browserdataextractor\src\drivermanager.cpp(1): warning C4603: “_CRT_SECURE_NO_WARNINGS”: 未定义宏或在预编译头使用后定义发生改变
  e:\vsproject\browserdataextractor\src\drivermanager.cpp(1): note: 将宏添加到预编译头中，而不是在此处定义
  e:\vsproject\browserdataextractor\src\drivermanager.cpp(2): note: 使用预编译头
e:\vsproject\browserdataextractor\include\utils.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\browserdataextractor.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\program files (x86)\microsoft sdks\windows\v7.1a\include\shlobj.h(1151): warning C4091: “typedef ”: 没有声明变量时忽略“tagGPFIDL_FLAGS”的左侧
e:\vsproject\browserdataextractor\src\drivermanager.cpp(372): warning C4101: “e”: 未引用的局部变量
e:\vsproject\browserdataextractor\src\drivermanager.cpp(425): warning C4101: “e”: 未引用的局部变量
  ExifManager.cpp
e:\vsproject\browserdataextractor\src\exifmanager.cpp(2): warning C4603: “_CRT_SECURE_NO_WARNINGS”: 未定义宏或在预编译头使用后定义发生改变
  e:\vsproject\browserdataextractor\src\exifmanager.cpp(2): note: 将宏添加到预编译头中，而不是在此处定义
  e:\vsproject\browserdataextractor\src\exifmanager.cpp(7): note: 使用预编译头
c:\program files (x86)\microsoft sdks\windows\v7.1a\include\shlobj.h(1151): warning C4091: “typedef ”: 没有声明变量时忽略“tagGPFIDL_FLAGS”的左侧
e:\vsproject\browserdataextractor\include\utils.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\browserdataextractor.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  FirefoxBrowser.cpp
e:\vsproject\browserdataextractor\src\firefoxbrowser.cpp(1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\src\firefoxbrowser.cpp(1): warning C4603: “WINVER”: 未定义宏或在预编译头使用后定义发生改变
  e:\vsproject\browserdataextractor\src\firefoxbrowser.cpp(1): note: 将宏添加到预编译头中，而不是在此处定义
  e:\vsproject\browserdataextractor\src\firefoxbrowser.cpp(6): note: 使用预编译头
e:\vsproject\browserdataextractor\src\firefoxbrowser.cpp(2): warning C4603: “_WIN32_WINNT”: 未定义宏或在预编译头使用后定义发生改变
  e:\vsproject\browserdataextractor\src\firefoxbrowser.cpp(2): note: 将宏添加到预编译头中，而不是在此处定义
  e:\vsproject\browserdataextractor\src\firefoxbrowser.cpp(6): note: 使用预编译头
e:\vsproject\browserdataextractor\src\firefoxbrowser.cpp(3): warning C4603: “_WIN32_IE”: 未定义宏或在预编译头使用后定义发生改变
  e:\vsproject\browserdataextractor\src\firefoxbrowser.cpp(3): note: 将宏添加到预编译头中，而不是在此处定义
  e:\vsproject\browserdataextractor\src\firefoxbrowser.cpp(6): note: 使用预编译头
e:\vsproject\browserdataextractor\src\firefoxbrowser.cpp(4): warning C4603: “NTDDI_VERSION”: 未定义宏或在预编译头使用后定义发生改变
  e:\vsproject\browserdataextractor\src\firefoxbrowser.cpp(4): note: 将宏添加到预编译头中，而不是在此处定义
  e:\vsproject\browserdataextractor\src\firefoxbrowser.cpp(6): note: 使用预编译头
e:\vsproject\browserdataextractor\include\firefoxbrowser.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\browserdataextractor.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\program files (x86)\microsoft sdks\windows\v7.1a\include\shlobj.h(1151): warning C4091: “typedef ”: 没有声明变量时忽略“tagGPFIDL_FLAGS”的左侧
e:\vsproject\browserdataextractor\include\utils.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\src\firefoxbrowser.cpp(721): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\src\firefoxbrowser.cpp(1336): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  IEBrowser.cpp
e:\vsproject\browserdataextractor\src\iebrowser.cpp(1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\src\iebrowser.cpp(1): warning C4603: “WINVER”: 未定义宏或在预编译头使用后定义发生改变
  e:\vsproject\browserdataextractor\src\iebrowser.cpp(1): note: 将宏添加到预编译头中，而不是在此处定义
  e:\vsproject\browserdataextractor\src\iebrowser.cpp(6): note: 使用预编译头
e:\vsproject\browserdataextractor\src\iebrowser.cpp(2): warning C4603: “_WIN32_WINNT”: 未定义宏或在预编译头使用后定义发生改变
  e:\vsproject\browserdataextractor\src\iebrowser.cpp(2): note: 将宏添加到预编译头中，而不是在此处定义
  e:\vsproject\browserdataextractor\src\iebrowser.cpp(6): note: 使用预编译头
e:\vsproject\browserdataextractor\src\iebrowser.cpp(3): warning C4603: “_WIN32_IE”: 未定义宏或在预编译头使用后定义发生改变
  e:\vsproject\browserdataextractor\src\iebrowser.cpp(3): note: 将宏添加到预编译头中，而不是在此处定义
  e:\vsproject\browserdataextractor\src\iebrowser.cpp(6): note: 使用预编译头
e:\vsproject\browserdataextractor\src\iebrowser.cpp(4): warning C4603: “NTDDI_VERSION”: 未定义宏或在预编译头使用后定义发生改变
  e:\vsproject\browserdataextractor\src\iebrowser.cpp(4): note: 将宏添加到预编译头中，而不是在此处定义
  e:\vsproject\browserdataextractor\src\iebrowser.cpp(6): note: 使用预编译头
e:\vsproject\browserdataextractor\include\iebrowser.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\browserdataextractor.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\program files (x86)\microsoft sdks\windows\v7.1a\include\shlobj.h(1151): warning C4091: “typedef ”: 没有声明变量时忽略“tagGPFIDL_FLAGS”的左侧
e:\vsproject\browserdataextractor\include\chromebrowser.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\utils.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\src\iebrowser.cpp(749): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\src\iebrowser.cpp(1412): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  Init_BroswerMessage.cpp
e:\vsproject\browserdataextractor\include\browserdataextractor.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\program files (x86)\microsoft sdks\windows\v7.1a\include\shlobj.h(1151): warning C4091: “typedef ”: 没有声明变量时忽略“tagGPFIDL_FLAGS”的左侧
e:\vsproject\browserdataextractor\include\chromebrowser.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\firefoxbrowser.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\iebrowser.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\utils.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\processinfomanager.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\init_broswermessage.h(20): warning C4190: “Init_BroswerMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(22): warning C4190: “Init_WifiInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(23): warning C4190: “Init_ServiceInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(24): warning C4190: “Init_ProcessallInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(25): warning C4190: “Init_ShareInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(27): warning C4190: “Init_DriverInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(28): warning C4190: “Init_FirewallInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(29): warning C4190: “Init_ScreensaverInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(30): warning C4190: “Init_PasswordPolicyInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(31): warning C4190: “Init_UserAccountInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(32): warning C4190: “Init_AccountLockoutPolicyInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(33): warning C4190: “Init_StartupInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(34): warning C4190: “Init_NetworkConnectionInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(35): warning C4190: “Init_ExifExtractorMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\src\init_broswermessage.cpp(2098): warning C4101: “e”: 未引用的局部变量
  NetworkConnectionManager.cpp
e:\vsproject\browserdataextractor\include\utils.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\browserdataextractor.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\program files (x86)\microsoft sdks\windows\v7.1a\include\shlobj.h(1151): warning C4091: “typedef ”: 没有声明变量时忽略“tagGPFIDL_FLAGS”的左侧
  ProcessInfoManager.cpp
e:\vsproject\browserdataextractor\src\processinfomanager.cpp(1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\processinfomanager.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\utils.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\browserdataextractor.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\program files (x86)\microsoft sdks\windows\v7.1a\include\shlobj.h(1151): warning C4091: “typedef ”: 没有声明变量时忽略“tagGPFIDL_FLAGS”的左侧
e:\vsproject\browserdataextractor\src\processinfomanager.cpp(383): warning C4101: “buffer”: 未引用的局部变量
  ProcessManager.cpp
e:\vsproject\browserdataextractor\include\utils.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\browserdataextractor.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\program files (x86)\microsoft sdks\windows\v7.1a\include\shlobj.h(1151): warning C4091: “typedef ”: 没有声明变量时忽略“tagGPFIDL_FLAGS”的左侧
  ServiceManager.cpp
e:\vsproject\browserdataextractor\include\utils.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\browserdataextractor.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\program files (x86)\microsoft sdks\windows\v7.1a\include\shlobj.h(1151): warning C4091: “typedef ”: 没有声明变量时忽略“tagGPFIDL_FLAGS”的左侧
  StartupManager.cpp
e:\vsproject\browserdataextractor\include\utils.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\browserdataextractor.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\program files (x86)\microsoft sdks\windows\v7.1a\include\shlobj.h(1151): warning C4091: “typedef ”: 没有声明变量时忽略“tagGPFIDL_FLAGS”的左侧
  Utils.cpp
e:\vsproject\browserdataextractor\src\utils.cpp(1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\utils.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\browserdataextractor.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\program files (x86)\microsoft sdks\windows\v7.1a\include\shlobj.h(1151): warning C4091: “typedef ”: 没有声明变量时忽略“tagGPFIDL_FLAGS”的左侧
e:\vsproject\browserdataextractor\src\utils.cpp(805): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\src\utils.cpp(1087): warning C4101: “e”: 未引用的局部变量
    正在创建库 E:\VsProject\BrowserDataExtractor\Release\BrowserDataExtractor.lib 和对象 E:\VsProject\BrowserDataExtractor\Release\BrowserDataExtractor.exp
  正在生成代码
  69 of 10439 functions ( 0.7%) were compiled, the rest were copied from previous compilation.
    7 functions were new in current compilation
    49 functions had inline decision re-evaluated but remain unchanged
  已完成代码的生成
  BrowserDataExtractor.vcxproj -> E:\VsProject\BrowserDataExtractor\Release\BrowserDataExtractor.dll
  'pwsh.exe' 不是内部或外部命令，也不是可运行的程序
  或批处理文件。
