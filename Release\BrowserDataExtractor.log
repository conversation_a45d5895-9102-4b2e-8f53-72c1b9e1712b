﻿C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v150\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  ExifManager.cpp
e:\vsproject\browserdataextractor\src\exifmanager.cpp(2): warning C4603: “_CRT_SECURE_NO_WARNINGS”: 未定义宏或在预编译头使用后定义发生改变
  e:\vsproject\browserdataextractor\src\exifmanager.cpp(2): note: 将宏添加到预编译头中，而不是在此处定义
  e:\vsproject\browserdataextractor\src\exifmanager.cpp(7): note: 使用预编译头
c:\program files (x86)\microsoft sdks\windows\v7.1a\include\shlobj.h(1151): warning C4091: “typedef ”: 没有声明变量时忽略“tagGPFIDL_FLAGS”的左侧
e:\vsproject\browserdataextractor\src\exifmanager.cpp(228): error C4996: 'localtime': This function or variable may be unsafe. Consider using localtime_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  c:\program files (x86)\windows kits\10\include\10.0.10240.0\ucrt\time.h(505): note: 参见“localtime”的声明
e:\vsproject\browserdataextractor\src\exifmanager.cpp(238): error C4996: 'strncpy': This function or variable may be unsafe. Consider using strncpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  c:\program files (x86)\windows kits\10\include\10.0.10240.0\ucrt\string.h(340): note: 参见“strncpy”的声明
e:\vsproject\browserdataextractor\src\exifmanager.cpp(256): warning C4101: “e”: 未引用的局部变量
  Init_BroswerMessage.cpp
e:\vsproject\browserdataextractor\include\browserdataextractor.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\program files (x86)\microsoft sdks\windows\v7.1a\include\shlobj.h(1151): warning C4091: “typedef ”: 没有声明变量时忽略“tagGPFIDL_FLAGS”的左侧
e:\vsproject\browserdataextractor\include\chromebrowser.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\firefoxbrowser.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\iebrowser.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\utils.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\processinfomanager.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\init_broswermessage.h(20): warning C4190: “Init_BroswerMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(22): warning C4190: “Init_WifiInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(23): warning C4190: “Init_ServiceInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(24): warning C4190: “Init_ProcessallInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(25): warning C4190: “Init_ShareInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(27): warning C4190: “Init_DriverInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(28): warning C4190: “Init_FirewallInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(29): warning C4190: “Init_ScreensaverInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(30): warning C4190: “Init_PasswordPolicyInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(31): warning C4190: “Init_UserAccountInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(32): warning C4190: “Init_AccountLockoutPolicyInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(33): warning C4190: “Init_StartupInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(34): warning C4190: “Init_NetworkConnectionInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(35): warning C4190: “Init_ExifExtractorMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\src\init_broswermessage.cpp(2106): warning C4101: “e”: 未引用的局部变量
