﻿C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v150\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  Init_BroswerMessage.cpp
e:\vsproject\browserdataextractor\include\browserdataextractor.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\program files (x86)\microsoft sdks\windows\v7.1a\include\shlobj.h(1151): warning C4091: “typedef ”: 没有声明变量时忽略“tagGPFIDL_FLAGS”的左侧
e:\vsproject\browserdataextractor\include\chromebrowser.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\firefoxbrowser.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\iebrowser.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\utils.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\processinfomanager.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\init_broswermessage.h(20): warning C4190: “Init_BroswerMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(22): warning C4190: “Init_WifiInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(23): warning C4190: “Init_ServiceInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(24): warning C4190: “Init_ProcessallInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(25): warning C4190: “Init_ShareInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(27): warning C4190: “Init_DriverInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(28): warning C4190: “Init_FirewallInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(29): warning C4190: “Init_ScreensaverInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(30): warning C4190: “Init_PasswordPolicyInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(31): warning C4190: “Init_UserAccountInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(32): warning C4190: “Init_AccountLockoutPolicyInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(33): warning C4190: “Init_StartupInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(34): warning C4190: “Init_NetworkConnectionInfoMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\include\init_broswermessage.h(35): warning C4190: “Init_ExifExtractorMsg”有指定的 C 链接，但返回了与 C 不兼容的 UDT“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”
  c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\xstring(4373): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
e:\vsproject\browserdataextractor\src\init_broswermessage.cpp(2098): warning C4101: “e”: 未引用的局部变量
  PasswordPolicyManager.cpp
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(350): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(350): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(453): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(453): error C3861: “UTF8ToWString”: 找不到标识符
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(651): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(651): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(652): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(652): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(691): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(691): error C3861: “UTF8ToWString”: 找不到标识符
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(697): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(697): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(698): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(698): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(779): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(779): error C3861: “UTF8ToWString”: 找不到标识符
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(792): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(792): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(809): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(809): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(1043): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(1043): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(1105): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(1105): error C3861: “UTF8ToWString”: 找不到标识符
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(1129): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(1129): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(1310): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\passwordpolicymanager.cpp(1310): error C3861: “UTF8ToWString”: 找不到标识符
  ProcessManager.cpp
e:\vsproject\browserdataextractor\include\utils.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vsproject\browserdataextractor\include\browserdataextractor.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\program files (x86)\microsoft sdks\windows\v7.1a\include\shlobj.h(1151): warning C4091: “typedef ”: 没有声明变量时忽略“tagGPFIDL_FLAGS”的左侧
  ScreensaverManager.cpp
c:\program files (x86)\microsoft sdks\windows\v7.1a\include\shlobj.h(1151): warning C4091: “typedef ”: 没有声明变量时忽略“tagGPFIDL_FLAGS”的左侧
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(410): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(410): error C3861: “UTF8ToWString”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(446): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(446): error C3861: “UTF8ToWString”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(476): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(476): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(485): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(485): error C3861: “UTF8ToWString”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(515): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(515): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(549): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(549): error C3861: “UTF8ToWString”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(575): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(575): error C3861: “UTF8ToWString”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(601): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(601): error C3861: “UTF8ToWString”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(673): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(673): error C3861: “UTF8ToWString”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(678): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(678): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(727): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(727): error C3861: “UTF8ToWString”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(755): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(755): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(761): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(761): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(777): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(777): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(782): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(782): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(788): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(788): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(904): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(904): error C3861: “UTF8ToWString”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(939): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(939): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(969): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(969): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(980): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(980): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(999): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(999): error C3861: “UTF8ToWString”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(1033): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(1033): error C3861: “UTF8ToWString”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(1056): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(1056): error C3861: “UTF8ToWString”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(1081): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(1081): error C3861: “UTF8ToWString”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(1172): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(1172): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(1314): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(1314): error C3861: “UTF8ToWString”: 找不到标识符
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(1337): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\screensavermanager.cpp(1337): error C3861: “UTF8ToWString”: 找不到标识符
  ShareManager.cpp
e:\vsproject\browserdataextractor\src\sharemanager.cpp(65): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\sharemanager.cpp(65): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\sharemanager.cpp(66): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\sharemanager.cpp(66): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\sharemanager.cpp(68): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\sharemanager.cpp(68): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\sharemanager.cpp(214): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\sharemanager.cpp(214): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\sharemanager.cpp(215): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\sharemanager.cpp(215): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\sharemanager.cpp(217): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\sharemanager.cpp(217): error C3861: “WStringToUTF8”: 找不到标识符
  WiFiManager.cpp
e:\vsproject\browserdataextractor\src\wifimanager.cpp(233): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\wifimanager.cpp(233): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\wifimanager.cpp(345): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\wifimanager.cpp(345): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\wifimanager.cpp(502): error C2653: “Utils”: 不是类或命名空间名称
e:\vsproject\browserdataextractor\src\wifimanager.cpp(502): error C3861: “WStringToUTF8”: 找不到标识符
e:\vsproject\browserdataextractor\src\wifimanager.cpp(724): warning C4566: 由通用字符名称“\u2713”表示的字符不能在当前代码页(936)中表示出来
e:\vsproject\browserdataextractor\src\wifimanager.cpp(725): warning C4566: 由通用字符名称“\u2713”表示的字符不能在当前代码页(936)中表示出来
e:\vsproject\browserdataextractor\src\wifimanager.cpp(728): warning C4566: 由通用字符名称“\u2717”表示的字符不能在当前代码页(936)中表示出来
