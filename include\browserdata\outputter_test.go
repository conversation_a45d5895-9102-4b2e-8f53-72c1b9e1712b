package browserdata

import (
	"os"
	"testing"
)

func TestNewOutPutter(t *testing.T) {
	t.<PERSON>()
	out := newOutPutter("json")
	if out == nil {
		t.<PERSON><PERSON><PERSON>("New() returned nil")
	}
	f, err := out.CreateFile("results", "test.json")
	if err != nil {
		t.<PERSON>("CreateFile() returned an error", err)
	}
	defer os.Remove<PERSON>ll("results")
	err = out.Write(nil, f)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("Write() returned an error", err)
	}
}
