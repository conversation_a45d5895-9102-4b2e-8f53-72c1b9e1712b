﻿#include "pch.h"
#include "ServiceManager.h"
#include "Utils.h"  // 添加Utils.h包含，用于统一的字符串转换
#include <iostream>
#include <sstream>
#include <memory>
#include <ctime>
#include <iomanip>
#include <fstream>

#pragma comment(lib, "advapi32.lib")

ServiceManager::ServiceManager() : m_scManager(nullptr), m_initialized(false) {
}

ServiceManager::~ServiceManager() {
    Cleanup();
}

bool ServiceManager::Initialize() {
    if (m_initialized) {
        return true;
    }

    // 打开服务控制管理器
    m_scManager = OpenSCManager(nullptr, nullptr, SC_MANAGER_ENUMERATE_SERVICE | SC_MANAGER_CONNECT);
    if (m_scManager == nullptr) {
        std::cout << "Failed to open Service Control Manager: " << GetLastError() << std::endl;
        return false;
    }

    m_initialized = true;
    return true;
}

void ServiceManager::Cleanup() {
    if (m_scManager) {
        CloseServiceHandle(m_scManager);
        m_scManager = nullptr;
    }
    m_initialized = false;
}

std::vector<ServiceData> ServiceManager::GetAllServices() {
    std::vector<ServiceData> allServices;

    if (!m_initialized) {
        return allServices;
    }

    // 第一次调用获取所需缓冲区大小
    DWORD bytesNeeded = 0;
    DWORD servicesReturned = 0;
    DWORD resumeHandle = 0;

    EnumServicesStatusEx(m_scManager, SC_ENUM_PROCESS_INFO, SERVICE_WIN32,
                        SERVICE_STATE_ALL, nullptr, 0, &bytesNeeded,
                        &servicesReturned, &resumeHandle, nullptr);

    if (GetLastError() != ERROR_MORE_DATA) {
        std::cout << "Failed to get services buffer size: " << GetLastError() << std::endl;
        return allServices;
    }

    // 分配缓冲区并获取服务列表
    std::vector<BYTE> buffer(bytesNeeded);
    LPENUM_SERVICE_STATUS_PROCESS services = reinterpret_cast<LPENUM_SERVICE_STATUS_PROCESS>(buffer.data());

    if (!EnumServicesStatusEx(m_scManager, SC_ENUM_PROCESS_INFO, SERVICE_WIN32,
                             SERVICE_STATE_ALL, buffer.data(), bytesNeeded,
                             &bytesNeeded, &servicesReturned, &resumeHandle, nullptr)) {
        std::cout << "Failed to enumerate services: " << GetLastError() << std::endl;
        return allServices;
    }

    // 遍历服务列表
    for (DWORD i = 0; i < servicesReturned; i++) {
        ServiceData serviceData;

        // 基本信息
        serviceData.service_name = Utils::WStringToUTF8(services[i].lpServiceName);
        serviceData.display_name = Utils::WStringToUTF8(services[i].lpDisplayName);
        serviceData.status = GetServiceStatusString(services[i].ServiceStatusProcess.dwCurrentState);
        serviceData.process_id = services[i].ServiceStatusProcess.dwProcessId;

        // 获取详细信息
        ServiceData detailedInfo = GetServiceDetails(serviceData.service_name);
        if (!detailedInfo.service_name.empty()) {
            serviceData.description = detailedInfo.description;
            serviceData.startup_type = detailedInfo.startup_type;
            serviceData.service_type = detailedInfo.service_type;
            serviceData.account = detailedInfo.account;
            serviceData.binary_path = detailedInfo.binary_path;
            serviceData.dependencies = detailedInfo.dependencies;
            serviceData.can_stop = detailedInfo.can_stop;
            serviceData.can_pause = detailedInfo.can_pause;
        }

        allServices.push_back(serviceData);
    }

    return allServices;
}

nlohmann::json ServiceManager::GetServicesInfoAsJson() {
    nlohmann::json result;

    // 获取所有服务
    std::vector<ServiceData> services = GetAllServices();
    result["services"] = services;

    // 统计信息
    int runningCount = 0;
    int stoppedCount = 0;
    int pausedCount = 0;

    for (const auto& service : services) {
        if (service.status == "Running") {
            runningCount++;
        } else if (service.status == "Stopped") {
            stoppedCount++;
        } else if (service.status == "Paused") {
            pausedCount++;
        }
    }

    // 添加元数据
    result["metadata"] = {
        {"total_services", services.size()},
        {"running_services", runningCount},
        {"stopped_services", stoppedCount},
        {"paused_services", pausedCount},
        {"scan_time", std::time(nullptr)},
        {"version", "1.0"}
    };

    return result;
}

bool ServiceManager::SaveServicesInfoToFile(const std::string& filename) {
    try {
        // 获取服务信息的JSON数据
        nlohmann::json serviceInfo = GetServicesInfoAsJson();

        // 打开文件进行写入
        std::ofstream file(filename, std::ios::out | std::ios::trunc);
        if (!file.is_open()) {
            std::cout << "Failed to open file for writing: " << filename << std::endl;
            return false;
        }

        // 将JSON数据写入文件（格式化输出，缩进为4个空格）
        file << serviceInfo.dump(4);
        file.close();

        std::cout << "Services information saved to: " << filename << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cout << "Error saving services information to file: " << e.what() << std::endl;
        return false;
    }
}

// 注意：字符串转换函数已移至Utils类中，使用以下方法替代：
// Utils::WStringToUTF8() 替代 ConvertToString()
// Utils::UTF8ToWString() 替代 ConvertToWString()

std::string ServiceManager::GetServiceStatusString(DWORD status) {
    switch (status) {
        case SERVICE_STOPPED: return "Stopped";
        case SERVICE_START_PENDING: return "Start Pending";
        case SERVICE_STOP_PENDING: return "Stop Pending";
        case SERVICE_RUNNING: return "Running";
        case SERVICE_CONTINUE_PENDING: return "Continue Pending";
        case SERVICE_PAUSE_PENDING: return "Pause Pending";
        case SERVICE_PAUSED: return "Paused";
        default: return "Unknown";
    }
}

std::string ServiceManager::GetStartupTypeString(DWORD startType) {
    switch (startType) {
        case SERVICE_AUTO_START: return "Automatic";
        case SERVICE_BOOT_START: return "Boot";
        case SERVICE_DEMAND_START: return "Manual";
        case SERVICE_DISABLED: return "Disabled";
        case SERVICE_SYSTEM_START: return "System";
        default: return "Unknown";
    }
}

std::string ServiceManager::GetServiceTypeString(DWORD serviceType) {
    std::string result;

    if (serviceType & SERVICE_WIN32_OWN_PROCESS) {
        result += "Win32 Own Process";
    }
    if (serviceType & SERVICE_WIN32_SHARE_PROCESS) {
        if (!result.empty()) result += ", ";
        result += "Win32 Share Process";
    }
    if (serviceType & SERVICE_KERNEL_DRIVER) {
        if (!result.empty()) result += ", ";
        result += "Kernel Driver";
    }
    if (serviceType & SERVICE_FILE_SYSTEM_DRIVER) {
        if (!result.empty()) result += ", ";
        result += "File System Driver";
    }
    if (serviceType & SERVICE_INTERACTIVE_PROCESS) {
        if (!result.empty()) result += ", ";
        result += "Interactive";
    }

    return result.empty() ? "Unknown" : result;
}

ServiceData ServiceManager::GetServiceDetails(const std::string& serviceName) {
    ServiceData serviceData;
    serviceData.service_name = serviceName;

    // 打开服务句柄
    std::wstring wServiceName = Utils::UTF8ToWString(serviceName);
    SC_HANDLE serviceHandle = OpenService(m_scManager, wServiceName.c_str(),
                                         SERVICE_QUERY_CONFIG | SERVICE_QUERY_STATUS);

    if (serviceHandle == nullptr) {
        return serviceData;
    }

    // 获取服务配置信息
    GetServiceConfig(serviceHandle, serviceData);

    // 获取服务描述
    serviceData.description = GetServiceDescription(serviceHandle);

    CloseServiceHandle(serviceHandle);
    return serviceData;
}

std::string ServiceManager::GetServiceDescription(SC_HANDLE serviceHandle) {
    DWORD bytesNeeded = 0;

    // 第一次调用获取所需缓冲区大小
    QueryServiceConfig2(serviceHandle, SERVICE_CONFIG_DESCRIPTION, nullptr, 0, &bytesNeeded);

    if (GetLastError() != ERROR_INSUFFICIENT_BUFFER) {
        return "";
    }

    // 分配缓冲区并获取描述
    std::vector<BYTE> buffer(bytesNeeded);
    LPSERVICE_DESCRIPTION serviceDesc = reinterpret_cast<LPSERVICE_DESCRIPTION>(buffer.data());

    if (QueryServiceConfig2(serviceHandle, SERVICE_CONFIG_DESCRIPTION,
                           buffer.data(), bytesNeeded, &bytesNeeded)) {
        if (serviceDesc->lpDescription) {
            return Utils::WStringToUTF8(serviceDesc->lpDescription);
        }
    }

    return "";
}

bool ServiceManager::GetServiceConfig(SC_HANDLE serviceHandle, ServiceData& serviceData) {
    DWORD bytesNeeded = 0;

    // 第一次调用获取所需缓冲区大小
    QueryServiceConfig(serviceHandle, nullptr, 0, &bytesNeeded);

    if (GetLastError() != ERROR_INSUFFICIENT_BUFFER) {
        return false;
    }

    // 分配缓冲区并获取配置
    std::vector<BYTE> buffer(bytesNeeded);
    LPQUERY_SERVICE_CONFIG serviceConfig = reinterpret_cast<LPQUERY_SERVICE_CONFIG>(buffer.data());

    if (QueryServiceConfig(serviceHandle, serviceConfig, bytesNeeded, &bytesNeeded)) {
        serviceData.startup_type = GetStartupTypeString(serviceConfig->dwStartType);
        serviceData.service_type = GetServiceTypeString(serviceConfig->dwServiceType);

        if (serviceConfig->lpServiceStartName) {
            serviceData.account = Utils::WStringToUTF8(serviceConfig->lpServiceStartName);
        }

        if (serviceConfig->lpBinaryPathName) {
            serviceData.binary_path = Utils::WStringToUTF8(serviceConfig->lpBinaryPathName);
        }

        // 获取依赖服务
        if (serviceConfig->lpDependencies) {
            std::wstring deps = serviceConfig->lpDependencies;
            serviceData.dependencies = Utils::WStringToUTF8(deps);
        }

        return true;
    }

    return false;
}
