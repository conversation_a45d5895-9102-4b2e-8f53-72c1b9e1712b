﻿#include "pch.h"
#include "NetworkConnectionManager.h"
#include "Utils.h"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <memory>
#include <algorithm>
#include <map>
#include <set>
#include <ctime>
#include <vector>

// 确保Windows XP兼容性
#ifndef _WIN32_WINNT
#define _WIN32_WINNT 0x0501  // Windows XP
#endif

// 包含Winsock头文件
#include <winsock2.h>
#include <ws2tcpip.h>

// 链接必要的库
#pragma comment(lib, "psapi.lib")
#pragma comment(lib, "shell32.lib")
#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib, "iphlpapi.lib")

// 获取所有网络连接信息
std::vector<NetworkConnectionInfo> NetworkConnectionManager::GetAllNetworkConnections() {
    std::vector<NetworkConnectionInfo> connections;
    
    // 初始化Winsock
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        std::cerr << "WSAStartup failed, error: " << WSAGetLastError() << std::endl;
        return connections;
    }
    
    // 获取TCP连接
    std::vector<NetworkConnectionInfo> tcpConnections = GetTcpConnections();
    connections.insert(connections.end(), tcpConnections.begin(), tcpConnections.end());
    
    // 获取UDP连接
    std::vector<NetworkConnectionInfo> udpConnections = GetUdpConnections();
    connections.insert(connections.end(), udpConnections.begin(), udpConnections.end());
    
    // 清理Winsock
    WSACleanup();
    
    return connections;
}

// 获取TCP连接表
std::vector<NetworkConnectionInfo> NetworkConnectionManager::GetTcpConnections() {
    std::vector<NetworkConnectionInfo> connections;
    
    PMIB_TCPTABLE_OWNER_PID pTcpTable = nullptr;
    DWORD dwSize = 0;
    DWORD dwRetVal = 0;
    
    // 首先获取需要的缓冲区大小
    dwRetVal = GetExtendedTcpTable(nullptr, &dwSize, TRUE, AF_INET, TCP_TABLE_OWNER_PID_ALL, 0);
    if (dwRetVal == ERROR_INSUFFICIENT_BUFFER) {
        pTcpTable = (PMIB_TCPTABLE_OWNER_PID)malloc(dwSize);
        if (pTcpTable == nullptr) {
            std::cerr << "Memory allocation failed for TCP table" << std::endl;
            return connections;
        }
    } else {
        std::cerr << "GetExtendedTcpTable failed, error: " << dwRetVal << std::endl;
        return connections;
    }
    
    // 获取TCP连接表
    dwRetVal = GetExtendedTcpTable(pTcpTable, &dwSize, TRUE, AF_INET, TCP_TABLE_OWNER_PID_ALL, 0);
    if (dwRetVal == NO_ERROR) {
        // 遍历TCP连接表
        for (DWORD i = 0; i < pTcpTable->dwNumEntries; i++) {
            NetworkConnectionInfo conn;
            
            // 获取本地地址和端口
            conn.localAddress = IpAddressToString(pTcpTable->table[i].dwLocalAddr);
            conn.localPort = ntohs((u_short)pTcpTable->table[i].dwLocalPort);
            
            // 获取远程地址和端口
            conn.remoteAddress = IpAddressToString(pTcpTable->table[i].dwRemoteAddr);
            conn.remotePort = ntohs((u_short)pTcpTable->table[i].dwRemotePort);
            
            // 获取连接状态和拥有进程ID
            conn.state = pTcpTable->table[i].dwState;
            conn.owningPid = pTcpTable->table[i].dwOwningPid;
            conn.protocol = "TCP";

            // 获取连接创建时间
            conn.createTime = GetConnectionCreateTime(conn.owningPid, "TCP",
                                                     conn.localAddress, conn.localPort);

            connections.push_back(conn);
        }
    } else {
        std::cerr << "GetExtendedTcpTable failed, error: " << dwRetVal << std::endl;
    }
    
    // 释放内存
    if (pTcpTable != nullptr) {
        free(pTcpTable);
    }
    
    return connections;
}

// 获取UDP连接表
std::vector<NetworkConnectionInfo> NetworkConnectionManager::GetUdpConnections() {
    std::vector<NetworkConnectionInfo> connections;
    
    PMIB_UDPTABLE_OWNER_PID pUdpTable = nullptr;
    DWORD dwSize = 0;
    DWORD dwRetVal = 0;
    
    // 首先获取需要的缓冲区大小
    dwRetVal = GetExtendedUdpTable(nullptr, &dwSize, TRUE, AF_INET, UDP_TABLE_OWNER_PID, 0);
    if (dwRetVal == ERROR_INSUFFICIENT_BUFFER) {
        pUdpTable = (PMIB_UDPTABLE_OWNER_PID)malloc(dwSize);
        if (pUdpTable == nullptr) {
            std::cerr << "Memory allocation failed for UDP table" << std::endl;
            return connections;
        }
    } else {
        std::cerr << "GetExtendedUdpTable failed, error: " << dwRetVal << std::endl;
        return connections;
    }
    
    // 获取UDP连接表
    dwRetVal = GetExtendedUdpTable(pUdpTable, &dwSize, TRUE, AF_INET, UDP_TABLE_OWNER_PID, 0);
    if (dwRetVal == NO_ERROR) {
        // 遍历UDP连接表
        for (DWORD i = 0; i < pUdpTable->dwNumEntries; i++) {
            NetworkConnectionInfo conn;
            
            // 获取本地地址和端口
            conn.localAddress = IpAddressToString(pUdpTable->table[i].dwLocalAddr);
            conn.localPort = ntohs((u_short)pUdpTable->table[i].dwLocalPort);
            
            // UDP没有远程连接信息
            conn.remoteAddress = "0.0.0.0";
            conn.remotePort = 0;
            
            // UDP没有连接状态，设置为0
            conn.state = 0;
            conn.owningPid = pUdpTable->table[i].dwOwningPid;
            conn.protocol = "UDP";

            // 获取连接创建时间
            conn.createTime = GetConnectionCreateTime(conn.owningPid, "UDP",
                                                     conn.localAddress, conn.localPort);

            connections.push_back(conn);
        }
    } else {
        std::cerr << "GetExtendedUdpTable failed, error: " << dwRetVal << std::endl;
    }
    
    // 释放内存
    if (pUdpTable != nullptr) {
        free(pUdpTable);
    }
    
    return connections;
}

// 获取指定进程的网络连接
std::vector<NetworkConnectionInfo> NetworkConnectionManager::GetProcessConnections(DWORD pid) {
    std::vector<NetworkConnectionInfo> processConnections;
    std::vector<NetworkConnectionInfo> allConnections = GetAllNetworkConnections();
    
    // 筛选出指定进程的连接
    for (const auto& conn : allConnections) {
        if (conn.owningPid == pid) {
            processConnections.push_back(conn);
        }
    }
    
    return processConnections;
}

// IP地址网络字节序转换为字符串
std::string NetworkConnectionManager::IpAddressToString(DWORD ipAddress) {
    struct in_addr addr;
    addr.s_addr = ipAddress;
    
    // 使用inet_ntoa将网络字节序IP地址转换为字符串
    // 这个函数在Windows XP上可用
    const char* ipStr = inet_ntoa(addr);
    if (ipStr) {
        return std::string(ipStr);
    }
    
    return "0.0.0.0"; // 默认返回
}

// 注意：字符串转换函数已移至Utils类中，使用以下方法替代：
// Utils::WStringToUTF8() 替代 WideStringToUTF8()
// Utils::UTF8ToWString() 替代 UTF8ToWideString()

// 获取所有有网络连接的进程信息
std::vector<NetworkProcessInfo> NetworkConnectionManager::GetAllNetworkProcesses() {
    std::vector<NetworkProcessInfo> networkProcesses;
    std::vector<NetworkConnectionInfo> allConnections = GetAllNetworkConnections();

    // 按进程ID分组连接
    std::map<DWORD, std::vector<NetworkConnectionInfo>> processConnectionMap;
    for (const auto& conn : allConnections) {
        processConnectionMap[conn.owningPid].push_back(conn);
    }

    // 为每个有网络连接的进程创建进程信息
    for (const auto& pair : processConnectionMap) {
        DWORD pid = pair.first;
        const std::vector<NetworkConnectionInfo>& connections = pair.second;

        NetworkProcessInfo processInfo = GetProcessInfo(pid);
        processInfo.connections = connections;

        networkProcesses.push_back(processInfo);
    }

    return networkProcesses;
}

// 获取进程信息
NetworkProcessInfo NetworkConnectionManager::GetProcessInfo(DWORD pid) {
    NetworkProcessInfo info;
    info.pid = pid;

    // 获取进程名称
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                if (pe32.th32ProcessID == pid) {
                    info.processName = Utils::WStringToUTF8(std::wstring(pe32.szExeFile));
                    break;
                }
            } while (Process32Next(hSnapshot, &pe32));
        }
        CloseHandle(hSnapshot);
    }

    // 获取进程路径
    info.processPath = GetProcessPath(pid);

    // 获取进程创建时间
    info.createTime = GetProcessCreateTime(pid);

    // 获取进程图标
    info.iconBase64 = GetProcessIconBase64(info.processPath);

    return info;
}

// 获取进程路径
std::string NetworkConnectionManager::GetProcessPath(DWORD pid) {
    std::string path;

    // 打开进程
    HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, pid);
    if (hProcess != nullptr) {
        WCHAR buffer[MAX_PATH];
        DWORD bufferSize = MAX_PATH;

        // 在Windows XP上，直接使用GetModuleFileNameEx
        // QueryFullProcessImageName是Vista及以上版本才有的API
        if (GetModuleFileNameExW(hProcess, nullptr, buffer, bufferSize)) {
            path = Utils::WStringToUTF8(std::wstring(buffer));
        }

        CloseHandle(hProcess);
    }

    return path;
}

// 获取进程创建时间
std::string NetworkConnectionManager::GetProcessCreateTime(DWORD pid) {
    // 打开进程
    HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, pid);
    if (hProcess == nullptr) {
        return "Unknown";
    }

    FILETIME createTime, exitTime, kernelTime, userTime;
    if (GetProcessTimes(hProcess, &createTime, &exitTime, &kernelTime, &userTime)) {
        // 转换UTC时间到本地时间
        FILETIME localFileTime;
        if (FileTimeToLocalFileTime(&createTime, &localFileTime)) {
            SYSTEMTIME systemTime;
            if (FileTimeToSystemTime(&localFileTime, &systemTime)) {
                char timeStr[64];
                sprintf_s(timeStr, sizeof(timeStr), "%04d-%02d-%02d %02d:%02d:%02d",
                    systemTime.wYear, systemTime.wMonth, systemTime.wDay,
                    systemTime.wHour, systemTime.wMinute, systemTime.wSecond);
                CloseHandle(hProcess);
                return std::string(timeStr);
            }
        }
    }

    CloseHandle(hProcess);
    return "Unknown";
}

// 获取进程图标Base64编码
std::string NetworkConnectionManager::GetProcessIconBase64(const std::string& processPath) {
    if (processPath.empty()) {
        return "";
    }

    try {
        // 使用Utils中的统一图标获取方法
        std::wstring wPath = Utils::UTF8ToWString(processPath);

        // 调用Utils中的GetFileIconAsBase64方法获取图标
        std::wstring iconBase64Wide = Utils::GetFileIconAsBase64(wPath, 32);

        if (!iconBase64Wide.empty()) {
            // 转换为UTF-8字符串返回
            return Utils::WStringToUTF8(iconBase64Wide);
        }

    } catch (...) {
        // 忽略异常，返回空字符串
    }

    return "";
}

// 获取TCP连接状态字符串
std::string NetworkConnectionManager::GetTcpStateString(DWORD state) {
    switch (state) {
        case TcpState::CLOSED: return "CLOSED";
        case TcpState::LISTEN: return "LISTEN";
        case TcpState::SYN_SENT: return "SYN_SENT";
        case TcpState::SYN_RCVD: return "SYN_RCVD";
        case TcpState::ESTABLISHED: return "ESTABLISHED";
        case TcpState::FIN_WAIT1: return "FIN_WAIT1";
        case TcpState::FIN_WAIT2: return "FIN_WAIT2";
        case TcpState::CLOSE_WAIT: return "CLOSE_WAIT";
        case TcpState::CLOSING: return "CLOSING";
        case TcpState::LAST_ACK: return "LAST_ACK";
        case TcpState::TIME_WAIT: return "TIME_WAIT";
        case TcpState::DELETE_TCB: return "DELETE_TCB";
        default: return "UNKNOWN";
    }
}

// 将网络连接信息转换为JSON格式（扁平化格式，适合网络监控软件）
json NetworkConnectionManager::NetworkConnectionsToJson(const std::vector<NetworkProcessInfo>& processes) {
    json result = json::array();

    for (const auto& process : processes) {
        // 为每个网络连接创建一个扁平化的JSON条目
        for (const auto& conn : process.connections) {
            json entry;
            entry["pid"] = process.pid;
            entry["processName"] = process.processName;
            entry["processPath"] = process.processPath;
            entry["processCreateTime"] = process.createTime;  // 进程创建时间
            entry["connectionCreateTime"] = conn.createTime;  // 连接创建时间
            entry["iconBase64"] = process.iconBase64;
            entry["protocol"] = conn.protocol;
            entry["localAddress"] = conn.localAddress;
            entry["localPort"] = conn.localPort;
            entry["remoteAddress"] = conn.remoteAddress;
            entry["remotePort"] = conn.remotePort;
            entry["state"] = conn.state;
            entry["stateString"] = (conn.protocol == "TCP") ? GetTcpStateString(conn.state) : "UDP";

            result.push_back(entry);
        }
    }

    return result;
}

// 获取所有网络连接并转换为JSON字符串
std::string NetworkConnectionManager::GetAllNetworkConnectionsAsJsonString() {
    std::vector<NetworkProcessInfo> networkProcesses = GetAllNetworkProcesses();
    json connectionsJson = NetworkConnectionsToJson(networkProcesses);
    return connectionsJson.dump(4); // 使用4个空格进行格式化缩进
}

// 获取活跃网络连接（仅包含已建立连接的TCP和所有UDP）
json NetworkConnectionManager::GetActiveNetworkConnections() {
    std::vector<NetworkProcessInfo> allNetworkProcesses = GetAllNetworkProcesses();
    json result = json::array();

    for (const auto& process : allNetworkProcesses) {
        for (const auto& conn : process.connections) {
            // 对于TCP，只包含已建立的连接；对于UDP，包含所有连接
            if (conn.protocol == "UDP" ||
                (conn.protocol == "TCP" && conn.state == TcpState::ESTABLISHED)) {

                json entry;
                entry["pid"] = process.pid;
                entry["processName"] = process.processName;
                entry["processPath"] = process.processPath;
                entry["createTime"] = process.createTime;
                entry["iconBase64"] = process.iconBase64;
                entry["protocol"] = conn.protocol;
                entry["localAddress"] = conn.localAddress;
                entry["localPort"] = conn.localPort;
                entry["remoteAddress"] = conn.remoteAddress;
                entry["remotePort"] = conn.remotePort;
                entry["state"] = conn.state;
                entry["stateString"] = (conn.protocol == "TCP") ? GetTcpStateString(conn.state) : "UDP";

                result.push_back(entry);
            }
        }
    }

    return result;
}

// 获取网络连接统计信息
json NetworkConnectionManager::GetNetworkConnectionStats() {
    std::vector<NetworkConnectionInfo> allConnections = GetAllNetworkConnections();

    json stats;
    int tcpCount = 0, udpCount = 0;
    int establishedCount = 0, listeningCount = 0;
    std::map<DWORD, int> processConnectionCount;

    for (const auto& conn : allConnections) {
        if (conn.protocol == "TCP") {
            tcpCount++;
            if (conn.state == TcpState::ESTABLISHED) {
                establishedCount++;
            } else if (conn.state == TcpState::LISTEN) {
                listeningCount++;
            }
        } else if (conn.protocol == "UDP") {
            udpCount++;
        }

        processConnectionCount[conn.owningPid]++;
    }

    stats["totalConnections"] = allConnections.size();
    stats["tcpConnections"] = tcpCount;
    stats["udpConnections"] = udpCount;
    stats["establishedConnections"] = establishedCount;
    stats["listeningConnections"] = listeningCount;
    stats["uniqueProcesses"] = processConnectionCount.size();

    return stats;
}

// 获取指定进程名称的网络连接信息
std::vector<NetworkProcessInfo> NetworkConnectionManager::GetNetworkProcessesByName(const std::string& processName) {
    std::vector<NetworkProcessInfo> matchedProcesses;
    std::vector<NetworkProcessInfo> allNetworkProcesses = GetAllNetworkProcesses();

    std::string lowerProcessName = processName;
    std::transform(lowerProcessName.begin(), lowerProcessName.end(), lowerProcessName.begin(), ::tolower);

    for (const auto& process : allNetworkProcesses) {
        std::string lowerCurrentName = process.processName;
        std::transform(lowerCurrentName.begin(), lowerCurrentName.end(), lowerCurrentName.begin(), ::tolower);

        if (lowerCurrentName.find(lowerProcessName) != std::string::npos) {
            matchedProcesses.push_back(process);
        }
    }

    return matchedProcesses;
}

// 根据端口查找进程
std::vector<NetworkProcessInfo> NetworkConnectionManager::FindProcessesByPort(DWORD port) {
    std::vector<NetworkProcessInfo> matchedProcesses;
    std::vector<NetworkProcessInfo> allNetworkProcesses = GetAllNetworkProcesses();

    for (const auto& process : allNetworkProcesses) {
        bool hasMatchingPort = false;
        for (const auto& conn : process.connections) {
            if (conn.localPort == port || conn.remotePort == port) {
                hasMatchingPort = true;
                break;
            }
        }

        if (hasMatchingPort) {
            matchedProcesses.push_back(process);
        }
    }

    return matchedProcesses;
}

// 根据远程地址查找进程
std::vector<NetworkProcessInfo> NetworkConnectionManager::FindProcessesByRemoteAddress(const std::string& remoteAddress) {
    std::vector<NetworkProcessInfo> matchedProcesses;
    std::vector<NetworkProcessInfo> allNetworkProcesses = GetAllNetworkProcesses();

    for (const auto& process : allNetworkProcesses) {
        bool hasMatchingAddress = false;
        for (const auto& conn : process.connections) {
            if (conn.remoteAddress == remoteAddress) {
                hasMatchingAddress = true;
                break;
            }
        }

        if (hasMatchingAddress) {
            matchedProcesses.push_back(process);
        }
    }

    return matchedProcesses;
}

// ========== 统一接口方法实现 ==========

// 获取所有网络连接数据（统一格式）
std::vector<NetworkConnectionData> NetworkConnectionManager::GetNetworkConnectionData() {
    std::vector<NetworkConnectionData> result;
    std::vector<NetworkProcessInfo> networkProcesses = GetAllNetworkProcesses();

    for (const auto& process : networkProcesses) {
        for (const auto& conn : process.connections) {
            NetworkConnectionData data;
            data.protocol = conn.protocol;
            data.local_address = conn.localAddress;
            data.local_port = conn.localPort;
            data.remote_address = conn.remoteAddress;
            data.remote_port = conn.remotePort;
            data.state = GetTcpStateString(static_cast<DWORD>(conn.state));
            data.owning_pid = conn.owningPid;
            data.process_name = process.processName;
            data.process_path = process.processPath;
            data.process_icon = process.iconBase64;
            data.create_time = conn.createTime;

            result.push_back(data);
        }
    }

    return result;
}

// 获取网络连接统计信息（统一格式）
NetworkConnectionStats NetworkConnectionManager::GetConnectionStats() {
    NetworkConnectionStats stats;
    std::vector<NetworkConnectionInfo> allConnections = GetAllNetworkConnections();

    std::set<DWORD> uniqueProcesses;

    for (const auto& conn : allConnections) {
        stats.total_connections++;
        uniqueProcesses.insert(conn.owningPid);

        if (conn.protocol == "TCP") {
            stats.tcp_connections++;
            if (conn.state == TcpState::ESTABLISHED) {
                stats.established_connections++;
            } else if (conn.state == TcpState::LISTEN) {
                stats.listening_connections++;
            }
        } else if (conn.protocol == "UDP") {
            stats.udp_connections++;
        }
    }

    stats.unique_processes = static_cast<int>(uniqueProcesses.size());
    return stats;
}

// 将网络连接数据转换为JSON字符串（统一接口）
std::string NetworkConnectionManager::GetNetworkConnectionDataAsJsonString() {
    std::vector<NetworkConnectionData> connections = GetNetworkConnectionData();
    json connectionsJson = connections;
    return connectionsJson.dump(4);
}

// Note: String conversion functions have been moved to Utils class
// Use Utils::WStringToUTF8() and Utils::UTF8ToWString() instead

// Get network connection creation time (estimated based on process creation time)
std::string NetworkConnectionManager::GetConnectionCreateTime(DWORD pid, const std::string& protocol,
                                                             const std::string& localAddr, DWORD localPort) {
    try {
        // Method 1: Try to get process creation time as baseline
        std::string processCreateTime = GetProcessCreateTime(pid);
        if (processCreateTime != "Unknown") {
            // For most cases, network connections are established after process startup
            // We can use process creation time as an approximation of connection time
            // Note: This is not the exact connection creation time, but a reasonable estimate
            return processCreateTime;  // Remove suffix to avoid encoding issues
        }

        // Method 2: If unable to get process creation time, use current time
        SYSTEMTIME currentTime;
        GetLocalTime(&currentTime);

        char timeStr[64];
        sprintf_s(timeStr, sizeof(timeStr), "%04d-%02d-%02d %02d:%02d:%02d",
            currentTime.wYear, currentTime.wMonth, currentTime.wDay,
            currentTime.wHour, currentTime.wMinute, currentTime.wSecond);

        return std::string(timeStr);

    } catch (...) {
        // Exception handling: return unknown time
        return "Unknown";
    }
}

